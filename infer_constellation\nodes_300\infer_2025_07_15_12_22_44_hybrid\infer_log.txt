推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 69.8345
  收益率: 0.5662
  距离: 19.5598
  内存使用: 0.2382
  能量使用: 0.5330
  推理时间: 1.3594秒

批次 2:
  奖励值: 64.4557
  收益率: 0.5500
  距离: 19.8489
  内存使用: 0.2725
  能量使用: 0.5672
  推理时间: 1.2853秒

批次 3:
  奖励值: 61.0895
  收益率: 0.5087
  距离: 16.6872
  内存使用: 0.2039
  能量使用: 0.5300
  推理时间: 1.2383秒

批次 4:
  奖励值: 73.3841
  收益率: 0.6073
  距离: 18.6796
  内存使用: 0.3216
  能量使用: 0.6306
  推理时间: 1.4411秒

批次 5:
  奖励值: 76.6444
  收益率: 0.5956
  距离: 20.6729
  内存使用: 0.3713
  能量使用: 0.6670
  推理时间: 1.4753秒

批次 6:
  奖励值: 71.4008
  收益率: 0.5825
  距离: 20.1294
  内存使用: 0.2740
  能量使用: 0.5950
  推理时间: 1.4015秒

批次 7:
  奖励值: 70.1668
  收益率: 0.5799
  距离: 21.3705
  内存使用: 0.2614
  能量使用: 0.6344
  推理时间: 1.3886秒

批次 8:
  奖励值: 68.3886
  收益率: 0.5812
  距离: 19.8225
  内存使用: 0.2766
  能量使用: 0.5237
  推理时间: 1.3347秒

批次 9:
  奖励值: 73.1203
  收益率: 0.5989
  距离: 20.6743
  内存使用: 0.2746
  能量使用: 0.6105
  推理时间: 1.3781秒

批次 10:
  奖励值: 66.4956
  收益率: 0.5611
  距离: 16.8049
  内存使用: 0.3135
  能量使用: 0.4904
  推理时间: 1.2664秒

批次 11:
  奖励值: 66.9305
  收益率: 0.5426
  距离: 19.3377
  内存使用: 0.2242
  能量使用: 0.5771
  推理时间: 1.2708秒

批次 12:
  奖励值: 70.3018
  收益率: 0.5813
  距离: 17.8597
  内存使用: 0.3084
  能量使用: 0.6283
  推理时间: 1.3274秒

批次 13:
  奖励值: 68.3907
  收益率: 0.5815
  距离: 15.7878
  内存使用: 0.2845
  能量使用: 0.5463
  推理时间: 1.3300秒

批次 14:
  奖励值: 65.3660
  收益率: 0.5361
  距离: 15.7526
  内存使用: 0.2383
  能量使用: 0.4771
  推理时间: 1.2543秒

批次 15:
  奖励值: 71.9390
  收益率: 0.5855
  距离: 19.6130
  内存使用: 0.3281
  能量使用: 0.5809
  推理时间: 1.3805秒

批次 16:
  奖励值: 67.1096
  收益率: 0.5683
  距离: 19.7326
  内存使用: 0.2523
  能量使用: 0.5960
  推理时间: 1.3445秒

批次 17:
  奖励值: 72.2628
  收益率: 0.5993
  距离: 18.1580
  内存使用: 0.3178
  能量使用: 0.5987
  推理时间: 1.4173秒

批次 18:
  奖励值: 63.3851
  收益率: 0.5492
  距离: 17.6531
  内存使用: 0.2228
  能量使用: 0.5540
  推理时间: 1.2630秒

批次 19:
  奖励值: 72.4469
  收益率: 0.5941
  距离: 18.3743
  内存使用: 0.3155
  能量使用: 0.6164
  推理时间: 1.4076秒

批次 20:
  奖励值: 55.5346
  收益率: 0.4889
  距离: 15.8641
  内存使用: 0.1939
  能量使用: 0.4728
  推理时间: 1.1524秒

批次 21:
  奖励值: 68.3455
  收益率: 0.5685
  距离: 19.2387
  内存使用: 0.2709
  能量使用: 0.5994
  推理时间: 1.4085秒

批次 22:
  奖励值: 69.4395
  收益率: 0.5903
  距离: 19.0715
  内存使用: 0.2814
  能量使用: 0.5680
  推理时间: 1.4210秒

批次 23:
  奖励值: 67.3415
  收益率: 0.5763
  距离: 17.8923
  内存使用: 0.2448
  能量使用: 0.5799
  推理时间: 1.3010秒

批次 24:
  奖励值: 73.6706
  收益率: 0.5976
  距离: 23.0989
  内存使用: 0.3312
  能量使用: 0.6288
  推理时间: 1.4408秒

批次 25:
  奖励值: 64.0951
  收益率: 0.5530
  距离: 16.9640
  内存使用: 0.2745
  能量使用: 0.5712
  推理时间: 1.3323秒

批次 26:
  奖励值: 65.6649
  收益率: 0.5449
  距离: 17.0057
  内存使用: 0.3597
  能量使用: 0.5047
  推理时间: 1.3449秒

批次 27:
  奖励值: 71.3590
  收益率: 0.5712
  距离: 19.9413
  内存使用: 0.3304
  能量使用: 0.6102
  推理时间: 1.4129秒

批次 28:
  奖励值: 68.1483
  收益率: 0.5441
  距离: 15.9212
  内存使用: 0.2434
  能量使用: 0.5389
  推理时间: 1.3333秒

批次 29:
  奖励值: 76.5210
  收益率: 0.5641
  距离: 18.4181
  内存使用: 0.3404
  能量使用: 0.5996
  推理时间: 1.4884秒

批次 30:
  奖励值: 64.1637
  收益率: 0.5257
  距离: 16.3360
  内存使用: 0.2116
  能量使用: 0.5249
  推理时间: 1.2010秒

批次 31:
  奖励值: 69.6940
  收益率: 0.5732
  距离: 17.1668
  内存使用: 0.2679
  能量使用: 0.5774
  推理时间: 1.4085秒

批次 32:
  奖励值: 80.1996
  收益率: 0.6433
  距离: 22.0779
  内存使用: 0.3942
  能量使用: 0.7286
  推理时间: 1.6777秒

批次 33:
  奖励值: 63.8861
  收益率: 0.5567
  距离: 18.6665
  内存使用: 0.2311
  能量使用: 0.5301
  推理时间: 1.2991秒

批次 34:
  奖励值: 61.6315
  收益率: 0.5438
  距离: 16.3272
  内存使用: 0.2611
  能量使用: 0.5486
  推理时间: 1.2267秒

批次 35:
  奖励值: 66.8654
  收益率: 0.5513
  距离: 17.1413
  内存使用: 0.2425
  能量使用: 0.4854
  推理时间: 1.3069秒

批次 36:
  奖励值: 70.1273
  收益率: 0.5860
  距离: 19.6013
  内存使用: 0.3181
  能量使用: 0.6260
  推理时间: 1.4737秒

批次 37:
  奖励值: 66.2491
  收益率: 0.5468
  距离: 17.4778
  内存使用: 0.2928
  能量使用: 0.5313
  推理时间: 1.2919秒

批次 38:
  奖励值: 65.1324
  收益率: 0.5350
  距离: 17.3359
  内存使用: 0.2563
  能量使用: 0.5429
  推理时间: 1.3452秒

批次 39:
  奖励值: 58.2973
  收益率: 0.5211
  距离: 18.7119
  内存使用: 0.1973
  能量使用: 0.4963
  推理时间: 1.2480秒

批次 40:
  奖励值: 75.6014
  收益率: 0.5989
  距离: 17.6340
  内存使用: 0.2360
  能量使用: 0.5272
  推理时间: 1.4794秒

批次 41:
  奖励值: 65.3233
  收益率: 0.5683
  距离: 19.1973
  内存使用: 0.2034
  能量使用: 0.5528
  推理时间: 1.3551秒

批次 42:
  奖励值: 68.1385
  收益率: 0.5568
  距离: 17.9287
  内存使用: 0.2599
  能量使用: 0.5345
  推理时间: 1.3509秒

批次 43:
  奖励值: 69.0274
  收益率: 0.5702
  距离: 19.5242
  内存使用: 0.2770
  能量使用: 0.5440
  推理时间: 1.4267秒

批次 44:
  奖励值: 70.0087
  收益率: 0.5818
  距离: 15.5220
  内存使用: 0.2683
  能量使用: 0.5924
  推理时间: 1.3351秒

批次 45:
  奖励值: 59.9392
  收益率: 0.5104
  距离: 16.6367
  内存使用: 0.2137
  能量使用: 0.4712
  推理时间: 1.2230秒

批次 46:
  奖励值: 70.2897
  收益率: 0.5793
  距离: 19.8135
  内存使用: 0.3291
  能量使用: 0.6780
  推理时间: 1.4406秒

批次 47:
  奖励值: 72.0223
  收益率: 0.5816
  距离: 19.6050
  内存使用: 0.3249
  能量使用: 0.6255
  推理时间: 1.5010秒

批次 48:
  奖励值: 70.3230
  收益率: 0.5787
  距离: 19.4999
  内存使用: 0.2867
  能量使用: 0.5675
  推理时间: 1.3878秒

批次 49:
  奖励值: 68.2699
  收益率: 0.5734
  距离: 18.8488
  内存使用: 0.2364
  能量使用: 0.5751
  推理时间: 1.4078秒

批次 50:
  奖励值: 66.0599
  收益率: 0.5526
  距离: 17.3355
  内存使用: 0.2588
  能量使用: 0.4859
  推理时间: 1.2859秒

批次 51:
  奖励值: 67.0863
  收益率: 0.5486
  距离: 15.2659
  内存使用: 0.2225
  能量使用: 0.5391
  推理时间: 1.2984秒

批次 52:
  奖励值: 69.2514
  收益率: 0.5760
  距离: 16.8409
  内存使用: 0.3161
  能量使用: 0.5402
  推理时间: 1.3633秒

批次 53:
  奖励值: 63.9303
  收益率: 0.5376
  距离: 18.6115
  内存使用: 0.2187
  能量使用: 0.5092
  推理时间: 1.2787秒

批次 54:
  奖励值: 58.3619
  收益率: 0.5194
  距离: 19.1328
  内存使用: 0.2086
  能量使用: 0.5348
  推理时间: 1.1920秒

批次 55:
  奖励值: 65.9539
  收益率: 0.5690
  距离: 20.7172
  内存使用: 0.2356
  能量使用: 0.5394
  推理时间: 1.3240秒

批次 56:
  奖励值: 67.8702
  收益率: 0.5818
  距离: 17.8376
  内存使用: 0.2876
  能量使用: 0.5345
  推理时间: 1.3469秒

批次 57:
  奖励值: 73.9870
  收益率: 0.5875
  距离: 20.8778
  内存使用: 0.3587
  能量使用: 0.5895
  推理时间: 1.4997秒

批次 58:
  奖励值: 72.6521
  收益率: 0.5855
  距离: 18.8135
  内存使用: 0.2941
  能量使用: 0.5882
  推理时间: 1.6026秒

批次 59:
  奖励值: 69.5316
  收益率: 0.5879
  距离: 21.7537
  内存使用: 0.3594
  能量使用: 0.6236
  推理时间: 1.5717秒

批次 60:
  奖励值: 65.5954
  收益率: 0.5435
  距离: 16.8047
  内存使用: 0.2714
  能量使用: 0.5333
  推理时间: 1.3275秒

批次 61:
  奖励值: 65.8013
  收益率: 0.5798
  距离: 19.2836
  内存使用: 0.2793
  能量使用: 0.6346
  推理时间: 1.3485秒

批次 62:
  奖励值: 62.5153
  收益率: 0.5383
  距离: 20.1484
  内存使用: 0.2574
  能量使用: 0.5637
  推理时间: 1.2563秒

批次 63:
  奖励值: 62.1306
  收益率: 0.5288
  距离: 16.2343
  内存使用: 0.5525
  能量使用: 0.5520
  推理时间: 1.2766秒

批次 64:
  奖励值: 66.1262
  收益率: 0.5568
  距离: 18.8851
  内存使用: 0.2856
  能量使用: 0.5768
  推理时间: 1.3787秒

批次 65:
  奖励值: 67.4068
  收益率: 0.5473
  距离: 18.1974
  内存使用: 0.2645
  能量使用: 0.5246
  推理时间: 1.3354秒

批次 66:
  奖励值: 70.3128
  收益率: 0.5716
  距离: 21.2899
  内存使用: 0.2917
  能量使用: 0.5929
  推理时间: 1.4938秒

批次 67:
  奖励值: 63.6932
  收益率: 0.5546
  距离: 19.1867
  内存使用: 0.2875
  能量使用: 0.5394
  推理时间: 1.2932秒

批次 68:
  奖励值: 69.3745
  收益率: 0.5520
  距离: 17.5906
  内存使用: 0.2916
  能量使用: 0.5687
  推理时间: 1.3849秒

批次 69:
  奖励值: 71.2873
  收益率: 0.5796
  距离: 18.0874
  内存使用: 0.3612
  能量使用: 0.5767
  推理时间: 1.4889秒

批次 70:
  奖励值: 69.0784
  收益率: 0.5730
  距离: 15.5840
  内存使用: 0.2935
  能量使用: 0.5658
  推理时间: 1.4113秒

批次 71:
  奖励值: 70.4515
  收益率: 0.5827
  距离: 17.4130
  内存使用: 0.2933
  能量使用: 0.5906
  推理时间: 1.3377秒

批次 72:
  奖励值: 68.5564
  收益率: 0.5609
  距离: 16.8612
  内存使用: 0.2888
  能量使用: 0.5373
  推理时间: 1.3169秒

批次 73:
  奖励值: 67.6009
  收益率: 0.5567
  距离: 16.2015
  内存使用: 0.2402
  能量使用: 0.5567
  推理时间: 1.2657秒

批次 74:
  奖励值: 71.6435
  收益率: 0.5955
  距离: 19.2139
  内存使用: 0.2937
  能量使用: 0.6054
  推理时间: 1.4184秒

批次 75:
  奖励值: 58.0358
  收益率: 0.5127
  距离: 16.2515
  内存使用: 0.1910
  能量使用: 0.4860
  推理时间: 1.1728秒

批次 76:
  奖励值: 69.2690
  收益率: 0.5698
  距离: 19.0057
  内存使用: 0.2841
  能量使用: 0.5723
  推理时间: 1.3602秒

批次 77:
  奖励值: 72.0197
  收益率: 0.5951
  距离: 19.0460
  内存使用: 0.3017
  能量使用: 0.6129
  推理时间: 1.3466秒

批次 78:
  奖励值: 69.8728
  收益率: 0.5653
  距离: 17.8086
  内存使用: 0.2985
  能量使用: 0.5474
  推理时间: 1.3391秒

批次 79:
  奖励值: 69.8959
  收益率: 0.5891
  距离: 17.3199
  内存使用: 0.3057
  能量使用: 0.5827
  推理时间: 1.3925秒

批次 80:
  奖励值: 72.7769
  收益率: 0.5819
  距离: 18.3539
  内存使用: 0.3076
  能量使用: 0.5847
  推理时间: 1.4376秒

批次 81:
  奖励值: 64.2008
  收益率: 0.5495
  距离: 15.0374
  内存使用: 0.2075
  能量使用: 0.5536
  推理时间: 1.2179秒

批次 82:
  奖励值: 67.7255
  收益率: 0.5336
  距离: 16.1715
  内存使用: 0.2390
  能量使用: 0.5386
  推理时间: 1.2767秒

批次 83:
  奖励值: 72.9603
  收益率: 0.5799
  距离: 18.6516
  内存使用: 0.2959
  能量使用: 0.5319
  推理时间: 1.3939秒

批次 84:
  奖励值: 71.3190
  收益率: 0.5984
  距离: 19.5106
  内存使用: 0.3135
  能量使用: 0.5380
  推理时间: 1.4119秒

批次 85:
  奖励值: 62.0401
  收益率: 0.5250
  距离: 17.7998
  内存使用: 0.2385
  能量使用: 0.4813
  推理时间: 1.2224秒

批次 86:
  奖励值: 66.4653
  收益率: 0.5625
  距离: 16.6198
  内存使用: 0.2751
  能量使用: 0.5746
  推理时间: 1.3052秒

批次 87:
  奖励值: 70.9331
  收益率: 0.5846
  距离: 19.6814
  内存使用: 0.3198
  能量使用: 0.5326
  推理时间: 1.3613秒

批次 88:
  奖励值: 66.2865
  收益率: 0.5449
  距离: 18.4245
  内存使用: 0.3078
  能量使用: 0.4947
  推理时间: 1.2841秒

批次 89:
  奖励值: 68.2621
  收益率: 0.5647
  距离: 18.8526
  内存使用: 0.2617
  能量使用: 0.5616
  推理时间: 1.2902秒

批次 90:
  奖励值: 70.6918
  收益率: 0.5698
  距离: 19.3719
  内存使用: 0.3062
  能量使用: 0.5384
  推理时间: 1.3361秒

批次 91:
  奖励值: 68.4827
  收益率: 0.5675
  距离: 17.8271
  内存使用: 0.2576
  能量使用: 0.5500
  推理时间: 1.2881秒

批次 92:
  奖励值: 66.5380
  收益率: 0.5679
  距离: 19.9023
  内存使用: 0.2557
  能量使用: 0.5659
  推理时间: 1.3259秒

批次 93:
  奖励值: 68.9699
  收益率: 0.5828
  距离: 20.2062
  内存使用: 0.3037
  能量使用: 0.5503
  推理时间: 1.3285秒

批次 94:
  奖励值: 70.6474
  收益率: 0.5838
  距离: 18.8772
  内存使用: 0.2895
  能量使用: 0.5385
  推理时间: 1.3695秒

批次 95:
  奖励值: 61.9376
  收益率: 0.5447
  距离: 16.6811
  内存使用: 0.2081
  能量使用: 0.6356
  推理时间: 1.2800秒

批次 96:
  奖励值: 74.4796
  收益率: 0.6013
  距离: 18.1288
  内存使用: 0.2998
  能量使用: 0.6121
  推理时间: 1.4335秒

批次 97:
  奖励值: 61.1362
  收益率: 0.5146
  距离: 18.2576
  内存使用: 0.1822
  能量使用: 0.4879
  推理时间: 1.1751秒

批次 98:
  奖励值: 64.5291
  收益率: 0.5375
  距离: 17.7951
  内存使用: 0.2577
  能量使用: 0.4890
  推理时间: 1.2717秒

批次 99:
  奖励值: 69.9467
  收益率: 0.5939
  距离: 19.8979
  内存使用: 0.2357
  能量使用: 0.5731
  推理时间: 1.3642秒

批次 100:
  奖励值: 64.4594
  收益率: 0.5345
  距离: 15.9516
  内存使用: 0.2099
  能量使用: 0.5198
  推理时间: 1.2078秒


==================== 总结 ====================
平均收益率: 0.5647
平均能量使用: 0.5615
平均推理时间: 1.3488秒
