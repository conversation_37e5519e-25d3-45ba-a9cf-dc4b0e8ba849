推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 28.0849
  收益率: 0.7095
  距离: 7.6404
  内存使用: -0.0362
  能量使用: 0.2260
  推理时间: 0.8704秒

批次 2:
  奖励值: 28.7623
  收益率: 0.7004
  距离: 7.3270
  内存使用: -0.0557
  能量使用: 0.2041
  推理时间: 0.5171秒

批次 3:
  奖励值: 26.7948
  收益率: 0.6645
  距离: 7.2085
  内存使用: -0.0337
  能量使用: 0.2355
  推理时间: 0.5271秒

批次 4:
  奖励值: 28.4973
  收益率: 0.6991
  距离: 7.3625
  内存使用: -0.0363
  能量使用: 0.2792
  推理时间: 0.5404秒

批次 5:
  奖励值: 27.5537
  收益率: 0.7023
  距离: 6.5535
  内存使用: -0.0835
  能量使用: 0.2226
  推理时间: 0.5015秒

批次 6:
  奖励值: 28.2953
  收益率: 0.6701
  距离: 9.0207
  内存使用: -0.0487
  能量使用: 0.2374
  推理时间: 0.5535秒

批次 7:
  奖励值: 28.6314
  收益率: 0.7372
  距离: 8.6612
  内存使用: -0.0312
  能量使用: 0.3070
  推理时间: 0.5538秒

批次 8:
  奖励值: 29.1031
  收益率: 0.7204
  距离: 8.5923
  内存使用: -0.0711
  能量使用: 0.2725
  推理时间: 0.5552秒

批次 9:
  奖励值: 29.2318
  收益率: 0.7290
  距离: 9.1358
  内存使用: -0.0688
  能量使用: 0.2693
  推理时间: 0.6271秒

批次 10:
  奖励值: 28.6331
  收益率: 0.7101
  距离: 9.7750
  内存使用: 0.2724
  能量使用: 0.2865
  推理时间: 0.5775秒

批次 11:
  奖励值: 32.0159
  收益率: 0.8057
  距离: 8.7192
  内存使用: -0.0540
  能量使用: 0.3030
  推理时间: 0.6476秒

批次 12:
  奖励值: 32.9234
  收益率: 0.7636
  距离: 6.9298
  内存使用: -0.0367
  能量使用: 0.2602
  推理时间: 0.6926秒

批次 13:
  奖励值: 30.9703
  收益率: 0.7314
  距离: 8.6436
  内存使用: -0.0335
  能量使用: 0.2493
  推理时间: 0.6329秒

批次 14:
  奖励值: 29.2632
  收益率: 0.7428
  距离: 8.2220
  内存使用: -0.0374
  能量使用: 0.2891
  推理时间: 0.5971秒

批次 15:
  奖励值: 28.2219
  收益率: 0.6876
  距离: 8.9362
  内存使用: -0.0387
  能量使用: 0.2586
  推理时间: 0.6545秒

批次 16:
  奖励值: 31.6527
  收益率: 0.7427
  距离: 8.0786
  内存使用: -0.0305
  能量使用: 0.2465
  推理时间: 0.6841秒

批次 17:
  奖励值: 29.7803
  收益率: 0.7339
  距离: 8.5515
  内存使用: -0.0227
  能量使用: 0.3107
  推理时间: 0.6540秒

批次 18:
  奖励值: 27.1417
  收益率: 0.7031
  距离: 8.4874
  内存使用: -0.0343
  能量使用: 0.2635
  推理时间: 0.5699秒

批次 19:
  奖励值: 31.2948
  收益率: 0.7598
  距离: 10.4891
  内存使用: -0.0094
  能量使用: 0.3308
  推理时间: 0.6449秒

批次 20:
  奖励值: 28.8384
  收益率: 0.7366
  距离: 8.1136
  内存使用: -0.0330
  能量使用: 0.2426
  推理时间: 0.5628秒

批次 21:
  奖励值: 31.4540
  收益率: 0.7702
  距离: 9.2386
  内存使用: -0.0404
  能量使用: 0.2938
  推理时间: 0.6055秒

批次 22:
  奖励值: 29.6611
  收益率: 0.7197
  距离: 8.2011
  内存使用: 0.0227
  能量使用: 0.2539
  推理时间: 0.5734秒

批次 23:
  奖励值: 29.1792
  收益率: 0.7030
  距离: 7.1960
  内存使用: -0.0198
  能量使用: 0.2654
  推理时间: 0.5268秒

批次 24:
  奖励值: 25.9131
  收益率: 0.6751
  距离: 7.0025
  内存使用: -0.0707
  能量使用: 0.2420
  推理时间: 0.5115秒

批次 25:
  奖励值: 29.6320
  收益率: 0.7010
  距离: 8.5685
  内存使用: -0.0830
  能量使用: 0.2506
  推理时间: 0.6001秒

批次 26:
  奖励值: 27.8032
  收益率: 0.6797
  距离: 6.9808
  内存使用: -0.0471
  能量使用: 0.2599
  推理时间: 0.5901秒

批次 27:
  奖励值: 29.5345
  收益率: 0.7336
  距离: 12.2647
  内存使用: -0.0102
  能量使用: 0.2765
  推理时间: 0.6749秒

批次 28:
  奖励值: 29.6450
  收益率: 0.7322
  距离: 8.2605
  内存使用: 0.0183
  能量使用: 0.2312
  推理时间: 0.6768秒

批次 29:
  奖励值: 30.2553
  收益率: 0.7617
  距离: 7.7196
  内存使用: -0.0759
  能量使用: 0.2457
  推理时间: 0.6190秒

批次 30:
  奖励值: 28.8921
  收益率: 0.7165
  距离: 8.1667
  内存使用: -0.0372
  能量使用: 0.2863
  推理时间: 0.6442秒

批次 31:
  奖励值: 32.6319
  收益率: 0.7490
  距离: 9.6812
  内存使用: -0.0192
  能量使用: 0.2742
  推理时间: 0.7005秒

批次 32:
  奖励值: 29.4048
  收益率: 0.7228
  距离: 8.7401
  内存使用: -0.0233
  能量使用: 0.2736
  推理时间: 0.6123秒

批次 33:
  奖励值: 30.7579
  收益率: 0.7735
  距离: 10.6446
  内存使用: -0.0423
  能量使用: 0.2820
  推理时间: 0.6819秒

批次 34:
  奖励值: 29.7179
  收益率: 0.7441
  距离: 9.5708
  内存使用: -0.0483
  能量使用: 0.2418
  推理时间: 0.6580秒

批次 35:
  奖励值: 29.7819
  收益率: 0.7309
  距离: 9.2048
  内存使用: -0.0334
  能量使用: 0.2648
  推理时间: 0.6483秒

批次 36:
  奖励值: 34.3051
  收益率: 0.7824
  距离: 9.5558
  内存使用: -0.0637
  能量使用: 0.2978
  推理时间: 0.6336秒

批次 37:
  奖励值: 25.7191
  收益率: 0.6932
  距离: 5.8366
  内存使用: -0.1010
  能量使用: 0.2263
  推理时间: 0.5544秒

批次 38:
  奖励值: 27.7514
  收益率: 0.7304
  距离: 7.7644
  内存使用: -0.0945
  能量使用: 0.2274
  推理时间: 0.5841秒

批次 39:
  奖励值: 29.9640
  收益率: 0.7372
  距离: 8.8595
  内存使用: -0.0429
  能量使用: 0.2553
  推理时间: 0.5993秒

批次 40:
  奖励值: 28.3756
  收益率: 0.7222
  距离: 8.0350
  内存使用: -0.0631
  能量使用: 0.2770
  推理时间: 0.6031秒

批次 41:
  奖励值: 27.1888
  收益率: 0.7036
  距离: 7.6587
  内存使用: -0.0718
  能量使用: 0.2354
  推理时间: 0.5607秒

批次 42:
  奖励值: 31.7935
  收益率: 0.7371
  距离: 7.3999
  内存使用: -0.0391
  能量使用: 0.2521
  推理时间: 0.6525秒

批次 43:
  奖励值: 29.4157
  收益率: 0.7186
  距离: 7.3521
  内存使用: -0.0156
  能量使用: 0.2322
  推理时间: 0.6403秒

批次 44:
  奖励值: 30.0889
  收益率: 0.7238
  距离: 8.7840
  内存使用: -0.0490
  能量使用: 0.2873
  推理时间: 0.6568秒

批次 45:
  奖励值: 29.5533
  收益率: 0.7273
  距离: 7.9230
  内存使用: -0.0538
  能量使用: 0.2242
  推理时间: 0.5814秒

批次 46:
  奖励值: 31.1229
  收益率: 0.7727
  距离: 8.9320
  内存使用: -0.0609
  能量使用: 0.2830
  推理时间: 0.6526秒

批次 47:
  奖励值: 28.2774
  收益率: 0.7140
  距离: 7.6836
  内存使用: -0.0826
  能量使用: 0.2441
  推理时间: 0.5565秒

批次 48:
  奖励值: 31.0936
  收益率: 0.7532
  距离: 8.0565
  内存使用: 0.2605
  能量使用: 0.2958
  推理时间: 0.6770秒

批次 49:
  奖励值: 33.1942
  收益率: 0.7500
  距离: 7.2478
  内存使用: -0.0663
  能量使用: 0.2302
  推理时间: 0.6172秒

批次 50:
  奖励值: 29.5971
  收益率: 0.7098
  距离: 8.6140
  内存使用: -0.0606
  能量使用: 0.2524
  推理时间: 0.5964秒

批次 51:
  奖励值: 26.9339
  收益率: 0.6587
  距离: 6.5759
  内存使用: -0.0605
  能量使用: 0.2167
  推理时间: 0.5469秒

批次 52:
  奖励值: 28.7090
  收益率: 0.7056
  距离: 7.5733
  内存使用: -0.0201
  能量使用: 0.2289
  推理时间: 0.6015秒

批次 53:
  奖励值: 32.3241
  收益率: 0.7704
  距离: 8.8645
  内存使用: 0.0023
  能量使用: 0.2850
  推理时间: 0.6780秒

批次 54:
  奖励值: 29.1672
  收益率: 0.7227
  距离: 7.6327
  内存使用: -0.0273
  能量使用: 0.2603
  推理时间: 0.6153秒

批次 55:
  奖励值: 31.6639
  收益率: 0.7700
  距离: 9.4141
  内存使用: -0.0150
  能量使用: 0.2732
  推理时间: 0.6462秒

批次 56:
  奖励值: 28.1651
  收益率: 0.7095
  距离: 7.5032
  内存使用: -0.0577
  能量使用: 0.2243
  推理时间: 0.5632秒

批次 57:
  奖励值: 26.5401
  收益率: 0.6918
  距离: 9.1242
  内存使用: -0.0503
  能量使用: 0.2459
  推理时间: 0.5610秒

批次 58:
  奖励值: 29.9405
  收益率: 0.7840
  距离: 10.2513
  内存使用: -0.0461
  能量使用: 0.3138
  推理时间: 0.6630秒

批次 59:
  奖励值: 33.6016
  收益率: 0.7589
  距离: 9.3740
  内存使用: -0.0285
  能量使用: 0.2513
  推理时间: 0.6432秒

批次 60:
  奖励值: 27.2265
  收益率: 0.6760
  距离: 7.9417
  内存使用: -0.0346
  能量使用: 0.2400
  推理时间: 0.5230秒

批次 61:
  奖励值: 28.5861
  收益率: 0.7227
  距离: 8.7982
  内存使用: -0.0361
  能量使用: 0.2658
  推理时间: 0.5493秒

批次 62:
  奖励值: 24.8631
  收益率: 0.6573
  距离: 6.5837
  内存使用: -0.1089
  能量使用: 0.1990
  推理时间: 0.4555秒

批次 63:
  奖励值: 28.8823
  收益率: 0.7624
  距离: 9.3691
  内存使用: -0.0162
  能量使用: 0.2825
  推理时间: 0.5815秒

批次 64:
  奖励值: 25.8097
  收益率: 0.7002
  距离: 7.9624
  内存使用: -0.0400
  能量使用: 0.2581
  推理时间: 0.5268秒

批次 65:
  奖励值: 33.7702
  收益率: 0.7914
  距离: 9.6597
  内存使用: -0.0440
  能量使用: 0.2439
  推理时间: 0.6569秒

批次 66:
  奖励值: 29.8498
  收益率: 0.7342
  距离: 9.6580
  内存使用: -0.0258
  能量使用: 0.2682
  推理时间: 0.5829秒

批次 67:
  奖励值: 30.4871
  收益率: 0.7398
  距离: 7.5748
  内存使用: -0.0292
  能量使用: 0.2802
  推理时间: 0.5749秒

批次 68:
  奖励值: 28.3540
  收益率: 0.7187
  距离: 7.5138
  内存使用: -0.0640
  能量使用: 0.2422
  推理时间: 0.5255秒

批次 69:
  奖励值: 28.0938
  收益率: 0.6996
  距离: 7.4417
  内存使用: -0.0652
  能量使用: 0.2359
  推理时间: 0.5193秒

批次 70:
  奖励值: 31.3464
  收益率: 0.7305
  距离: 8.1100
  内存使用: -0.0540
  能量使用: 0.2513
  推理时间: 0.5654秒

批次 71:
  奖励值: 30.2721
  收益率: 0.7339
  距离: 7.6693
  内存使用: -0.0677
  能量使用: 0.2542
  推理时间: 0.5382秒

批次 72:
  奖励值: 28.0524
  收益率: 0.7070
  距离: 7.9046
  内存使用: -0.0583
  能量使用: 0.2488
  推理时间: 0.5487秒

批次 73:
  奖励值: 34.3087
  收益率: 0.7654
  距离: 8.0539
  内存使用: 0.0004
  能量使用: 0.3284
  推理时间: 0.6873秒

批次 74:
  奖励值: 31.5611
  收益率: 0.7377
  距离: 8.6639
  内存使用: -0.0621
  能量使用: 0.2759
  推理时间: 0.5973秒

批次 75:
  奖励值: 26.4099
  收益率: 0.6477
  距离: 6.2350
  内存使用: -0.0780
  能量使用: 0.2231
  推理时间: 0.5353秒

批次 76:
  奖励值: 25.5189
  收益率: 0.6813
  距离: 7.8073
  内存使用: -0.0850
  能量使用: 0.2399
  推理时间: 0.5275秒

批次 77:
  奖励值: 32.3258
  收益率: 0.7403
  距离: 7.5296
  内存使用: -0.0352
  能量使用: 0.2539
  推理时间: 0.6276秒

批次 78:
  奖励值: 28.2849
  收益率: 0.7004
  距离: 6.9029
  内存使用: -0.0968
  能量使用: 0.2241
  推理时间: 0.5616秒

批次 79:
  奖励值: 26.3898
  收益率: 0.6909
  距离: 6.0715
  内存使用: -0.0641
  能量使用: 0.2129
  推理时间: 0.5427秒

批次 80:
  奖励值: 28.5165
  收益率: 0.7289
  距离: 8.3622
  内存使用: -0.0401
  能量使用: 0.2449
  推理时间: 0.6071秒

批次 81:
  奖励值: 21.7827
  收益率: 0.6484
  距离: 8.3223
  内存使用: -0.0931
  能量使用: 0.2054
  推理时间: 0.5188秒

批次 82:
  奖励值: 32.2950
  收益率: 0.7470
  距离: 8.8945
  内存使用: 0.0157
  能量使用: 0.2999
  推理时间: 0.6970秒

批次 83:
  奖励值: 33.1742
  收益率: 0.7535
  距离: 7.9503
  内存使用: -0.0146
  能量使用: 0.3159
  推理时间: 0.6158秒

批次 84:
  奖励值: 29.5943
  收益率: 0.6967
  距离: 7.2508
  内存使用: -0.0557
  能量使用: 0.2162
  推理时间: 0.5612秒

批次 85:
  奖励值: 28.6809
  收益率: 0.6862
  距离: 8.0738
  内存使用: -0.0825
  能量使用: 0.2470
  推理时间: 0.5517秒

批次 86:
  奖励值: 29.8296
  收益率: 0.7544
  距离: 8.2934
  内存使用: -0.0268
  能量使用: 0.2742
  推理时间: 0.6746秒

批次 87:
  奖励值: 28.2201
  收益率: 0.7118
  距离: 7.5332
  内存使用: -0.0576
  能量使用: 0.2841
  推理时间: 0.5752秒

批次 88:
  奖励值: 31.9187
  收益率: 0.7427
  距离: 6.9818
  内存使用: -0.0428
  能量使用: 0.2235
  推理时间: 0.6394秒

批次 89:
  奖励值: 27.7562
  收益率: 0.7254
  距离: 9.3296
  内存使用: -0.0446
  能量使用: 0.2027
  推理时间: 0.6115秒

批次 90:
  奖励值: 27.5161
  收益率: 0.7408
  距离: 9.3430
  内存使用: -0.0418
  能量使用: 0.2665
  推理时间: 0.6112秒

批次 91:
  奖励值: 28.4263
  收益率: 0.6970
  距离: 7.3755
  内存使用: -0.0690
  能量使用: 0.2410
  推理时间: 0.5978秒

批次 92:
  奖励值: 30.3381
  收益率: 0.7023
  距离: 7.5190
  内存使用: -0.0405
  能量使用: 0.2454
  推理时间: 0.5612秒

批次 93:
  奖励值: 30.2633
  收益率: 0.6866
  距离: 8.0614
  内存使用: -0.0444
  能量使用: 0.2564
  推理时间: 0.5810秒

批次 94:
  奖励值: 26.7087
  收益率: 0.6749
  距离: 6.5951
  内存使用: -0.0638
  能量使用: 0.2513
  推理时间: 0.5547秒

批次 95:
  奖励值: 30.9313
  收益率: 0.7280
  距离: 9.1235
  内存使用: -0.0283
  能量使用: 0.2422
  推理时间: 0.6409秒

批次 96:
  奖励值: 28.3095
  收益率: 0.6918
  距离: 7.4676
  内存使用: -0.0877
  能量使用: 0.2011
  推理时间: 0.5523秒

批次 97:
  奖励值: 26.3749
  收益率: 0.6652
  距离: 7.8509
  内存使用: -0.0508
  能量使用: 0.2502
  推理时间: 0.5706秒

批次 98:
  奖励值: 31.2694
  收益率: 0.7160
  距离: 8.8449
  内存使用: -0.0657
  能量使用: 0.2820
  推理时间: 0.5920秒

批次 99:
  奖励值: 26.8693
  收益率: 0.7106
  距离: 6.4829
  内存使用: -0.0832
  能量使用: 0.2619
  推理时间: 0.5751秒

批次 100:
  奖励值: 32.1702
  收益率: 0.7561
  距离: 9.9008
  内存使用: -0.0766
  能量使用: 0.2353
  推理时间: 0.6418秒


==================== 总结 ====================
平均收益率: 0.7221
平均能量使用: 0.2563
平均推理时间: 0.5993秒
