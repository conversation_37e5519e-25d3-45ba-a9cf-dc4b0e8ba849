推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 87.2504
  收益率: 0.4201
  距离: 20.6564
  内存使用: 0.3670
  能量使用: 0.6759
  推理时间: 1.7981秒

批次 2:
  奖励值: 84.8171
  收益率: 0.4160
  距离: 21.8915
  内存使用: 0.4302
  能量使用: 0.6442
  推理时间: 1.7885秒

批次 3:
  奖励值: 89.5191
  收益率: 0.4452
  距离: 22.8500
  内存使用: 0.4230
  能量使用: 0.6889
  推理时间: 1.7359秒

批次 4:
  奖励值: 80.5134
  收益率: 0.3989
  距离: 19.9201
  内存使用: 0.4114
  能量使用: 0.6416
  推理时间: 1.6666秒

批次 5:
  奖励值: 84.9308
  收益率: 0.4263
  距离: 19.8472
  内存使用: 0.3801
  能量使用: 0.7109
  推理时间: 1.6855秒

批次 6:
  奖励值: 82.1132
  收益率: 0.4290
  距离: 24.9153
  内存使用: 0.4040
  能量使用: 0.6543
  推理时间: 1.7101秒

批次 7:
  奖励值: 85.9053
  收益率: 0.4368
  距离: 23.2961
  内存使用: 0.3942
  能量使用: 0.6990
  推理时间: 1.7338秒

批次 8:
  奖励值: 90.1479
  收益率: 0.4653
  距离: 24.7065
  内存使用: 0.4592
  能量使用: 0.7384
  推理时间: 1.9488秒

批次 9:
  奖励值: 81.2372
  收益率: 0.4246
  距离: 21.2771
  内存使用: 0.3705
  能量使用: 0.6780
  推理时间: 1.6597秒

批次 10:
  奖励值: 84.9936
  收益率: 0.4220
  距离: 23.7000
  内存使用: 0.4281
  能量使用: 0.6847
  推理时间: 1.8211秒

批次 11:
  奖励值: 89.0134
  收益率: 0.4341
  距离: 23.0327
  内存使用: 0.4528
  能量使用: 0.6877
  推理时间: 1.7805秒

批次 12:
  奖励值: 87.5415
  收益率: 0.4355
  距离: 22.3028
  内存使用: 0.4716
  能量使用: 0.7426
  推理时间: 2.0303秒

批次 13:
  奖励值: 78.1826
  收益率: 0.4126
  距离: 23.5056
  内存使用: 0.3268
  能量使用: 0.6024
  推理时间: 1.7409秒

批次 14:
  奖励值: 83.8628
  收益率: 0.4172
  距离: 22.3098
  内存使用: 0.3753
  能量使用: 0.5894
  推理时间: 1.6689秒

批次 15:
  奖励值: 83.4372
  收益率: 0.4204
  距离: 23.9473
  内存使用: 0.3112
  能量使用: 0.6671
  推理时间: 1.6910秒

批次 16:
  奖励值: 84.8691
  收益率: 0.4323
  距离: 23.1316
  内存使用: 0.4007
  能量使用: 0.7295
  推理时间: 1.6736秒

批次 17:
  奖励值: 81.0141
  收益率: 0.4020
  距离: 20.8300
  内存使用: 0.3312
  能量使用: 0.6106
  推理时间: 1.6439秒

批次 18:
  奖励值: 74.1825
  收益率: 0.3893
  距离: 20.4873
  内存使用: 0.3264
  能量使用: 0.6213
  推理时间: 1.4907秒

批次 19:
  奖励值: 77.6996
  收益率: 0.3820
  距离: 21.4520
  内存使用: 0.3777
  能量使用: 0.5710
  推理时间: 1.5850秒

批次 20:
  奖励值: 91.7921
  收益率: 0.4610
  距离: 23.2461
  内存使用: 0.4689
  能量使用: 0.7007
  推理时间: 2.0784秒

批次 21:
  奖励值: 93.6736
  收益率: 0.4653
  距离: 22.2218
  内存使用: 0.4436
  能量使用: 0.7875
  推理时间: 1.9042秒

批次 22:
  奖励值: 85.6331
  收益率: 0.4427
  距离: 23.0361
  内存使用: 0.4194
  能量使用: 0.6782
  推理时间: 1.9648秒

批次 23:
  奖励值: 94.0459
  收益率: 0.4673
  距离: 25.0569
  内存使用: 0.4746
  能量使用: 0.7767
  推理时间: 2.1979秒

批次 24:
  奖励值: 85.4899
  收益率: 0.4450
  距离: 21.8754
  内存使用: 0.3808
  能量使用: 0.7640
  推理时间: 1.6869秒

批次 25:
  奖励值: 82.0692
  收益率: 0.4047
  距离: 20.7971
  内存使用: 0.4095
  能量使用: 0.6550
  推理时间: 1.6580秒

批次 26:
  奖励值: 86.8483
  收益率: 0.4350
  距离: 23.7672
  内存使用: 0.4469
  能量使用: 0.6893
  推理时间: 1.7272秒

批次 27:
  奖励值: 82.6095
  收益率: 0.4216
  距离: 24.3960
  内存使用: 0.4493
  能量使用: 0.7357
  推理时间: 1.6511秒

批次 28:
  奖励值: 76.5079
  收益率: 0.3809
  距离: 19.0337
  内存使用: 0.3093
  能量使用: 0.6412
  推理时间: 1.3857秒

批次 29:
  奖励值: 85.5317
  收益率: 0.4285
  距离: 21.5228
  内存使用: 0.3415
  能量使用: 0.6722
  推理时间: 1.5743秒

批次 30:
  奖励值: 83.3341
  收益率: 0.4221
  距离: 22.8944
  内存使用: 0.3570
  能量使用: 0.6803
  推理时间: 1.5534秒

批次 31:
  奖励值: 86.7557
  收益率: 0.4311
  距离: 21.1868
  内存使用: 0.3971
  能量使用: 0.7047
  推理时间: 1.6726秒

批次 32:
  奖励值: 83.7726
  收益率: 0.4171
  距离: 20.6604
  内存使用: 0.3491
  能量使用: 0.6454
  推理时间: 1.5458秒

批次 33:
  奖励值: 88.0480
  收益率: 0.4389
  距离: 23.2747
  内存使用: 0.3445
  能量使用: 0.6849
  推理时间: 1.7083秒

批次 34:
  奖励值: 89.1145
  收益率: 0.4522
  距离: 24.2170
  内存使用: 0.4689
  能量使用: 0.6852
  推理时间: 1.7677秒

批次 35:
  奖励值: 81.8558
  收益率: 0.4228
  距离: 22.0509
  内存使用: 0.3502
  能量使用: 0.6874
  推理时间: 1.6768秒

批次 36:
  奖励值: 80.8473
  收益率: 0.4034
  距离: 22.6794
  内存使用: 0.3315
  能量使用: 0.6945
  推理时间: 1.5892秒

批次 37:
  奖励值: 83.3487
  收益率: 0.4126
  距离: 18.7834
  内存使用: 0.4288
  能量使用: 0.6904
  推理时间: 1.6811秒

批次 38:
  奖励值: 82.7776
  收益率: 0.4186
  距离: 24.1474
  内存使用: 0.4164
  能量使用: 0.6811
  推理时间: 1.6859秒

批次 39:
  奖励值: 88.8588
  收益率: 0.4453
  距离: 26.1647
  内存使用: 0.4213
  能量使用: 0.6966
  推理时间: 1.7409秒

批次 40:
  奖励值: 82.2463
  收益率: 0.4360
  距离: 22.4631
  内存使用: 0.3676
  能量使用: 0.6767
  推理时间: 1.6499秒

批次 41:
  奖励值: 86.0045
  收益率: 0.4397
  距离: 25.8866
  内存使用: 0.4138
  能量使用: 0.6905
  推理时间: 1.7531秒

批次 42:
  奖励值: 89.8428
  收益率: 0.4452
  距离: 23.1728
  内存使用: 0.4129
  能量使用: 0.7286
  推理时间: 1.7758秒

批次 43:
  奖励值: 95.0134
  收益率: 0.4731
  距离: 26.9307
  内存使用: 0.4766
  能量使用: 0.7661
  推理时间: 1.9094秒

批次 44:
  奖励值: 93.7055
  收益率: 0.4745
  距离: 24.9951
  内存使用: 0.4760
  能量使用: 0.7180
  推理时间: 1.8357秒

批次 45:
  奖励值: 89.1801
  收益率: 0.4283
  距离: 24.2310
  内存使用: 0.4694
  能量使用: 0.7395
  推理时间: 1.8878秒

批次 46:
  奖励值: 78.9611
  收益率: 0.3931
  距离: 20.1582
  内存使用: 0.2704
  能量使用: 0.6493
  推理时间: 1.9779秒

批次 47:
  奖励值: 84.6452
  收益率: 0.4350
  距离: 23.0502
  内存使用: 0.3745
  能量使用: 0.6849
  推理时间: 1.8369秒

批次 48:
  奖励值: 88.3010
  收益率: 0.4472
  距离: 24.9196
  内存使用: 0.3817
  能量使用: 0.6968
  推理时间: 1.9343秒

批次 49:
  奖励值: 83.2139
  收益率: 0.4143
  距离: 23.5695
  内存使用: 0.3982
  能量使用: 0.6046
  推理时间: 1.8305秒

批次 50:
  奖励值: 80.0000
  收益率: 0.3885
  距离: 19.2558
  内存使用: 0.3242
  能量使用: 0.6200
  推理时间: 1.6004秒

批次 51:
  奖励值: 85.1265
  收益率: 0.4349
  距离: 21.9334
  内存使用: 0.3816
  能量使用: 0.6320
  推理时间: 1.8391秒

批次 52:
  奖励值: 77.9696
  收益率: 0.3989
  距离: 21.8210
  内存使用: 0.3613
  能量使用: 0.6786
  推理时间: 1.6766秒

批次 53:
  奖励值: 79.3832
  收益率: 0.4106
  距离: 24.8008
  内存使用: 0.3512
  能量使用: 0.6816
  推理时间: 1.9216秒

批次 54:
  奖励值: 79.4679
  收益率: 0.3909
  距离: 20.6804
  内存使用: 0.3699
  能量使用: 0.6139
  推理时间: 1.7222秒

批次 55:
  奖励值: 89.6827
  收益率: 0.4502
  距离: 23.1996
  内存使用: 0.3762
  能量使用: 0.6588
  推理时间: 1.8666秒

批次 56:
  奖励值: 84.3612
  收益率: 0.4234
  距离: 23.0802
  内存使用: 0.3640
  能量使用: 0.6335
  推理时间: 1.8984秒

批次 57:
  奖励值: 83.1468
  收益率: 0.4184
  距离: 22.2817
  内存使用: 0.3790
  能量使用: 0.6457
  推理时间: 1.9211秒

批次 58:
  奖励值: 94.6546
  收益率: 0.4674
  距离: 24.5233
  内存使用: 0.4524
  能量使用: 0.7151
  推理时间: 2.1710秒

批次 59:
  奖励值: 82.5283
  收益率: 0.4180
  距离: 22.0873
  内存使用: 0.3623
  能量使用: 0.6938
  推理时间: 2.1390秒

批次 60:
  奖励值: 85.1279
  收益率: 0.4140
  距离: 19.7492
  内存使用: 0.3293
  能量使用: 0.6657
  推理时间: 1.8978秒

批次 61:
  奖励值: 89.0120
  收益率: 0.4465
  距离: 23.8452
  内存使用: 0.4105
  能量使用: 0.7202
  推理时间: 1.9935秒

批次 62:
  奖励值: 91.9220
  收益率: 0.4771
  距离: 25.7178
  内存使用: 0.4305
  能量使用: 0.8077
  推理时间: 1.8421秒

批次 63:
  奖励值: 91.5875
  收益率: 0.4413
  距离: 23.9990
  内存使用: 0.4611
  能量使用: 0.7649
  推理时间: 1.7957秒

批次 64:
  奖励值: 90.0041
  收益率: 0.4547
  距离: 25.5268
  内存使用: 0.3592
  能量使用: 0.7057
  推理时间: 2.8712秒

批次 65:
  奖励值: 81.1380
  收益率: 0.4032
  距离: 20.5202
  内存使用: 0.3844
  能量使用: 0.6194
  推理时间: 1.6709秒

批次 66:
  奖励值: 94.7558
  收益率: 0.4706
  距离: 22.2588
  内存使用: 0.4415
  能量使用: 0.7882
  推理时间: 1.9228秒

批次 67:
  奖励值: 88.8579
  收益率: 0.4407
  距离: 23.9029
  内存使用: 0.4086
  能量使用: 0.7726
  推理时间: 1.9331秒

批次 68:
  奖励值: 84.5641
  收益率: 0.4141
  距离: 20.2388
  内存使用: 0.3827
  能量使用: 0.6503
  推理时间: 1.6462秒

批次 69:
  奖励值: 80.7163
  收益率: 0.4090
  距离: 19.9117
  内存使用: 0.4040
  能量使用: 0.6517
  推理时间: 1.6331秒

批次 70:
  奖励值: 89.2126
  收益率: 0.4444
  距离: 24.0930
  内存使用: 0.7186
  能量使用: 0.7633
  推理时间: 1.9784秒

批次 71:
  奖励值: 91.3634
  收益率: 0.4501
  距离: 22.6710
  内存使用: 0.4665
  能量使用: 0.7356
  推理时间: 1.9384秒

批次 72:
  奖励值: 79.8246
  收益率: 0.4309
  距离: 21.5457
  内存使用: 0.3572
  能量使用: 0.6480
  推理时间: 1.7715秒

批次 73:
  奖励值: 80.5122
  收益率: 0.4107
  距离: 23.7045
  内存使用: 0.4031
  能量使用: 0.6681
  推理时间: 1.7225秒

批次 74:
  奖励值: 92.1325
  收益率: 0.4596
  距离: 26.3393
  内存使用: 0.4896
  能量使用: 0.7061
  推理时间: 1.8769秒

批次 75:
  奖励值: 89.5268
  收益率: 0.4496
  距离: 25.9413
  内存使用: 0.4484
  能量使用: 0.7567
  推理时间: 3.2758秒

批次 76:
  奖励值: 86.4091
  收益率: 0.4367
  距离: 23.4208
  内存使用: 0.4489
  能量使用: 0.7121
  推理时间: 1.7083秒

批次 77:
  奖励值: 84.4291
  收益率: 0.4338
  距离: 26.0621
  内存使用: 0.3896
  能量使用: 0.6901
  推理时间: 2.0557秒

批次 78:
  奖励值: 92.1031
  收益率: 0.4429
  距离: 24.5464
  内存使用: 0.4629
  能量使用: 0.7845
  推理时间: 2.0249秒

批次 79:
  奖励值: 88.7764
  收益率: 0.4484
  距离: 20.3246
  内存使用: 0.4148
  能量使用: 0.7077
  推理时间: 1.7611秒

批次 80:
  奖励值: 84.4681
  收益率: 0.4406
  距离: 19.7706
  内存使用: 0.4015
  能量使用: 0.6917
  推理时间: 1.7436秒

批次 81:
  奖励值: 94.0136
  收益率: 0.4692
  距离: 26.9017
  内存使用: 0.4884
  能量使用: 0.7827
  推理时间: 1.9385秒

批次 82:
  奖励值: 78.2064
  收益率: 0.3960
  距离: 23.7167
  内存使用: 0.3611
  能量使用: 0.7094
  推理时间: 1.7073秒

批次 83:
  奖励值: 83.0940
  收益率: 0.4145
  距离: 23.4205
  内存使用: 0.3404
  能量使用: 0.6511
  推理时间: 1.9388秒

批次 84:
  奖励值: 88.1367
  收益率: 0.4459
  距离: 25.2074
  内存使用: 0.3820
  能量使用: 0.7373
  推理时间: 1.8191秒

批次 85:
  奖励值: 89.8216
  收益率: 0.4559
  距离: 25.8434
  内存使用: 0.3985
  能量使用: 0.7149
  推理时间: 1.8222秒

批次 86:
  奖励值: 87.8907
  收益率: 0.4460
  距离: 27.8885
  内存使用: 0.3761
  能量使用: 0.7540
  推理时间: 2.0319秒

批次 87:
  奖励值: 71.9010
  收益率: 0.3719
  距离: 18.4541
  内存使用: 0.3599
  能量使用: 0.5841
  推理时间: 1.6484秒

批次 88:
  奖励值: 82.7120
  收益率: 0.4371
  距离: 24.6774
  内存使用: 0.3758
  能量使用: 0.7227
  推理时间: 1.9009秒

批次 89:
  奖励值: 90.0978
  收益率: 0.4636
  距离: 25.2445
  内存使用: 0.4517
  能量使用: 0.7081
  推理时间: 1.9992秒

批次 90:
  奖励值: 83.9893
  收益率: 0.4255
  距离: 21.1099
  内存使用: 0.4046
  能量使用: 0.7069
  推理时间: 1.8814秒

批次 91:
  奖励值: 94.6474
  收益率: 0.4739
  距离: 25.1852
  内存使用: 0.5018
  能量使用: 0.8182
  推理时间: 1.9561秒

批次 92:
  奖励值: 73.3436
  收益率: 0.3914
  距离: 23.5727
  内存使用: 0.3160
  能量使用: 0.6241
  推理时间: 1.5639秒

批次 93:
  奖励值: 83.3479
  收益率: 0.4253
  距离: 23.0573
  内存使用: 0.3337
  能量使用: 0.7132
  推理时间: 1.6938秒

批次 94:
  奖励值: 80.1614
  收益率: 0.4290
  距离: 22.8197
  内存使用: 0.3829
  能量使用: 0.6745
  推理时间: 1.7274秒

批次 95:
  奖励值: 86.5052
  收益率: 0.4350
  距离: 24.5015
  内存使用: 0.3633
  能量使用: 0.7247
  推理时间: 1.6730秒

批次 96:
  奖励值: 78.2513
  收益率: 0.4060
  距离: 21.7734
  内存使用: 0.3047
  能量使用: 0.6193
  推理时间: 1.6136秒

批次 97:
  奖励值: 83.2993
  收益率: 0.4176
  距离: 24.5257
  内存使用: 0.4413
  能量使用: 0.6344
  推理时间: 1.6578秒

批次 98:
  奖励值: 83.3251
  收益率: 0.4445
  距离: 23.6474
  内存使用: 0.4303
  能量使用: 0.6722
  推理时间: 1.7165秒

批次 99:
  奖励值: 86.8920
  收益率: 0.4253
  距离: 19.7236
  内存使用: 0.4001
  能量使用: 0.6922
  推理时间: 1.7975秒

批次 100:
  奖励值: 87.0018
  收益率: 0.4293
  距离: 23.4829
  内存使用: 0.3917
  能量使用: 0.7218
  推理时间: 1.8715秒


==================== 总结 ====================
平均收益率: 0.4304
平均能量使用: 0.6909
平均推理时间: 1.8147秒
