推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 147.7951
  收益率: 0.1860
  距离: 35.3363
  内存使用: 0.8180
  能量使用: 1.0555
  推理时间: 2.5111秒

批次 2:
  奖励值: 113.9918
  收益率: 0.1455
  距离: 31.4616
  内存使用: 0.6391
  能量使用: 0.9156
  推理时间: 2.0503秒

批次 3:
  奖励值: 138.4755
  收益率: 0.1720
  距离: 31.8463
  内存使用: 0.7721
  能量使用: 1.0306
  推理时间: 2.3498秒

批次 4:
  奖励值: 135.0442
  收益率: 0.1690
  距离: 37.6061
  内存使用: 0.8423
  能量使用: 1.0630
  推理时间: 2.3884秒

批次 5:
  奖励值: 125.2577
  收益率: 0.1599
  距离: 31.3711
  内存使用: 0.9159
  能量使用: 0.9975
  推理时间: 2.2141秒

批次 6:
  奖励值: 105.8723
  收益率: 0.1333
  距离: 28.2922
  内存使用: 0.9190
  能量使用: 0.8441
  推理时间: 1.8863秒

批次 7:
  奖励值: 134.5041
  收益率: 0.1694
  距离: 37.1867
  内存使用: 0.7627
  能量使用: 1.0423
  推理时间: 2.4466秒

批次 8:
  奖励值: 119.0823
  收益率: 0.1510
  距离: 33.6287
  内存使用: 0.6516
  能量使用: 0.9551
  推理时间: 2.1050秒

批次 9:
  奖励值: 131.3096
  收益率: 0.1634
  距离: 34.4239
  内存使用: 0.6976
  能量使用: 1.0592
  推理时间: 2.3498秒

批次 10:
  奖励值: 126.5808
  收益率: 0.1581
  距离: 29.5964
  内存使用: 0.6524
  能量使用: 0.9895
  推理时间: 2.2563秒

批次 11:
  奖励值: 115.4095
  收益率: 0.1485
  距离: 30.3553
  内存使用: 0.9050
  能量使用: 0.9208
  推理时间: 2.0720秒

批次 12:
  奖励值: 119.3426
  收益率: 0.1485
  距离: 26.3330
  内存使用: 0.6515
  能量使用: 0.9302
  推理时间: 2.3486秒

批次 13:
  奖励值: 147.3145
  收益率: 0.1823
  距离: 33.0025
  内存使用: 0.8109
  能量使用: 1.1576
  推理时间: 2.4990秒

批次 14:
  奖励值: 108.2679
  收益率: 0.1373
  距离: 28.2338
  内存使用: 0.9144
  能量使用: 0.9161
  推理时间: 2.0377秒

批次 15:
  奖励值: 126.7811
  收益率: 0.1590
  距离: 32.4329
  内存使用: 0.6844
  能量使用: 0.9204
  推理时间: 2.2129秒

批次 16:
  奖励值: 121.1276
  收益率: 0.1500
  距离: 29.1854
  内存使用: 0.6340
  能量使用: 0.9254
  推理时间: 2.0835秒

批次 17:
  奖励值: 134.0337
  收益率: 0.1668
  距离: 31.4699
  内存使用: 0.6700
  能量使用: 0.9927
  推理时间: 2.4202秒

批次 18:
  奖励值: 112.2262
  收益率: 0.1419
  距离: 29.2566
  内存使用: 0.9123
  能量使用: 0.9788
  推理时间: 2.0593秒

批次 19:
  奖励值: 129.9671
  收益率: 0.1647
  距离: 33.3637
  内存使用: 0.7180
  能量使用: 0.9840
  推理时间: 2.3873秒

批次 20:
  奖励值: 137.8040
  收益率: 0.1769
  距离: 37.5945
  内存使用: 0.7957
  能量使用: 1.2017
  推理时间: 2.5682秒

批次 21:
  奖励值: 118.4199
  收益率: 0.1500
  距离: 27.8236
  内存使用: 0.6244
  能量使用: 0.9122
  推理时间: 2.0524秒

批次 22:
  奖励值: 128.5347
  收益率: 0.1637
  距离: 30.9058
  内存使用: 0.7525
  能量使用: 1.0724
  推理时间: 2.2966秒

批次 23:
  奖励值: 127.7548
  收益率: 0.1607
  距离: 35.2522
  内存使用: 0.6743
  能量使用: 0.9639
  推理时间: 2.3145秒

批次 24:
  奖励值: 112.2793
  收益率: 0.1402
  距离: 28.4743
  内存使用: 0.9109
  能量使用: 0.8562
  推理时间: 2.0500秒

批次 25:
  奖励值: 121.7999
  收益率: 0.1529
  距离: 30.7923
  内存使用: 0.6449
  能量使用: 0.9631
  推理时间: 2.1910秒

批次 26:
  奖励值: 126.0638
  收益率: 0.1599
  距离: 29.7672
  内存使用: 0.7285
  能量使用: 0.9767
  推理时间: 2.1709秒

批次 27:
  奖励值: 129.5263
  收益率: 0.1633
  距离: 34.5379
  内存使用: 0.7572
  能量使用: 1.0523
  推理时间: 2.4041秒

批次 28:
  奖励值: 123.9491
  收益率: 0.1538
  距离: 32.3183
  内存使用: 0.6413
  能量使用: 0.9421
  推理时间: 2.2703秒

批次 29:
  奖励值: 123.1754
  收益率: 0.1571
  距离: 33.3693
  内存使用: 0.9098
  能量使用: 0.9701
  推理时间: 2.5256秒

批次 30:
  奖励值: 105.6766
  收益率: 0.1326
  距离: 27.0992
  内存使用: 0.9144
  能量使用: 0.8332
  推理时间: 1.9574秒

批次 31:
  奖励值: 114.9020
  收益率: 0.1460
  距离: 30.1538
  内存使用: 0.9130
  能量使用: 0.9293
  推理时间: 2.1976秒

批次 32:
  奖励值: 105.8614
  收益率: 0.1328
  距离: 26.6557
  内存使用: 0.9069
  能量使用: 0.9146
  推理时间: 2.1034秒

批次 33:
  奖励值: 128.6503
  收益率: 0.1616
  距离: 32.0338
  内存使用: 0.9114
  能量使用: 0.9542
  推理时间: 2.2767秒

批次 34:
  奖励值: 110.1571
  收益率: 0.1388
  距离: 29.8509
  内存使用: 0.9054
  能量使用: 0.9295
  推理时间: 1.9588秒

批次 35:
  奖励值: 132.2266
  收益率: 0.1632
  距离: 33.5612
  内存使用: 0.7092
  能量使用: 1.0764
  推理时间: 2.3406秒

批次 36:
  奖励值: 126.0031
  收益率: 0.1546
  距离: 27.9858
  内存使用: 0.6816
  能量使用: 0.9262
  推理时间: 2.1346秒

批次 37:
  奖励值: 122.7930
  收益率: 0.1575
  距离: 33.0051
  内存使用: 0.6332
  能量使用: 0.9756
  推理时间: 2.1236秒

批次 38:
  奖励值: 132.7345
  收益率: 0.1705
  距离: 33.9040
  内存使用: 0.8027
  能量使用: 1.0243
  推理时间: 2.3056秒

批次 39:
  奖励值: 123.4981
  收益率: 0.1565
  距离: 31.4692
  内存使用: 0.6284
  能量使用: 0.9061
  推理时间: 2.1468秒

批次 40:
  奖励值: 119.1889
  收益率: 0.1468
  距离: 30.3871
  内存使用: 0.9045
  能量使用: 0.9306
  推理时间: 2.1337秒

批次 41:
  奖励值: 135.9290
  收益率: 0.1693
  距离: 33.4473
  内存使用: 0.6566
  能量使用: 1.0380
  推理时间: 2.4867秒

批次 42:
  奖励值: 130.6743
  收益率: 0.1622
  距离: 30.8608
  内存使用: 0.7332
  能量使用: 1.0573
  推理时间: 2.2929秒

批次 43:
  奖励值: 123.7174
  收益率: 0.1538
  距离: 31.0730
  内存使用: 0.6966
  能量使用: 0.9956
  推理时间: 2.1620秒

批次 44:
  奖励值: 118.4320
  收益率: 0.1475
  距离: 27.7332
  内存使用: 0.9088
  能量使用: 0.8940
  推理时间: 2.0752秒

批次 45:
  奖励值: 127.1979
  收益率: 0.1599
  距离: 33.5353
  内存使用: 0.6557
  能量使用: 1.0132
  推理时间: 2.1818秒

批次 46:
  奖励值: 139.4339
  收益率: 0.1783
  距离: 32.0994
  内存使用: 0.7655
  能量使用: 1.0672
  推理时间: 2.3447秒

批次 47:
  奖励值: 122.2017
  收益率: 0.1525
  距离: 31.7138
  内存使用: 0.6548
  能量使用: 1.0280
  推理时间: 2.1295秒

批次 48:
  奖励值: 127.9746
  收益率: 0.1576
  距离: 27.9978
  内存使用: 0.6336
  能量使用: 1.0194
  推理时间: 2.2345秒

批次 49:
  奖励值: 139.3165
  收益率: 0.1763
  距离: 34.2061
  内存使用: 0.7070
  能量使用: 1.0537
  推理时间: 2.4667秒

批次 50:
  奖励值: 103.4219
  收益率: 0.1296
  距离: 26.3456
  内存使用: 0.8178
  能量使用: 0.7928
  推理时间: 1.8866秒

批次 51:
  奖励值: 136.8615
  收益率: 0.1751
  距离: 36.4083
  内存使用: 0.7958
  能量使用: 1.0728
  推理时间: 2.3443秒

批次 52:
  奖励值: 115.9627
  收益率: 0.1422
  距离: 26.4546
  内存使用: 0.9129
  能量使用: 0.9072
  推理时间: 1.9974秒

批次 53:
  奖励值: 124.1942
  收益率: 0.1549
  距离: 29.8944
  内存使用: 0.7319
  能量使用: 0.9854
  推理时间: 2.1992秒

批次 54:
  奖励值: 126.0806
  收益率: 0.1597
  距离: 31.7182
  内存使用: 0.6756
  能量使用: 1.0449
  推理时间: 2.1321秒

批次 55:
  奖励值: 130.4838
  收益率: 0.1661
  距离: 35.6461
  内存使用: 0.7307
  能量使用: 1.0557
  推理时间: 2.6091秒

批次 56:
  奖励值: 145.9230
  收益率: 0.1843
  距离: 37.5638
  内存使用: 0.8419
  能量使用: 1.1484
  推理时间: 2.5018秒

批次 57:
  奖励值: 111.4876
  收益率: 0.1401
  距离: 28.4005
  内存使用: 0.9110
  能量使用: 0.9133
  推理时间: 1.9603秒

批次 58:
  奖励值: 129.7047
  收益率: 0.1622
  距离: 31.6801
  内存使用: 0.7513
  能量使用: 0.9590
  推理时间: 2.2235秒

批次 59:
  奖励值: 136.3066
  收益率: 0.1731
  距离: 36.1820
  内存使用: 0.7307
  能量使用: 1.0741
  推理时间: 2.3300秒

批次 60:
  奖励值: 121.3676
  收益率: 0.1546
  距离: 29.1456
  内存使用: 0.6298
  能量使用: 0.8894
  推理时间: 2.1003秒

批次 61:
  奖励值: 122.3024
  收益率: 0.1523
  距离: 30.2701
  内存使用: 0.6248
  能量使用: 0.9002
  推理时间: 2.1669秒

批次 62:
  奖励值: 126.3278
  收益率: 0.1555
  距离: 29.3470
  内存使用: 0.6461
  能量使用: 0.9514
  推理时间: 2.5840秒

批次 63:
  奖励值: 114.7831
  收益率: 0.1450
  距离: 31.0213
  内存使用: 0.5784
  能量使用: 0.8341
  推理时间: 2.0401秒

批次 64:
  奖励值: 126.5825
  收益率: 0.1602
  距离: 31.8990
  内存使用: 0.7474
  能量使用: 0.9885
  推理时间: 2.1985秒

批次 65:
  奖励值: 147.6841
  收益率: 0.1794
  距离: 35.8380
  内存使用: 0.8444
  能量使用: 1.1494
  推理时间: 2.5536秒

批次 66:
  奖励值: 122.3206
  收益率: 0.1526
  距离: 26.1492
  内存使用: 0.6703
  能量使用: 0.9393
  推理时间: 2.1866秒

批次 67:
  奖励值: 138.1166
  收益率: 0.1722
  距离: 32.5173
  内存使用: 0.7972
  能量使用: 1.0522
  推理时间: 2.3668秒

批次 68:
  奖励值: 145.3054
  收益率: 0.1815
  距离: 35.9776
  内存使用: 0.7680
  能量使用: 1.2435
  推理时间: 2.5615秒

批次 69:
  奖励值: 126.6919
  收益率: 0.1605
  距离: 32.1870
  内存使用: 0.7391
  能量使用: 0.8900
  推理时间: 2.2012秒

批次 70:
  奖励值: 117.7690
  收益率: 0.1464
  距离: 30.4768
  内存使用: 0.9061
  能量使用: 0.8790
  推理时间: 2.0940秒

批次 71:
  奖励值: 104.4032
  收益率: 0.1300
  距离: 25.3911
  内存使用: 0.9083
  能量使用: 0.8942
  推理时间: 1.8566秒

批次 72:
  奖励值: 128.6070
  收益率: 0.1585
  距离: 30.5017
  内存使用: 0.7022
  能量使用: 0.9819
  推理时间: 2.2308秒

批次 73:
  奖励值: 137.0643
  收益率: 0.1728
  距离: 36.9956
  内存使用: 0.8057
  能量使用: 1.0699
  推理时间: 2.4128秒

批次 74:
  奖励值: 128.0589
  收益率: 0.1588
  距离: 31.1263
  内存使用: 0.7300
  能量使用: 1.0260
  推理时间: 2.4195秒

批次 75:
  奖励值: 116.1164
  收益率: 0.1458
  距离: 29.9417
  内存使用: 0.9043
  能量使用: 0.9211
  推理时间: 2.2148秒

批次 76:
  奖励值: 129.9574
  收益率: 0.1632
  距离: 32.6167
  内存使用: 0.7066
  能量使用: 0.9619
  推理时间: 2.2965秒

批次 77:
  奖励值: 116.6999
  收益率: 0.1465
  距离: 28.5806
  内存使用: 0.9075
  能量使用: 0.9121
  推理时间: 2.0903秒

批次 78:
  奖励值: 121.0246
  收益率: 0.1555
  距离: 32.3643
  内存使用: 0.6729
  能量使用: 0.9137
  推理时间: 2.1223秒

批次 79:
  奖励值: 136.7744
  收益率: 0.1742
  距离: 32.4484
  内存使用: 0.7406
  能量使用: 1.0620
  推理时间: 2.4521秒

批次 80:
  奖励值: 111.7902
  收益率: 0.1415
  距离: 29.8024
  内存使用: 0.9119
  能量使用: 0.9053
  推理时间: 2.0414秒

批次 81:
  奖励值: 139.2391
  收益率: 0.1756
  距离: 35.3088
  内存使用: 0.8151
  能量使用: 0.9979
  推理时间: 2.5436秒

批次 82:
  奖励值: 139.1299
  收益率: 0.1767
  距离: 38.1138
  内存使用: 0.7068
  能量使用: 1.1196
  推理时间: 2.4108秒

批次 83:
  奖励值: 106.5670
  收益率: 0.1335
  距离: 26.7124
  内存使用: 0.9072
  能量使用: 0.8465
  推理时间: 1.9401秒

批次 84:
  奖励值: 144.7709
  收益率: 0.1817
  距离: 35.5395
  内存使用: 0.8384
  能量使用: 1.0803
  推理时间: 2.7277秒

批次 85:
  奖励值: 114.2405
  收益率: 0.1451
  距离: 30.3187
  内存使用: 0.9073
  能量使用: 0.8930
  推理时间: 2.0204秒

批次 86:
  奖励值: 130.4229
  收益率: 0.1597
  距离: 29.4103
  内存使用: 0.6579
  能量使用: 1.0860
  推理时间: 2.3260秒

批次 87:
  奖励值: 135.6381
  收益率: 0.1709
  距离: 33.0283
  内存使用: 0.7876
  能量使用: 1.1078
  推理时间: 2.4796秒

批次 88:
  奖励值: 120.2183
  收益率: 0.1472
  距离: 28.5178
  内存使用: 0.9074
  能量使用: 0.9381
  推理时间: 2.1682秒

批次 89:
  奖励值: 104.9436
  收益率: 0.1292
  距离: 26.4648
  内存使用: 0.9112
  能量使用: 0.9368
  推理时间: 1.9093秒

批次 90:
  奖励值: 120.3598
  收益率: 0.1490
  距离: 27.2705
  内存使用: 0.6458
  能量使用: 0.9640
  推理时间: 2.0733秒

批次 91:
  奖励值: 122.3618
  收益率: 0.1509
  距离: 27.7765
  内存使用: 0.5478
  能量使用: 0.9520
  推理时间: 2.1275秒

批次 92:
  奖励值: 146.5275
  收益率: 0.1815
  距离: 38.6868
  内存使用: 0.8790
  能量使用: 1.1791
  推理时间: 2.7076秒

批次 93:
  奖励值: 130.4921
  收益率: 0.1619
  距离: 33.0497
  内存使用: 0.7371
  能量使用: 1.0289
  推理时间: 2.3646秒

批次 94:
  奖励值: 120.2778
  收益率: 0.1514
  距离: 27.4557
  内存使用: 0.9053
  能量使用: 0.8834
  推理时间: 2.0993秒

批次 95:
  奖励值: 109.3619
  收益率: 0.1372
  距离: 25.2761
  内存使用: 0.5706
  能量使用: 0.8295
  推理时间: 1.9223秒

批次 96:
  奖励值: 118.7297
  收益率: 0.1515
  距离: 32.7980
  内存使用: 0.6115
  能量使用: 0.9311
  推理时间: 2.0983秒

批次 97:
  奖励值: 136.0193
  收益率: 0.1726
  距离: 36.4113
  内存使用: 0.7979
  能量使用: 1.1522
  推理时间: 2.4726秒

批次 98:
  奖励值: 141.2232
  收益率: 0.1756
  距离: 34.2504
  内存使用: 0.8270
  能量使用: 1.0762
  推理时间: 2.5224秒

批次 99:
  奖励值: 141.2450
  收益率: 0.1778
  距离: 37.2755
  内存使用: 0.6876
  能量使用: 1.1467
  推理时间: 2.6282秒

批次 100:
  奖励值: 119.1941
  收益率: 0.1518
  距离: 32.2981
  内存使用: 0.9149
  能量使用: 0.9989
  推理时间: 2.1198秒


==================== 总结 ====================
平均收益率: 0.1578
平均能量使用: 0.9862
平均推理时间: 2.2461秒
