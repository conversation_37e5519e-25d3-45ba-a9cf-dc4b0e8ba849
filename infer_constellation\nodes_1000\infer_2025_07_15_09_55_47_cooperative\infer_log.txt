推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 108.1172
  收益率: 0.2833
  距离: 30.8764
  内存使用: 0.5624
  能量使用: 0.9269
  推理时间: 2.0242秒

批次 2:
  奖励值: 109.1419
  收益率: 0.2737
  距离: 29.0503
  内存使用: 0.5934
  能量使用: 0.8725
  推理时间: 1.9680秒

批次 3:
  奖励值: 125.1463
  收益率: 0.3103
  距离: 32.4032
  内存使用: 0.7300
  能量使用: 0.9742
  推理时间: 2.2995秒

批次 4:
  奖励值: 109.1446
  收益率: 0.2725
  距离: 28.9197
  内存使用: 0.8828
  能量使用: 0.9083
  推理时间: 1.9706秒

批次 5:
  奖励值: 117.1825
  收益率: 0.2970
  距离: 31.3971
  内存使用: 0.6847
  能量使用: 0.9531
  推理时间: 2.0929秒

批次 6:
  奖励值: 129.4735
  收益率: 0.3291
  距离: 35.2938
  内存使用: 0.7105
  能量使用: 1.0486
  推理时间: 2.4738秒

批次 7:
  奖励值: 106.4964
  收益率: 0.2729
  距离: 27.7644
  内存使用: 0.6186
  能量使用: 0.8243
  推理时间: 1.9743秒

批次 8:
  奖励值: 107.6771
  收益率: 0.2742
  距离: 29.1969
  内存使用: 0.6444
  能量使用: 0.8045
  推理时间: 2.1168秒

批次 9:
  奖励值: 125.9210
  收益率: 0.3128
  距离: 28.3594
  内存使用: 0.6590
  能量使用: 0.9396
  推理时间: 2.1803秒

批次 10:
  奖励值: 127.2066
  收益率: 0.3132
  距离: 30.3670
  内存使用: 0.6646
  能量使用: 0.9552
  推理时间: 2.1918秒

批次 11:
  奖励值: 124.9875
  收益率: 0.3051
  距离: 30.8482
  内存使用: 0.6571
  能量使用: 0.9198
  推理时间: 2.1443秒

批次 12:
  奖励值: 133.6893
  收益率: 0.3388
  距离: 32.4549
  内存使用: 0.7621
  能量使用: 1.0042
  推理时间: 2.3306秒

批次 13:
  奖励值: 129.7538
  收益率: 0.3224
  距离: 28.8768
  内存使用: 0.7967
  能量使用: 1.0188
  推理时间: 2.3642秒

批次 14:
  奖励值: 87.7655
  收益率: 0.2256
  距离: 24.8028
  内存使用: 0.7600
  能量使用: 0.7063
  推理时间: 1.5811秒

批次 15:
  奖励值: 113.3894
  收益率: 0.2896
  距离: 30.0420
  内存使用: 0.6683
  能量使用: 0.9109
  推理时间: 2.0249秒

批次 16:
  奖励值: 124.8046
  收益率: 0.3158
  距离: 32.8272
  内存使用: 0.6165
  能量使用: 0.9470
  推理时间: 2.2084秒

批次 17:
  奖励值: 107.8393
  收益率: 0.2723
  距离: 27.7682
  内存使用: 0.4956
  能量使用: 0.8576
  推理时间: 1.8767秒

批次 18:
  奖励值: 125.1288
  收益率: 0.3188
  距离: 31.5904
  内存使用: 0.6859
  能量使用: 1.0660
  推理时间: 2.2532秒

批次 19:
  奖励值: 114.9742
  收益率: 0.2871
  距离: 27.7210
  内存使用: 0.6256
  能量使用: 0.9049
  推理时间: 2.0139秒

批次 20:
  奖励值: 135.3197
  收益率: 0.3333
  距离: 32.3774
  内存使用: 0.7053
  能量使用: 1.0780
  推理时间: 2.3209秒

批次 21:
  奖励值: 129.9995
  收益率: 0.3070
  距离: 28.5780
  内存使用: 0.6193
  能量使用: 1.0037
  推理时间: 2.2045秒

批次 22:
  奖励值: 119.3237
  收益率: 0.2979
  距离: 31.1890
  内存使用: 0.6543
  能量使用: 0.9091
  推理时间: 2.1236秒

批次 23:
  奖励值: 113.1287
  收益率: 0.2877
  距离: 29.1703
  内存使用: 0.6334
  能量使用: 0.9390
  推理时间: 2.0694秒

批次 24:
  奖励值: 105.0556
  收益率: 0.2630
  距离: 27.5394
  内存使用: 0.8773
  能量使用: 0.8720
  推理时间: 2.0262秒

批次 25:
  奖励值: 129.2941
  收益率: 0.3115
  距离: 31.1614
  内存使用: 0.6864
  能量使用: 0.9640
  推理时间: 2.3052秒

批次 26:
  奖励值: 120.5202
  收益率: 0.3111
  距离: 35.6492
  内存使用: 0.7145
  能量使用: 0.9959
  推理时间: 2.2529秒

批次 27:
  奖励值: 112.5573
  收益率: 0.2926
  距离: 32.6285
  内存使用: 0.5880
  能量使用: 0.8687
  推理时间: 2.0493秒

批次 28:
  奖励值: 117.6001
  收益率: 0.2904
  距离: 26.3225
  内存使用: 0.6106
  能量使用: 0.8666
  推理时间: 2.0035秒

批次 29:
  奖励值: 120.3127
  收益率: 0.2999
  距离: 29.3313
  内存使用: 0.6355
  能量使用: 1.0078
  推理时间: 2.2218秒

批次 30:
  奖励值: 122.1653
  收益率: 0.2960
  距离: 29.8317
  内存使用: 0.6734
  能量使用: 0.9643
  推理时间: 2.0879秒

批次 31:
  奖励值: 140.8685
  收益率: 0.3517
  距离: 32.0555
  内存使用: 0.7357
  能量使用: 1.0718
  推理时间: 2.4493秒

批次 32:
  奖励值: 120.3959
  收益率: 0.3033
  距离: 32.5210
  内存使用: 0.7167
  能量使用: 0.9705
  推理时间: 2.1372秒

批次 33:
  奖励值: 139.0025
  收益率: 0.3472
  距离: 35.7897
  内存使用: 0.7642
  能量使用: 1.0410
  推理时间: 2.4152秒

批次 34:
  奖励值: 121.5666
  收益率: 0.3061
  距离: 34.3290
  内存使用: 0.7610
  能量使用: 0.9767
  推理时间: 2.2527秒

批次 35:
  奖励值: 105.2373
  收益率: 0.2665
  距离: 28.2919
  内存使用: 0.9119
  能量使用: 0.9216
  推理时间: 1.9500秒

批次 36:
  奖励值: 140.4318
  收益率: 0.3517
  距离: 34.3245
  内存使用: 0.7598
  能量使用: 1.0521
  推理时间: 2.4254秒

批次 37:
  奖励值: 114.8977
  收益率: 0.2916
  距离: 30.5348
  内存使用: 0.7027
  能量使用: 0.9671
  推理时间: 2.1262秒

批次 38:
  奖励值: 108.6712
  收益率: 0.2752
  距离: 27.0092
  内存使用: 0.9115
  能量使用: 0.9368
  推理时间: 2.0091秒

批次 39:
  奖励值: 120.7051
  收益率: 0.3129
  距离: 31.3028
  内存使用: 0.6799
  能量使用: 1.0070
  推理时间: 2.2516秒

批次 40:
  奖励值: 117.8369
  收益率: 0.2960
  距离: 31.1847
  内存使用: 0.6002
  能量使用: 0.9412
  推理时间: 2.0849秒

批次 41:
  奖励值: 110.5389
  收益率: 0.2766
  距离: 27.0046
  内存使用: 0.6231
  能量使用: 0.8946
  推理时间: 2.0838秒

批次 42:
  奖励值: 127.2728
  收益率: 0.3150
  距离: 31.7006
  内存使用: 0.7488
  能量使用: 1.0050
  推理时间: 2.2481秒

批次 43:
  奖励值: 112.9482
  收益率: 0.2941
  距离: 32.5243
  内存使用: 0.6861
  能量使用: 0.8931
  推理时间: 2.0461秒

批次 44:
  奖励值: 111.7958
  收益率: 0.2862
  距离: 29.4860
  内存使用: 0.6091
  能量使用: 0.9132
  推理时间: 1.9717秒

批次 45:
  奖励值: 119.4202
  收益率: 0.3053
  距离: 34.5872
  内存使用: 0.6210
  能量使用: 0.9514
  推理时间: 2.1439秒

批次 46:
  奖励值: 107.8855
  收益率: 0.2740
  距离: 27.4071
  内存使用: 0.5242
  能量使用: 0.8976
  推理时间: 1.9056秒

批次 47:
  奖励值: 114.1014
  收益率: 0.2860
  距离: 29.6051
  内存使用: 0.6251
  能量使用: 0.9669
  推理时间: 2.2094秒

批次 48:
  奖励值: 117.3979
  收益率: 0.3021
  距离: 27.8970
  内存使用: 0.6297
  能量使用: 0.8775
  推理时间: 2.0619秒

批次 49:
  奖励值: 111.4209
  收益率: 0.2788
  距离: 28.5086
  内存使用: 0.6182
  能量使用: 0.8313
  推理时间: 2.0478秒

批次 50:
  奖励值: 106.0439
  收益率: 0.2596
  距离: 26.4421
  内存使用: 0.5547
  能量使用: 0.9153
  推理时间: 1.8913秒

批次 51:
  奖励值: 113.4429
  收益率: 0.2797
  距离: 28.0205
  内存使用: 0.6253
  能量使用: 0.8684
  推理时间: 1.9646秒

批次 52:
  奖励值: 131.7512
  收益率: 0.3286
  距离: 34.6910
  内存使用: 0.7613
  能量使用: 1.0452
  推理时间: 2.3081秒

批次 53:
  奖励值: 127.0835
  收益率: 0.3296
  距离: 33.1713
  内存使用: 0.6862
  能量使用: 0.9756
  推理时间: 2.4379秒

批次 54:
  奖励值: 120.6023
  收益率: 0.3006
  距离: 30.0936
  内存使用: 0.6832
  能量使用: 1.0186
  推理时间: 2.2215秒

批次 55:
  奖励值: 119.7073
  收益率: 0.2985
  距离: 29.6564
  内存使用: 0.6258
  能量使用: 0.9032
  推理时间: 2.0344秒

批次 56:
  奖励值: 111.7892
  收益率: 0.2792
  距离: 29.1828
  内存使用: 0.9135
  能量使用: 0.9253
  推理时间: 2.0911秒

批次 57:
  奖励值: 108.1573
  收益率: 0.2801
  距离: 26.5017
  内存使用: 0.9063
  能量使用: 0.8774
  推理时间: 2.0003秒

批次 58:
  奖励值: 124.5565
  收益率: 0.3065
  距离: 27.3449
  内存使用: 0.6265
  能量使用: 0.9155
  推理时间: 2.1184秒

批次 59:
  奖励值: 125.3918
  收益率: 0.3149
  距离: 30.1247
  内存使用: 0.7295
  能量使用: 0.9622
  推理时间: 2.1769秒

批次 60:
  奖励值: 112.7836
  收益率: 0.2876
  距离: 29.7192
  内存使用: 0.6236
  能量使用: 0.8900
  推理时间: 1.9432秒

批次 61:
  奖励值: 120.6837
  收益率: 0.3051
  距离: 31.2351
  内存使用: 0.6530
  能量使用: 0.9444
  推理时间: 2.1332秒

批次 62:
  奖励值: 120.8414
  收益率: 0.3046
  距离: 31.1226
  内存使用: 0.6900
  能量使用: 0.9045
  推理时间: 2.1676秒

批次 63:
  奖励值: 108.3804
  收益率: 0.2673
  距离: 27.5779
  内存使用: 0.6083
  能量使用: 0.8530
  推理时间: 1.9109秒

批次 64:
  奖励值: 112.3275
  收益率: 0.2890
  距离: 33.4323
  内存使用: 0.6277
  能量使用: 0.8850
  推理时间: 2.1325秒

批次 65:
  奖励值: 112.6413
  收益率: 0.2798
  距离: 27.1472
  内存使用: 0.5857
  能量使用: 0.9844
  推理时间: 1.9757秒

批次 66:
  奖励值: 105.6774
  收益率: 0.2655
  距离: 27.1717
  内存使用: 0.6064
  能量使用: 0.8670
  推理时间: 1.8532秒

批次 67:
  奖励值: 125.5849
  收益率: 0.3165
  距离: 32.7310
  内存使用: 0.6664
  能量使用: 1.0326
  推理时间: 2.2041秒

批次 68:
  奖励值: 135.0178
  收益率: 0.3432
  距离: 36.3561
  内存使用: 0.7404
  能量使用: 1.0678
  推理时间: 2.4808秒

批次 69:
  奖励值: 118.2178
  收益率: 0.2964
  距离: 30.0481
  内存使用: 0.6493
  能量使用: 0.8670
  推理时间: 2.1043秒

批次 70:
  奖励值: 111.7328
  收益率: 0.2829
  距离: 30.2976
  内存使用: 0.9101
  能量使用: 0.9267
  推理时间: 2.0338秒

批次 71:
  奖励值: 115.6467
  收益率: 0.2898
  距离: 30.9522
  内存使用: 0.6197
  能量使用: 0.9346
  推理时间: 2.0096秒

批次 72:
  奖励值: 122.5899
  收益率: 0.3112
  距离: 32.7269
  内存使用: 0.7219
  能量使用: 0.9713
  推理时间: 2.2747秒

批次 73:
  奖励值: 130.9365
  收益率: 0.3355
  距离: 31.9221
  内存使用: 0.7259
  能量使用: 1.0790
  推理时间: 2.2891秒

批次 74:
  奖励值: 117.0023
  收益率: 0.2817
  距离: 27.9083
  内存使用: 0.6263
  能量使用: 0.8607
  推理时间: 2.0483秒

批次 75:
  奖励值: 126.3880
  收益率: 0.3029
  距离: 31.6675
  内存使用: 0.7061
  能量使用: 1.0504
  推理时间: 2.2436秒

批次 76:
  奖励值: 123.3344
  收益率: 0.3123
  距离: 30.5563
  内存使用: 0.6765
  能量使用: 0.8983
  推理时间: 2.1733秒

批次 77:
  奖励值: 121.2265
  收益率: 0.3000
  距离: 34.4787
  内存使用: 0.6884
  能量使用: 0.9799
  推理时间: 2.1561秒

批次 78:
  奖励值: 114.0543
  收益率: 0.2928
  距离: 32.5084
  内存使用: 0.6294
  能量使用: 0.9536
  推理时间: 2.0194秒

批次 79:
  奖励值: 109.3160
  收益率: 0.2739
  距离: 28.0291
  内存使用: 0.6192
  能量使用: 0.9196
  推理时间: 1.9400秒

批次 80:
  奖励值: 106.8596
  收益率: 0.2668
  距离: 28.7586
  内存使用: 0.4972
  能量使用: 0.8250
  推理时间: 1.8581秒

批次 81:
  奖励值: 125.5869
  收益率: 0.3097
  距离: 29.7882
  内存使用: 0.7097
  能量使用: 0.9284
  推理时间: 2.1544秒

批次 82:
  奖励值: 115.2427
  收益率: 0.2947
  距离: 30.1197
  内存使用: 0.6409
  能量使用: 0.9539
  推理时间: 2.0705秒

批次 83:
  奖励值: 115.0555
  收益率: 0.2882
  距离: 28.1505
  内存使用: 0.6012
  能量使用: 0.9372
  推理时间: 2.1241秒

批次 84:
  奖励值: 121.3617
  收益率: 0.3107
  距离: 32.2815
  内存使用: 0.6533
  能量使用: 0.9417
  推理时间: 2.1445秒

批次 85:
  奖励值: 119.7398
  收益率: 0.3004
  距离: 30.6056
  内存使用: 0.6103
  能量使用: 0.9044
  推理时间: 2.0472秒

批次 86:
  奖励值: 87.0981
  收益率: 0.2289
  距离: 24.4628
  内存使用: 0.4329
  能量使用: 0.7081
  推理时间: 1.5925秒

批次 87:
  奖励值: 115.3365
  收益率: 0.2979
  距离: 27.9776
  内存使用: 0.5906
  能量使用: 0.9587
  推理时间: 2.0683秒

批次 88:
  奖励值: 120.0592
  收益率: 0.3037
  距离: 29.0870
  内存使用: 0.6718
  能量使用: 0.9228
  推理时间: 2.1231秒

批次 89:
  奖励值: 125.7402
  收益率: 0.3172
  距离: 35.2680
  内存使用: 0.7078
  能量使用: 0.9437
  推理时间: 2.2186秒

批次 90:
  奖励值: 101.7294
  收益率: 0.2610
  距离: 31.4960
  内存使用: 0.9044
  能量使用: 0.9407
  推理时间: 1.8689秒

批次 91:
  奖励值: 120.6045
  收益率: 0.3060
  距离: 30.9662
  内存使用: 0.6905
  能量使用: 0.9342
  推理时间: 2.1041秒

批次 92:
  奖励值: 126.1308
  收益率: 0.3182
  距离: 32.5340
  内存使用: 0.7332
  能量使用: 1.0713
  推理时间: 2.4990秒

批次 93:
  奖励值: 131.2493
  收益率: 0.3236
  距离: 33.0247
  内存使用: 0.8078
  能量使用: 1.0689
  推理时间: 2.3634秒

批次 94:
  奖励值: 123.9096
  收益率: 0.3089
  距离: 33.8273
  内存使用: 0.7548
  能量使用: 0.9988
  推理时间: 2.2416秒

批次 95:
  奖励值: 104.9480
  收益率: 0.2639
  距离: 29.2417
  内存使用: 0.6192
  能量使用: 0.8429
  推理时间: 1.8777秒

批次 96:
  奖励值: 125.2271
  收益率: 0.3151
  距离: 31.4915
  内存使用: 0.6172
  能量使用: 1.0369
  推理时间: 2.2457秒

批次 97:
  奖励值: 121.5213
  收益率: 0.3041
  距离: 31.7248
  内存使用: 0.6679
  能量使用: 0.9647
  推理时间: 2.1788秒

批次 98:
  奖励值: 119.7079
  收益率: 0.3075
  距离: 31.4883
  内存使用: 0.7079
  能量使用: 0.9881
  推理时间: 2.1399秒

批次 99:
  奖励值: 116.0154
  收益率: 0.2887
  距离: 28.4575
  内存使用: 0.6509
  能量使用: 0.8607
  推理时间: 2.0297秒

批次 100:
  奖励值: 119.1801
  收益率: 0.3029
  距离: 29.8247
  内存使用: 0.6652
  能量使用: 0.9500
  推理时间: 2.0708秒


==================== 总结 ====================
平均收益率: 0.2976
平均能量使用: 0.9409
平均推理时间: 2.1193秒
