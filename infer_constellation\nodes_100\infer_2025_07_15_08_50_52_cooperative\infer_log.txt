推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 30.9548
  收益率: 0.7894
  距离: 9.0346
  内存使用: -0.0063
  能量使用: 0.2621
  推理时间: 1.0288秒

批次 2:
  奖励值: 29.5860
  收益率: 0.7349
  距离: 8.8281
  内存使用: -0.0189
  能量使用: 0.2188
  推理时间: 0.6067秒

批次 3:
  奖励值: 29.9725
  收益率: 0.7560
  距离: 9.1615
  内存使用: 0.0068
  能量使用: 0.2868
  推理时间: 0.6073秒

批次 4:
  奖励值: 31.3776
  收益率: 0.7684
  距离: 7.9202
  内存使用: -0.0011
  能量使用: 0.3258
  推理时间: 0.6406秒

批次 5:
  奖励值: 28.7431
  收益率: 0.7432
  距离: 7.7100
  内存使用: -0.0638
  能量使用: 0.2676
  推理时间: 0.5614秒

批次 6:
  奖励值: 32.2812
  收益率: 0.7597
  距离: 9.7883
  内存使用: -0.0170
  能量使用: 0.2662
  推理时间: 0.6284秒

批次 7:
  奖励值: 28.6807
  收益率: 0.7238
  距离: 7.3713
  内存使用: -0.0358
  能量使用: 0.3032
  推理时间: 0.5637秒

批次 8:
  奖励值: 29.3690
  收益率: 0.7290
  距离: 8.8497
  内存使用: -0.0567
  能量使用: 0.2691
  推理时间: 0.5719秒

批次 9:
  奖励值: 28.4666
  收益率: 0.6946
  距离: 7.4710
  内存使用: -0.0691
  能量使用: 0.2650
  推理时间: 0.5414秒

批次 10:
  奖励值: 31.5930
  收益率: 0.7710
  距离: 9.6111
  内存使用: 0.2792
  能量使用: 0.3237
  推理时间: 0.6272秒

批次 11:
  奖励值: 31.2633
  收益率: 0.8102
  距离: 10.6169
  内存使用: -0.0596
  能量使用: 0.3161
  推理时间: 0.6611秒

批次 12:
  奖励值: 34.4302
  收益率: 0.8159
  距离: 8.8460
  内存使用: 0.0019
  能量使用: 0.2918
  推理时间: 0.6846秒

批次 13:
  奖励值: 35.0917
  收益率: 0.8347
  距离: 10.2898
  内存使用: 0.0204
  能量使用: 0.3065
  推理时间: 0.7675秒

批次 14:
  奖励值: 29.3309
  收益率: 0.7384
  距离: 7.7058
  内存使用: -0.0446
  能量使用: 0.2769
  推理时间: 0.5776秒

批次 15:
  奖励值: 31.0016
  收益率: 0.7526
  距离: 9.5293
  内存使用: -0.0173
  能量使用: 0.2849
  推理时间: 0.5938秒

批次 16:
  奖励值: 34.1056
  收益率: 0.8008
  距离: 8.6986
  内存使用: 0.0050
  能量使用: 0.2852
  推理时间: 0.6971秒

批次 17:
  奖励值: 31.5763
  收益率: 0.7682
  距离: 8.1092
  内存使用: 0.0065
  能量使用: 0.3317
  推理时间: 0.7013秒

批次 18:
  奖励值: 29.3420
  收益率: 0.7679
  距离: 9.8080
  内存使用: -0.0124
  能量使用: 0.3203
  推理时间: 0.6286秒

批次 19:
  奖励值: 32.1391
  收益率: 0.7681
  距离: 9.6254
  内存使用: -0.0168
  能量使用: 0.3131
  推理时间: 0.6477秒

批次 20:
  奖励值: 29.4869
  收益率: 0.7589
  距离: 8.7983
  内存使用: -0.0299
  能量使用: 0.2578
  推理时间: 0.5942秒

批次 21:
  奖励值: 30.6479
  收益率: 0.7426
  距离: 8.2522
  内存使用: -0.0427
  能量使用: 0.2947
  推理时间: 0.5917秒

批次 22:
  奖励值: 30.9681
  收益率: 0.7558
  距离: 8.9467
  内存使用: 0.0413
  能量使用: 0.2758
  推理时间: 0.6214秒

批次 23:
  奖励值: 30.0436
  收益率: 0.7179
  距离: 6.8529
  内存使用: -0.0082
  能量使用: 0.2681
  推理时间: 0.5864秒

批次 24:
  奖励值: 28.1434
  收益率: 0.7414
  距离: 8.2395
  内存使用: -0.0082
  能量使用: 0.2818
  推理时间: 0.7629秒

批次 25:
  奖励值: 33.0164
  收益率: 0.7691
  距离: 8.3245
  内存使用: -0.0514
  能量使用: 0.2941
  推理时间: 0.6933秒

批次 26:
  奖励值: 32.2996
  收益率: 0.8139
  距离: 10.2218
  内存使用: 0.0449
  能量使用: 0.3341
  推理时间: 0.7666秒

批次 27:
  奖励值: 33.0587
  收益率: 0.7848
  距离: 10.1634
  内存使用: 0.0252
  能量使用: 0.2940
  推理时间: 0.7233秒

批次 28:
  奖励值: 32.1342
  收益率: 0.7883
  距离: 8.4516
  内存使用: 0.0221
  能量使用: 0.2580
  推理时间: 0.7203秒

批次 29:
  奖励值: 30.5985
  收益率: 0.7617
  距离: 7.0374
  内存使用: -0.0819
  能量使用: 0.2477
  推理时间: 0.5970秒

批次 30:
  奖励值: 30.9927
  收益率: 0.7446
  距离: 6.5564
  内存使用: -0.0218
  能量使用: 0.2800
  推理时间: 0.5960秒

批次 31:
  奖励值: 34.1694
  收益率: 0.7869
  距离: 10.3529
  内存使用: 0.0059
  能量使用: 0.3024
  推理时间: 0.6725秒

批次 32:
  奖励值: 29.5394
  收益率: 0.7335
  距离: 9.4654
  内存使用: -0.0202
  能量使用: 0.2760
  推理时间: 0.5763秒

批次 33:
  奖励值: 26.4359
  收益率: 0.6624
  距离: 8.9716
  内存使用: -0.0875
  能量使用: 0.2442
  推理时间: 0.5060秒

批次 34:
  奖励值: 31.6181
  收益率: 0.7892
  距离: 9.9240
  内存使用: -0.0204
  能量使用: 0.2602
  推理时间: 0.6506秒

批次 35:
  奖励值: 31.1799
  收益率: 0.7648
  距离: 9.5592
  内存使用: -0.0031
  能量使用: 0.2842
  推理时间: 0.6140秒

批次 36:
  奖励值: 34.6296
  收益率: 0.7784
  距离: 8.5043
  内存使用: -0.0558
  能量使用: 0.2924
  推理时间: 0.6128秒

批次 37:
  奖励值: 27.0587
  收益率: 0.7464
  距离: 7.4926
  内存使用: -0.0723
  能量使用: 0.2624
  推理时间: 0.5539秒

批次 38:
  奖励值: 27.2824
  收益率: 0.7350
  距离: 9.1087
  内存使用: -0.0968
  能量使用: 0.2234
  推理时间: 0.5433秒

批次 39:
  奖励值: 32.2476
  收益率: 0.7821
  距离: 8.4697
  内存使用: -0.0360
  能量使用: 0.2711
  推理时间: 0.6005秒

批次 40:
  奖励值: 26.8631
  收益率: 0.6911
  距离: 8.2779
  内存使用: -0.0697
  能量使用: 0.2657
  推理时间: 0.5291秒

批次 41:
  奖励值: 29.6522
  收益率: 0.7715
  距离: 8.6445
  内存使用: -0.0247
  能量使用: 0.2758
  推理时间: 0.5818秒

批次 42:
  奖励值: 33.7138
  收益率: 0.8095
  距离: 10.5075
  内存使用: -0.0129
  能量使用: 0.2779
  推理时间: 0.7369秒

批次 43:
  奖励值: 31.6662
  收益率: 0.7792
  距离: 8.3650
  内存使用: 0.0181
  能量使用: 0.2845
  推理时间: 0.6541秒

批次 44:
  奖励值: 22.7738
  收益率: 0.5586
  距离: 7.7470
  内存使用: -0.1033
  能量使用: 0.2088
  推理时间: 0.4649秒

批次 45:
  奖励值: 30.4544
  收益率: 0.7619
  距离: 9.2842
  内存使用: -0.0320
  能量使用: 0.2390
  推理时间: 0.5887秒

批次 46:
  奖励值: 30.6554
  收益率: 0.7489
  距离: 7.6885
  内存使用: -0.0626
  能量使用: 0.2632
  推理时间: 0.5742秒

批次 47:
  奖励值: 29.3095
  收益率: 0.7517
  距离: 8.9613
  内存使用: -0.0654
  能量使用: 0.2851
  推理时间: 0.5916秒

批次 48:
  奖励值: 33.1409
  收益率: 0.8064
  距离: 8.9039
  内存使用: 0.3007
  能量使用: 0.3136
  推理时间: 0.7118秒

批次 49:
  奖励值: 33.6204
  收益率: 0.7663
  距离: 7.9841
  内存使用: -0.0597
  能量使用: 0.2349
  推理时间: 0.6042秒

批次 50:
  奖励值: 31.7417
  收益率: 0.7599
  距离: 9.0871
  内存使用: -0.0531
  能量使用: 0.2827
  推理时间: 0.6033秒

批次 51:
  奖励值: 31.1417
  收益率: 0.7630
  距离: 7.6383
  内存使用: -0.0067
  能量使用: 0.2850
  推理时间: 0.6219秒

批次 52:
  奖励值: 30.8837
  收益率: 0.7684
  距离: 8.9568
  内存使用: 0.0160
  能量使用: 0.2598
  推理时间: 0.6014秒

批次 53:
  奖励值: 33.3498
  收益率: 0.7954
  距离: 9.1807
  内存使用: 0.0209
  能量使用: 0.2987
  推理时间: 0.6519秒

批次 54:
  奖励值: 31.8577
  收益率: 0.7860
  距离: 7.9994
  内存使用: 0.0035
  能量使用: 0.2818
  推理时间: 0.6637秒

批次 55:
  奖励值: 32.3213
  收益率: 0.7743
  距离: 8.4877
  内存使用: -0.0059
  能量使用: 0.2755
  推理时间: 0.6319秒

批次 56:
  奖励值: 30.6346
  收益率: 0.7650
  距离: 7.4870
  内存使用: -0.0209
  能量使用: 0.2645
  推理时间: 0.6110秒

批次 57:
  奖励值: 28.1207
  收益率: 0.7295
  距离: 9.3070
  内存使用: -0.0274
  能量使用: 0.2791
  推理时间: 0.5709秒

批次 58:
  奖励值: 30.9524
  收益率: 0.7884
  距离: 8.6056
  内存使用: -0.0302
  能量使用: 0.3198
  推理时间: 0.6367秒

批次 59:
  奖励值: 36.4402
  收益率: 0.8261
  距离: 10.4206
  内存使用: -0.0002
  能量使用: 0.2991
  推理时间: 0.7008秒

批次 60:
  奖励值: 28.3518
  收益率: 0.7214
  距离: 9.8505
  内存使用: -0.0116
  能量使用: 0.2575
  推理时间: 0.5877秒

批次 61:
  奖励值: 30.5034
  收益率: 0.7664
  距离: 8.9147
  内存使用: -0.0115
  能量使用: 0.2901
  推理时间: 0.5820秒

批次 62:
  奖励值: 26.5940
  收益率: 0.7110
  距离: 7.6675
  内存使用: -0.0827
  能量使用: 0.2271
  推理时间: 0.5051秒

批次 63:
  奖励值: 29.0103
  收益率: 0.7579
  距离: 8.7301
  内存使用: -0.0237
  能量使用: 0.2730
  推理时间: 0.5856秒

批次 64:
  奖励值: 27.3999
  收益率: 0.7377
  距离: 7.9467
  内存使用: -0.0250
  能量使用: 0.2786
  推理时间: 0.5590秒

批次 65:
  奖励值: 35.1157
  收益率: 0.8180
  距离: 9.5391
  内存使用: -0.0266
  能量使用: 0.2561
  推理时间: 0.6872秒

批次 66:
  奖励值: 28.4046
  收益率: 0.7089
  距离: 10.1572
  内存使用: -0.0356
  能量使用: 0.2691
  推理时间: 0.5740秒

批次 67:
  奖励值: 31.7988
  收益率: 0.7763
  距离: 8.3064
  内存使用: -0.0000
  能量使用: 0.2961
  推理时间: 0.6043秒

批次 68:
  奖励值: 28.7266
  收益率: 0.7478
  距离: 9.3319
  内存使用: -0.0360
  能量使用: 0.2509
  推理时间: 0.5529秒

批次 69:
  奖励值: 29.9228
  收益率: 0.7610
  距离: 9.3096
  内存使用: -0.0313
  能量使用: 0.2761
  推理时间: 0.5832秒

批次 70:
  奖励值: 29.4813
  收益率: 0.7037
  距离: 9.2564
  内存使用: -0.0645
  能量使用: 0.2455
  推理时间: 0.5539秒

批次 71:
  奖励值: 29.8936
  收益率: 0.7532
  距离: 10.1717
  内存使用: -0.0391
  能量使用: 0.2803
  推理时间: 0.5857秒

批次 72:
  奖励值: 30.3155
  收益率: 0.7621
  距离: 8.3310
  内存使用: -0.0428
  能量使用: 0.2808
  推理时间: 0.5941秒

批次 73:
  奖励值: 37.4341
  收益率: 0.8588
  距离: 11.1201
  内存使用: 0.0363
  能量使用: 0.3755
  推理时间: 0.7545秒

批次 74:
  奖励值: 30.6640
  收益率: 0.7111
  距离: 7.8542
  内存使用: -0.0540
  能量使用: 0.2717
  推理时间: 0.5635秒

批次 75:
  奖励值: 28.2015
  收益率: 0.7330
  距离: 10.3535
  内存使用: -0.0328
  能量使用: 0.2764
  推理时间: 0.5908秒

批次 76:
  奖励值: 26.7046
  收益率: 0.7206
  距离: 8.7786
  内存使用: -0.0547
  能量使用: 0.2670
  推理时间: 0.5365秒

批次 77:
  奖励值: 32.0269
  收益率: 0.7566
  距离: 9.7173
  内存使用: -0.0285
  能量使用: 0.2574
  推理时间: 0.5955秒

批次 78:
  奖励值: 29.5366
  收益率: 0.7577
  距离: 9.5291
  内存使用: -0.0658
  能量使用: 0.2636
  推理时间: 0.6020秒

批次 79:
  奖励值: 28.7507
  收益率: 0.7658
  距离: 7.7025
  内存使用: -0.0444
  能量使用: 0.2405
  推理时间: 0.5928秒

批次 80:
  奖励值: 30.1999
  收益率: 0.7756
  距离: 9.1213
  内存使用: -0.0113
  能量使用: 0.2903
  推理时间: 0.6160秒

批次 81:
  奖励值: 23.8355
  收益率: 0.7007
  距离: 8.3659
  内存使用: -0.0750
  能量使用: 0.2381
  推理时间: 0.5184秒

批次 82:
  奖励值: 32.6600
  收益率: 0.7348
  距离: 6.9640
  内存使用: 0.0087
  能量使用: 0.3073
  推理时间: 0.6229秒

批次 83:
  奖励值: 33.7860
  收益率: 0.7879
  距离: 10.1113
  内存使用: 0.0011
  能量使用: 0.3156
  推理时间: 0.6312秒

批次 84:
  奖励值: 30.1543
  收益率: 0.7322
  距离: 9.4967
  内存使用: -0.0333
  能量使用: 0.2281
  推理时间: 0.5876秒

批次 85:
  奖励值: 30.9961
  收益率: 0.7343
  距离: 7.9933
  内存使用: -0.0570
  能量使用: 0.2715
  推理时间: 0.5647秒

批次 86:
  奖励值: 30.7341
  收益率: 0.7832
  距离: 9.0474
  内存使用: -0.0143
  能量使用: 0.2988
  推理时间: 0.6404秒

批次 87:
  奖励值: 29.5555
  收益率: 0.7539
  距离: 8.6326
  内存使用: -0.0406
  能量使用: 0.2970
  推理时间: 0.5964秒

批次 88:
  奖励值: 32.1801
  收益率: 0.7615
  距离: 8.2540
  内存使用: -0.0347
  能量使用: 0.2204
  推理时间: 0.6079秒

批次 89:
  奖励值: 30.0152
  收益率: 0.7768
  距离: 9.3696
  内存使用: -0.0184
  能量使用: 0.2184
  推理时间: 0.6082秒

批次 90:
  奖励值: 27.9658
  收益率: 0.7523
  距离: 9.4116
  内存使用: -0.0192
  能量使用: 0.2762
  推理时间: 0.5875秒

批次 91:
  奖励值: 29.5103
  收益率: 0.7359
  距离: 8.7605
  内存使用: -0.0509
  能量使用: 0.2697
  推理时间: 0.5850秒

批次 92:
  奖励值: 30.5794
  收益率: 0.7207
  距离: 8.8008
  内存使用: -0.0277
  能量使用: 0.2682
  推理时间: 0.5844秒

批次 93:
  奖励值: 34.5229
  收益率: 0.7725
  距离: 8.0659
  内存使用: 0.0038
  能量使用: 0.2845
  推理时间: 0.6536秒

批次 94:
  奖励值: 29.4337
  收益率: 0.7803
  距离: 10.4218
  内存使用: -0.0009
  能量使用: 0.3116
  推理时间: 0.6446秒

批次 95:
  奖励值: 30.3799
  收益率: 0.7178
  距离: 9.2382
  内存使用: -0.0319
  能量使用: 0.2340
  推理时间: 0.5844秒

批次 96:
  奖励值: 29.9260
  收益率: 0.7435
  距离: 8.9534
  内存使用: -0.0369
  能量使用: 0.2316
  推理时间: 0.5778秒

批次 97:
  奖励值: 27.8786
  收益率: 0.6871
  距离: 6.8165
  内存使用: -0.0210
  能量使用: 0.2472
  推理时间: 0.6039秒

批次 98:
  奖励值: 32.3137
  收益率: 0.7380
  距离: 8.8945
  内存使用: -0.0333
  能量使用: 0.3115
  推理时间: 0.6067秒

批次 99:
  奖励值: 27.5047
  收益率: 0.7341
  距离: 7.2036
  内存使用: -0.0750
  能量使用: 0.2620
  推理时间: 0.5640秒

批次 100:
  奖励值: 32.6647
  收益率: 0.7683
  距离: 10.0900
  内存使用: -0.0783
  能量使用: 0.2590
  推理时间: 0.6036秒


==================== 总结 ====================
平均收益率: 0.7561
平均能量使用: 0.2759
平均推理时间: 0.6163秒
