推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 104.9628
  收益率: 0.5024
  距离: 23.4106
  内存使用: 0.4902
  能量使用: 0.7736
  推理时间: 2.0099秒

批次 2:
  奖励值: 98.4430
  收益率: 0.4808
  距离: 24.5093
  内存使用: 0.5040
  能量使用: 0.7006
  推理时间: 2.1010秒

批次 3:
  奖励值: 98.5845
  收益率: 0.4952
  距离: 27.3472
  内存使用: 0.5040
  能量使用: 0.7798
  推理时间: 2.0452秒

批次 4:
  奖励值: 96.9714
  收益率: 0.4910
  距离: 28.6969
  内存使用: 0.5463
  能量使用: 0.8141
  推理时间: 1.9739秒

批次 5:
  奖励值: 84.8619
  收益率: 0.4316
  距离: 22.2847
  内存使用: 0.4743
  能量使用: 0.7172
  推理时间: 1.6522秒

批次 6:
  奖励值: 87.7130
  收益率: 0.4529
  距离: 24.1702
  内存使用: 0.4579
  能量使用: 0.7459
  推理时间: 1.7015秒

批次 7:
  奖励值: 100.1872
  收益率: 0.5134
  距离: 28.7974
  内存使用: 0.5755
  能量使用: 0.8526
  推理时间: 2.2319秒

批次 8:
  奖励值: 45.1713
  收益率: 0.2342
  距离: 12.6431
  内存使用: 0.4462
  能量使用: 0.3681
  推理时间: 1.0074秒

批次 9:
  奖励值: 88.6459
  收益率: 0.4642
  距离: 23.4197
  内存使用: 0.4919
  能量使用: 0.7966
  推理时间: 1.7738秒

批次 10:
  奖励值: 97.2539
  收益率: 0.4822
  距离: 26.7767
  内存使用: 0.5229
  能量使用: 0.7924
  推理时间: 1.8660秒

批次 11:
  奖励值: 108.2681
  收益率: 0.5303
  距离: 29.0056
  内存使用: 0.5952
  能量使用: 0.8630
  推理时间: 2.0823秒

批次 12:
  奖励值: 95.5131
  收益率: 0.4816
  距离: 27.2342
  内存使用: 0.5439
  能量使用: 0.7957
  推理时间: 2.0781秒

批次 13:
  奖励值: 91.7025
  收益率: 0.4774
  距离: 24.5670
  内存使用: 0.4916
  能量使用: 0.7363
  推理时间: 1.8601秒

批次 14:
  奖励值: 95.6873
  收益率: 0.4771
  距离: 25.7919
  内存使用: 0.5248
  能量使用: 0.7086
  推理时间: 1.9564秒

批次 15:
  奖励值: 104.9268
  收益率: 0.5265
  距离: 28.9676
  内存使用: 0.4950
  能量使用: 0.8839
  推理时间: 1.9806秒

批次 16:
  奖励值: 91.4824
  收益率: 0.4612
  距离: 22.7863
  内存使用: 0.4552
  能量使用: 0.7936
  推理时间: 1.7998秒

批次 17:
  奖励值: 88.6893
  收益率: 0.4396
  距离: 22.5361
  内存使用: 0.3932
  能量使用: 0.6921
  推理时间: 1.7048秒

批次 18:
  奖励值: 99.9646
  收益率: 0.5264
  距离: 28.2064
  内存使用: 0.5717
  能量使用: 0.8926
  推理时间: 2.0981秒

批次 19:
  奖励值: 100.8339
  收益率: 0.5015
  距离: 30.2823
  内存使用: 0.6113
  能量使用: 0.8386
  推理时间: 2.0589秒

批次 20:
  奖励值: 103.3026
  收益率: 0.5239
  距离: 28.3864
  内存使用: 0.5817
  能量使用: 0.8266
  推理时间: 2.0019秒

批次 21:
  奖励值: 100.1316
  收益率: 0.5064
  距离: 27.7299
  内存使用: 0.5383
  能量使用: 0.8685
  推理时间: 2.0139秒

批次 22:
  奖励值: 102.1352
  收益率: 0.5338
  距离: 29.7904
  内存使用: 0.6145
  能量使用: 0.9248
  推理时间: 2.1365秒

批次 23:
  奖励值: 108.1206
  收益率: 0.5436
  距离: 31.5619
  内存使用: 0.6408
  能量使用: 0.9560
  推理时间: 2.2052秒

批次 24:
  奖励值: 97.3462
  收益率: 0.5106
  距离: 26.5210
  内存使用: 0.4813
  能量使用: 0.9052
  推理时间: 1.9332秒

批次 25:
  奖励值: 100.5641
  收益率: 0.4983
  距离: 26.4616
  内存使用: 0.5632
  能量使用: 0.8470
  推理时间: 2.0142秒

批次 26:
  奖励值: 94.2943
  收益率: 0.4726
  距离: 25.9019
  内存使用: 0.5509
  能量使用: 0.7586
  推理时间: 1.8563秒

批次 27:
  奖励值: 98.7395
  收益率: 0.4932
  距离: 24.2481
  内存使用: 0.5920
  能量使用: 0.8808
  推理时间: 1.9986秒

批次 28:
  奖励值: 90.8065
  收益率: 0.4588
  距离: 25.5734
  内存使用: 0.4308
  能量使用: 0.7828
  推理时间: 1.8640秒

批次 29:
  奖励值: 89.4053
  收益率: 0.4542
  距离: 25.2184
  内存使用: 0.4325
  能量使用: 0.7384
  推理时间: 1.7971秒

批次 30:
  奖励值: 104.6291
  收益率: 0.5332
  距离: 30.0568
  内存使用: 0.5532
  能量使用: 0.9318
  推理时间: 2.1309秒

批次 31:
  奖励值: 108.6561
  收益率: 0.5487
  距离: 30.2980
  内存使用: 0.6204
  能量使用: 0.9695
  推理时间: 2.3222秒

批次 32:
  奖励值: 100.1321
  收益率: 0.4982
  距离: 24.4133
  内存使用: 0.5239
  能量使用: 0.7985
  推理时间: 1.9459秒

批次 33:
  奖励值: 105.9074
  收益率: 0.5318
  距离: 29.5591
  内存使用: 0.5469
  能量使用: 0.8791
  推理时间: 2.1904秒

批次 34:
  奖励值: 93.9594
  收益率: 0.4758
  距离: 24.9826
  内存使用: 0.5157
  能量使用: 0.7828
  推理时间: 1.7779秒

批次 35:
  奖励值: 95.8779
  收益率: 0.4975
  距离: 26.7054
  内存使用: 0.4980
  能量使用: 0.8408
  推理时间: 2.0941秒

批次 36:
  奖励值: 102.7711
  收益率: 0.5183
  距离: 31.2332
  内存使用: 0.5457
  能量使用: 0.8790
  推理时间: 2.2122秒

批次 37:
  奖励值: 99.7355
  收益率: 0.5029
  距离: 26.5234
  内存使用: 0.6083
  能量使用: 0.7973
  推理时间: 2.0629秒

批次 38:
  奖励值: 103.9257
  收益率: 0.5265
  距离: 30.5975
  内存使用: 0.6280
  能量使用: 0.9232
  推理时间: 2.2592秒

批次 39:
  奖励值: 109.4110
  收益率: 0.5452
  距离: 30.7068
  内存使用: 0.5689
  能量使用: 0.9022
  推理时间: 2.2158秒

批次 40:
  奖励值: 95.1264
  收益率: 0.5102
  距离: 28.3669
  内存使用: 0.5314
  能量使用: 0.8489
  推理时间: 2.0932秒

批次 41:
  奖励值: 95.8410
  收益率: 0.4852
  距离: 26.6736
  内存使用: 0.5061
  能量使用: 0.7382
  推理时间: 2.0104秒

批次 42:
  奖励值: 101.0570
  收益率: 0.5024
  距离: 26.7521
  内存使用: 0.4769
  能量使用: 0.8570
  推理时间: 2.0647秒

批次 43:
  奖励值: 103.1820
  收益率: 0.5113
  距离: 27.9748
  内存使用: 0.5270
  能量使用: 0.9341
  推理时间: 2.1145秒

批次 44:
  奖励值: 95.3263
  收益率: 0.4896
  距离: 28.5654
  内存使用: 0.4479
  能量使用: 0.7342
  推理时间: 1.8750秒

批次 45:
  奖励值: 107.3702
  收益率: 0.5149
  距离: 28.7525
  内存使用: 0.6311
  能量使用: 0.8759
  推理时间: 2.0635秒

批次 46:
  奖励值: 91.3814
  收益率: 0.4572
  距离: 24.2280
  内存使用: 0.4477
  能量使用: 0.7615
  推理时间: 1.8247秒

批次 47:
  奖励值: 88.4499
  收益率: 0.4516
  距离: 22.8294
  内存使用: 0.3837
  能量使用: 0.6871
  推理时间: 1.7527秒

批次 48:
  奖励值: 100.2261
  收益率: 0.5095
  距离: 28.9633
  内存使用: 0.5348
  能量使用: 0.8496
  推理时间: 2.0098秒

批次 49:
  奖励值: 92.3822
  收益率: 0.4616
  距离: 26.8390
  内存使用: 0.4587
  能量使用: 0.7378
  推理时间: 1.8366秒

批次 50:
  奖励值: 96.4874
  收益率: 0.4713
  距离: 24.3730
  内存使用: 0.4654
  能量使用: 0.7868
  推理时间: 1.9023秒

批次 51:
  奖励值: 90.9140
  收益率: 0.4615
  距离: 21.9753
  内存使用: 0.4711
  能量使用: 0.7257
  推理时间: 1.8160秒

批次 52:
  奖励值: 100.7414
  收益率: 0.5158
  距离: 28.2489
  内存使用: 0.5700
  能量使用: 0.8982
  推理时间: 2.0097秒

批次 53:
  奖励值: 102.1469
  收益率: 0.5231
  距离: 29.4547
  内存使用: 0.5702
  能量使用: 0.8813
  推理时间: 2.0501秒

批次 54:
  奖励值: 105.3997
  收益率: 0.5195
  距离: 27.7538
  内存使用: 0.5969
  能量使用: 0.8498
  推理时间: 2.2251秒

批次 55:
  奖励值: 105.2259
  收益率: 0.5268
  距离: 26.3175
  内存使用: 0.5658
  能量使用: 0.8648
  推理时间: 2.0721秒

批次 56:
  奖励值: 104.2885
  收益率: 0.5266
  距离: 29.8525
  内存使用: 0.5144
  能量使用: 0.8560
  推理时间: 2.0239秒

批次 57:
  奖励值: 91.7479
  收益率: 0.4691
  距离: 27.8922
  内存使用: 0.4967
  能量使用: 0.7153
  推理时间: 1.9963秒

批次 58:
  奖励值: 102.6853
  收益率: 0.5052
  距离: 25.6381
  内存使用: 0.5668
  能量使用: 0.8245
  推理时间: 2.0245秒

批次 59:
  奖励值: 90.2561
  收益率: 0.4629
  距离: 26.6310
  内存使用: 0.4688
  能量使用: 0.7880
  推理时间: 1.8718秒

批次 60:
  奖励值: 101.7127
  收益率: 0.5037
  距离: 27.5945
  内存使用: 0.5167
  能量使用: 0.8634
  推理时间: 2.1472秒

批次 61:
  奖励值: 104.0825
  收益率: 0.5183
  距离: 26.0241
  内存使用: 0.5526
  能量使用: 0.8583
  推理时间: 2.0716秒

批次 62:
  奖励值: 108.7739
  收益率: 0.5622
  距离: 29.3137
  内存使用: 0.5612
  能量使用: 0.9773
  推理时间: 2.2290秒

批次 63:
  奖励值: 99.8071
  收益率: 0.4867
  距离: 28.7310
  内存使用: 0.6028
  能量使用: 0.8520
  推理时间: 2.0702秒

批次 64:
  奖励值: 94.3260
  收益率: 0.4736
  距离: 25.4415
  内存使用: 0.3965
  能量使用: 0.7099
  推理时间: 1.8711秒

批次 65:
  奖励值: 100.8706
  收益率: 0.5064
  距离: 27.7276
  内存使用: 0.5684
  能量使用: 0.7629
  推理时间: 2.0910秒

批次 66:
  奖励值: 103.2124
  收益率: 0.5245
  距离: 29.5117
  内存使用: 0.5577
  能量使用: 0.9058
  推理时间: 2.1375秒

批次 67:
  奖励值: 93.1245
  收益率: 0.4637
  距离: 25.8624
  内存使用: 0.4877
  能量使用: 0.8010
  推理时间: 1.9632秒

批次 68:
  奖励值: 101.3444
  收益率: 0.5037
  距离: 27.5489
  内存使用: 0.5515
  能量使用: 0.8108
  推理时间: 2.0150秒

批次 69:
  奖励值: 100.9959
  收益率: 0.5193
  距离: 28.1510
  内存使用: 0.5811
  能量使用: 0.8762
  推理时间: 2.0803秒

批次 70:
  奖励值: 106.6238
  收益率: 0.5242
  距离: 25.9089
  内存使用: 0.5739
  能量使用: 0.8696
  推理时间: 2.0561秒

批次 71:
  奖励值: 96.9415
  收益率: 0.4832
  距离: 26.6391
  内存使用: 0.4933
  能量使用: 0.7846
  推理时间: 1.9664秒

批次 72:
  奖励值: 96.2006
  收益率: 0.5232
  距离: 27.4126
  内存使用: 0.5415
  能量使用: 0.8446
  推理时间: 1.9757秒

批次 73:
  奖励值: 88.5317
  收益率: 0.4456
  距离: 23.3461
  内存使用: 0.4794
  能量使用: 0.7111
  推理时间: 1.7368秒

批次 74:
  奖励值: 103.4380
  收益率: 0.5156
  距离: 29.3260
  内存使用: 0.6084
  能量使用: 0.7897
  推理时间: 2.1409秒

批次 75:
  奖励值: 98.2550
  收益率: 0.4884
  距离: 26.1399
  内存使用: 0.5149
  能量使用: 0.8353
  推理时间: 1.9659秒

批次 76:
  奖励值: 100.1712
  收益率: 0.5018
  距离: 25.0328
  内存使用: 0.5669
  能量使用: 0.8578
  推理时间: 2.3498秒

批次 77:
  奖励值: 102.5531
  收益率: 0.5215
  距离: 29.1280
  内存使用: 0.5293
  能量使用: 0.8365
  推理时间: 2.0762秒

批次 78:
  奖励值: 97.0727
  收益率: 0.4623
  距离: 23.6677
  内存使用: 0.5397
  能量使用: 0.8473
  推理时间: 1.9177秒

批次 79:
  奖励值: 98.2370
  收益率: 0.5011
  距离: 24.5195
  内存使用: 0.5508
  能量使用: 0.8556
  推理时间: 1.9677秒

批次 80:
  奖励值: 96.7893
  收益率: 0.5084
  距离: 24.1374
  内存使用: 0.4989
  能量使用: 0.7851
  推理时间: 1.9169秒

批次 81:
  奖励值: 103.8893
  收益率: 0.5165
  距离: 28.7916
  内存使用: 0.5422
  能量使用: 0.8876
  推理时间: 2.0346秒

批次 82:
  奖励值: 100.3750
  收益率: 0.5057
  距离: 29.2241
  内存使用: 0.5588
  能量使用: 0.8670
  推理时间: 1.9588秒

批次 83:
  奖励值: 92.7900
  收益率: 0.4654
  距离: 27.1385
  内存使用: 0.4827
  能量使用: 0.7987
  推理时间: 1.8239秒

批次 84:
  奖励值: 83.0462
  收益率: 0.4168
  距离: 22.2321
  内存使用: 0.3765
  能量使用: 0.6989
  推理时间: 1.6395秒

批次 85:
  奖励值: 84.6495
  收益率: 0.4263
  距离: 22.8771
  内存使用: 0.3429
  能量使用: 0.6812
  推理时间: 1.6870秒

批次 86:
  奖励值: 97.0416
  收益率: 0.4817
  距离: 25.8243
  内存使用: 0.4813
  能量使用: 0.8112
  推理时间: 1.9258秒

批次 87:
  奖励值: 89.6949
  收益率: 0.4640
  距离: 22.9575
  内存使用: 0.5470
  能量使用: 0.7057
  推理时间: 2.2755秒

批次 88:
  奖励值: 100.0346
  收益率: 0.5203
  距离: 26.0776
  内存使用: 0.5472
  能量使用: 0.9059
  推理时间: 2.0510秒

批次 89:
  奖励值: 93.7544
  收益率: 0.4794
  距离: 24.8000
  内存使用: 0.4835
  能量使用: 0.8078
  推理时间: 1.8669秒

批次 90:
  奖励值: 99.3608
  收益率: 0.5128
  距离: 29.0496
  内存使用: 0.5462
  能量使用: 0.8827
  推理时间: 2.1345秒

批次 91:
  奖励值: 97.8405
  收益率: 0.4888
  距离: 25.5225
  内存使用: 0.5296
  能量使用: 0.8670
  推理时间: 2.0817秒

批次 92:
  奖励值: 94.4091
  收益率: 0.4915
  距离: 24.8316
  内存使用: 0.5305
  能量使用: 0.8198
  推理时间: 2.0333秒

批次 93:
  奖励值: 91.3185
  收益率: 0.4574
  距离: 21.4028
  内存使用: 0.3729
  能量使用: 0.7873
  推理时间: 1.7925秒

批次 94:
  奖励值: 98.6262
  收益率: 0.5300
  距离: 28.9509
  内存使用: 0.5351
  能量使用: 0.8617
  推理时间: 2.4425秒

批次 95:
  奖励值: 63.5274
  收益率: 0.3202
  距离: 18.0196
  内存使用: 0.5547
  能量使用: 0.5709
  推理时间: 1.3108秒

批次 96:
  奖励值: 94.3543
  收益率: 0.4894
  距离: 25.9923
  内存使用: 0.4746
  能量使用: 0.8245
  推理时间: 1.9788秒

批次 97:
  奖励值: 99.6324
  收益率: 0.4963
  距离: 27.7897
  内存使用: 0.5638
  能量使用: 0.7817
  推理时间: 1.9471秒

批次 98:
  奖励值: 93.9531
  收益率: 0.5063
  距离: 28.7356
  内存使用: 0.5380
  能量使用: 0.8202
  推理时间: 2.1944秒

批次 99:
  奖励值: 112.1007
  收益率: 0.5568
  距离: 29.0667
  内存使用: 0.5968
  能量使用: 0.9350
  推理时间: 2.1970秒

批次 100:
  奖励值: 93.6494
  收益率: 0.4617
  距离: 25.0497
  内存使用: 0.4739
  能量使用: 0.7776
  推理时间: 1.9034秒


==================== 总结 ====================
平均收益率: 0.4914
平均能量使用: 0.8148
平均推理时间: 1.9868秒
