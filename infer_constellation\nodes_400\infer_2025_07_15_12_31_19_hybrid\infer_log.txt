推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 88.8748
  收益率: 0.5497
  距离: 26.8768
  内存使用: 0.4422
  能量使用: 0.7315
  推理时间: 1.7574秒

批次 2:
  奖励值: 78.4111
  收益率: 0.4975
  距离: 21.7558
  内存使用: 0.3514
  能量使用: 0.6707
  推理时间: 1.5591秒

批次 3:
  奖励值: 72.2750
  收益率: 0.4752
  距离: 19.1131
  内存使用: 0.3401
  能量使用: 0.5968
  推理时间: 1.3847秒

批次 4:
  奖励值: 76.4386
  收益率: 0.4858
  距离: 21.2210
  内存使用: 0.3013
  能量使用: 0.6004
  推理时间: 1.4377秒

批次 5:
  奖励值: 88.3892
  收益率: 0.5515
  距离: 22.2060
  内存使用: 0.4495
  能量使用: 0.7662
  推理时间: 1.7452秒

批次 6:
  奖励值: 91.5520
  收益率: 0.5471
  距离: 23.6423
  内存使用: 0.4582
  能量使用: 0.7955
  推理时间: 1.7386秒

批次 7:
  奖励值: 79.9837
  收益率: 0.5123
  距离: 21.8312
  内存使用: 0.3993
  能量使用: 0.6021
  推理时间: 1.5520秒

批次 8:
  奖励值: 85.4559
  收益率: 0.5280
  距离: 23.7234
  内存使用: 0.4116
  能量使用: 0.7532
  推理时间: 1.6550秒

批次 9:
  奖励值: 77.9614
  收益率: 0.4969
  距离: 21.4616
  内存使用: 0.3646
  能量使用: 0.6511
  推理时间: 1.5299秒

批次 10:
  奖励值: 82.2614
  收益率: 0.5161
  距离: 23.1807
  内存使用: 0.4427
  能量使用: 0.6537
  推理时间: 1.5972秒

批次 11:
  奖励值: 84.2996
  收益率: 0.5119
  距离: 24.0502
  内存使用: 0.4623
  能量使用: 0.6882
  推理时间: 1.5975秒

批次 12:
  奖励值: 79.6467
  收益率: 0.5052
  距离: 24.7642
  内存使用: 0.2926
  能量使用: 0.6498
  推理时间: 1.6124秒

批次 13:
  奖励值: 85.0146
  收益率: 0.5416
  距离: 25.5233
  内存使用: 0.3887
  能量使用: 0.6588
  推理时间: 1.6531秒

批次 14:
  奖励值: 85.1394
  收益率: 0.5318
  距离: 22.8349
  内存使用: 0.3787
  能量使用: 0.7075
  推理时间: 1.6396秒

批次 15:
  奖励值: 82.1788
  收益率: 0.5329
  距离: 25.5522
  内存使用: 0.3908
  能量使用: 0.6995
  推理时间: 1.6041秒

批次 16:
  奖励值: 77.8688
  收益率: 0.5152
  距离: 22.6711
  内存使用: 0.3210
  能量使用: 0.6703
  推理时间: 1.4787秒

批次 17:
  奖励值: 83.5441
  收益率: 0.5168
  距离: 23.1012
  内存使用: 0.3586
  能量使用: 0.6521
  推理时间: 1.6155秒

批次 18:
  奖励值: 75.4419
  收益率: 0.5061
  距离: 23.1935
  内存使用: 0.2867
  能量使用: 0.6361
  推理时间: 1.4417秒

批次 19:
  奖励值: 77.7525
  收益率: 0.4809
  距离: 22.1154
  内存使用: 0.3413
  能量使用: 0.6382
  推理时间: 1.4900秒

批次 20:
  奖励值: 76.2615
  收益率: 0.4957
  距离: 18.1411
  内存使用: 0.3044
  能量使用: 0.6315
  推理时间: 1.4106秒

批次 21:
  奖励值: 82.6927
  收益率: 0.5345
  距离: 23.9161
  内存使用: 0.4164
  能量使用: 0.6821
  推理时间: 1.6586秒

批次 22:
  奖励值: 78.4162
  收益率: 0.4846
  距离: 21.0102
  内存使用: 0.3352
  能量使用: 0.6221
  推理时间: 1.4886秒

批次 23:
  奖励值: 89.8997
  收益率: 0.5496
  距离: 24.0145
  内存使用: 0.4455
  能量使用: 0.7405
  推理时间: 1.6849秒

批次 24:
  奖励值: 84.3750
  收益率: 0.5234
  距离: 20.1688
  内存使用: 0.3681
  能量使用: 0.7131
  推理时间: 1.5807秒

批次 25:
  奖励值: 79.5205
  收益率: 0.4983
  距离: 21.0300
  内存使用: 0.2959
  能量使用: 0.6331
  推理时间: 1.5265秒

批次 26:
  奖励值: 82.2825
  收益率: 0.5108
  距离: 19.4295
  内存使用: 0.3436
  能量使用: 0.6619
  推理时间: 1.5625秒

批次 27:
  奖励值: 90.9701
  收益率: 0.5478
  距离: 21.9322
  内存使用: 0.4118
  能量使用: 0.7159
  推理时间: 1.7340秒

批次 28:
  奖励值: 73.7334
  收益率: 0.4817
  距离: 20.1661
  内存使用: 0.2914
  能量使用: 0.6757
  推理时间: 1.3694秒

批次 29:
  奖励值: 77.6583
  收益率: 0.5017
  距离: 23.5076
  内存使用: 0.3397
  能量使用: 0.6360
  推理时间: 1.5085秒

批次 30:
  奖励值: 92.2790
  收益率: 0.5590
  距离: 25.3876
  内存使用: 0.4672
  能量使用: 0.7872
  推理时间: 1.8174秒

批次 31:
  奖励值: 79.9417
  收益率: 0.5058
  距离: 21.2746
  内存使用: 0.3992
  能量使用: 0.6429
  推理时间: 1.5463秒

批次 32:
  奖励值: 90.2371
  收益率: 0.5549
  距离: 23.5770
  内存使用: 0.4207
  能量使用: 0.7281
  推理时间: 1.7185秒

批次 33:
  奖励值: 75.4044
  收益率: 0.4799
  距离: 20.2870
  内存使用: 0.2938
  能量使用: 0.6104
  推理时间: 1.4560秒

批次 34:
  奖励值: 80.1675
  收益率: 0.5061
  距离: 21.8475
  内存使用: 0.3954
  能量使用: 0.6222
  推理时间: 1.5402秒

批次 35:
  奖励值: 83.9587
  收益率: 0.5221
  距离: 22.0914
  内存使用: 0.4461
  能量使用: 0.7451
  推理时间: 1.6463秒

批次 36:
  奖励值: 84.7152
  收益率: 0.5304
  距离: 21.2699
  内存使用: 0.3950
  能量使用: 0.7047
  推理时间: 1.6509秒

批次 37:
  奖励值: 80.4914
  收益率: 0.5260
  距离: 21.7639
  内存使用: 0.3756
  能量使用: 0.6777
  推理时间: 1.5855秒

批次 38:
  奖励值: 81.6209
  收益率: 0.5145
  距离: 23.4864
  内存使用: 0.3671
  能量使用: 0.7047
  推理时间: 1.6112秒

批次 39:
  奖励值: 77.2553
  收益率: 0.4978
  距离: 21.6894
  内存使用: 0.3516
  能量使用: 0.6484
  推理时间: 1.5461秒

批次 40:
  奖励值: 72.2911
  收益率: 0.4706
  距离: 21.0791
  内存使用: 0.3296
  能量使用: 0.6091
  推理时间: 1.4438秒

批次 41:
  奖励值: 73.5978
  收益率: 0.4791
  距离: 21.2965
  内存使用: 0.2817
  能量使用: 0.6262
  推理时间: 1.4760秒

批次 42:
  奖励值: 89.1957
  收益率: 0.5310
  距离: 21.0972
  内存使用: 0.3936
  能量使用: 0.7178
  推理时间: 1.6986秒

批次 43:
  奖励值: 95.7739
  收益率: 0.5775
  距离: 23.9936
  内存使用: 0.5045
  能量使用: 0.7540
  推理时间: 1.8439秒

批次 44:
  奖励值: 90.0452
  收益率: 0.5718
  距离: 24.0771
  内存使用: 0.4516
  能量使用: 0.7809
  推理时间: 1.7519秒

批次 45:
  奖励值: 75.6295
  收益率: 0.4802
  距离: 19.9466
  内存使用: 0.3031
  能量使用: 0.6913
  推理时间: 1.4657秒

批次 46:
  奖励值: 89.1434
  收益率: 0.5524
  距离: 22.8559
  内存使用: 0.4569
  能量使用: 0.8005
  推理时间: 1.7285秒

批次 47:
  奖励值: 78.0203
  收益率: 0.4992
  距离: 22.5809
  内存使用: 0.3583
  能量使用: 0.6202
  推理时间: 1.4984秒

批次 48:
  奖励值: 85.0709
  收益率: 0.5212
  距离: 20.7654
  内存使用: 0.4044
  能量使用: 0.6883
  推理时间: 1.5743秒

批次 49:
  奖励值: 76.9697
  收益率: 0.5057
  距离: 22.8614
  内存使用: 0.3658
  能量使用: 0.6334
  推理时间: 1.5210秒

批次 50:
  奖励值: 83.7449
  收益率: 0.5118
  距离: 22.0409
  内存使用: 0.3967
  能量使用: 0.6726
  推理时间: 1.6402秒

批次 51:
  奖励值: 81.5025
  收益率: 0.5110
  距离: 21.5749
  内存使用: 0.3593
  能量使用: 0.6608
  推理时间: 1.5536秒

批次 52:
  奖励值: 76.7463
  收益率: 0.5014
  距离: 21.1604
  内存使用: 0.3342
  能量使用: 0.6129
  推理时间: 1.5283秒

批次 53:
  奖励值: 77.7182
  收益率: 0.5074
  距离: 22.1729
  内存使用: 0.3530
  能量使用: 0.6378
  推理时间: 1.5200秒

批次 54:
  奖励值: 83.0251
  收益率: 0.5075
  距离: 21.4096
  内存使用: 0.4398
  能量使用: 0.7004
  推理时间: 1.5590秒

批次 55:
  奖励值: 84.0554
  收益率: 0.5003
  距离: 20.5637
  内存使用: 0.4209
  能量使用: 0.7046
  推理时间: 1.5808秒

批次 56:
  奖励值: 79.4939
  收益率: 0.5017
  距离: 19.5783
  内存使用: 0.3563
  能量使用: 0.6777
  推理时间: 1.5069秒

批次 57:
  奖励值: 85.5805
  收益率: 0.5440
  距离: 25.5223
  内存使用: 0.3975
  能量使用: 0.7193
  推理时间: 1.6812秒

批次 58:
  奖励值: 76.5697
  收益率: 0.4952
  距离: 19.9348
  内存使用: 0.3141
  能量使用: 0.6116
  推理时间: 1.4115秒

批次 59:
  奖励值: 83.1908
  收益率: 0.5393
  距离: 24.7457
  内存使用: 0.3966
  能量使用: 0.6761
  推理时间: 1.6324秒

批次 60:
  奖励值: 79.0575
  收益率: 0.4892
  距离: 18.0960
  内存使用: 0.3270
  能量使用: 0.6620
  推理时间: 1.4583秒

批次 61:
  奖励值: 85.1334
  收益率: 0.5337
  距离: 25.2508
  内存使用: 0.3938
  能量使用: 0.6886
  推理时间: 1.6501秒

批次 62:
  奖励值: 85.1276
  收益率: 0.5196
  距离: 21.8440
  内存使用: 0.4079
  能量使用: 0.6928
  推理时间: 1.6272秒

批次 63:
  奖励值: 88.2136
  收益率: 0.5376
  距离: 25.1345
  内存使用: 0.4841
  能量使用: 0.7544
  推理时间: 1.7221秒

批次 64:
  奖励值: 75.6133
  收益率: 0.4895
  距离: 19.6999
  内存使用: 0.2872
  能量使用: 0.5863
  推理时间: 1.4227秒

批次 65:
  奖励值: 86.5168
  收益率: 0.5511
  距离: 23.3207
  内存使用: 0.3618
  能量使用: 0.6840
  推理时间: 1.6231秒

批次 66:
  奖励值: 91.6223
  收益率: 0.5564
  距离: 23.7906
  内存使用: 0.4458
  能量使用: 0.7190
  推理时间: 1.7344秒

批次 67:
  奖励值: 93.6041
  收益率: 0.5545
  距离: 24.2410
  内存使用: 0.4012
  能量使用: 0.7495
  推理时间: 1.7419秒

批次 68:
  奖励值: 80.3056
  收益率: 0.4823
  距离: 18.6454
  内存使用: 0.3092
  能量使用: 0.6342
  推理时间: 1.4515秒

批次 69:
  奖励值: 78.8938
  收益率: 0.5160
  距离: 22.1110
  内存使用: 0.3738
  能量使用: 0.7275
  推理时间: 1.7514秒

批次 70:
  奖励值: 78.6717
  收益率: 0.5011
  距离: 21.1935
  内存使用: 0.4004
  能量使用: 0.6626
  推理时间: 1.5743秒

批次 71:
  奖励值: 87.8822
  收益率: 0.5312
  距离: 18.9290
  内存使用: 0.4131
  能量使用: 0.6935
  推理时间: 1.6333秒

批次 72:
  奖励值: 86.7908
  收益率: 0.5159
  距离: 23.0500
  内存使用: 0.4152
  能量使用: 0.7532
  推理时间: 1.6405秒

批次 73:
  奖励值: 81.1354
  收益率: 0.5024
  距离: 22.4913
  内存使用: 0.3476
  能量使用: 0.6902
  推理时间: 1.5519秒

批次 74:
  奖励值: 68.7201
  收益率: 0.4554
  距离: 20.0687
  内存使用: 0.2660
  能量使用: 0.6251
  推理时间: 1.3652秒

批次 75:
  奖励值: 84.6346
  收益率: 0.5198
  距离: 24.1330
  内存使用: 0.3555
  能量使用: 0.6424
  推理时间: 1.6297秒

批次 76:
  奖励值: 79.5312
  收益率: 0.4921
  距离: 20.9653
  内存使用: 0.3340
  能量使用: 0.6384
  推理时间: 1.5573秒

批次 77:
  奖励值: 84.8371
  收益率: 0.5239
  距离: 22.5401
  内存使用: 0.3316
  能量使用: 0.6541
  推理时间: 1.5698秒

批次 78:
  奖励值: 84.2015
  收益率: 0.5279
  距离: 21.9695
  内存使用: 0.3582
  能量使用: 0.6694
  推理时间: 1.6095秒

批次 79:
  奖励值: 85.2571
  收益率: 0.5250
  距离: 23.5311
  内存使用: 0.4424
  能量使用: 0.7123
  推理时间: 1.6678秒

批次 80:
  奖励值: 82.7832
  收益率: 0.5301
  距离: 25.1149
  内存使用: 0.4583
  能量使用: 0.6604
  推理时间: 1.5941秒

批次 81:
  奖励值: 81.5531
  收益率: 0.5115
  距离: 22.8355
  内存使用: 0.4297
  能量使用: 0.6287
  推理时间: 1.6917秒

批次 82:
  奖励值: 84.5269
  收益率: 0.5390
  距离: 22.0803
  内存使用: 0.4100
  能量使用: 0.6559
  推理时间: 1.7346秒

批次 83:
  奖励值: 78.8545
  收益率: 0.4918
  距离: 21.3362
  内存使用: 0.3232
  能量使用: 0.6316
  推理时间: 1.5710秒

批次 84:
  奖励值: 82.4685
  收益率: 0.5378
  距离: 23.3585
  内存使用: 0.3726
  能量使用: 0.7318
  推理时间: 1.7286秒

批次 85:
  奖励值: 83.5717
  收益率: 0.5278
  距离: 23.5926
  内存使用: 0.3919
  能量使用: 0.6721
  推理时间: 1.7209秒

批次 86:
  奖励值: 73.2690
  收益率: 0.4697
  距离: 19.7346
  内存使用: 0.2917
  能量使用: 0.6358
  推理时间: 1.5149秒

批次 87:
  奖励值: 86.9380
  收益率: 0.5314
  距离: 19.8240
  内存使用: 0.4005
  能量使用: 0.6995
  推理时间: 1.6186秒

批次 88:
  奖励值: 84.1890
  收益率: 0.5169
  距离: 20.1382
  内存使用: 0.3638
  能量使用: 0.7200
  推理时间: 1.6300秒

批次 89:
  奖励值: 84.6920
  收益率: 0.5224
  距离: 22.7508
  内存使用: 0.4163
  能量使用: 0.6489
  推理时间: 1.6060秒

批次 90:
  奖励值: 80.0077
  收益率: 0.5005
  距离: 22.5459
  内存使用: 0.3696
  能量使用: 0.6690
  推理时间: 1.5900秒

批次 91:
  奖励值: 81.1307
  收益率: 0.5328
  距离: 25.1414
  内存使用: 0.4283
  能量使用: 0.7689
  推理时间: 1.6549秒

批次 92:
  奖励值: 77.1482
  收益率: 0.4992
  距离: 21.5051
  内存使用: 0.3582
  能量使用: 0.6403
  推理时间: 1.4569秒

批次 93:
  奖励值: 81.7474
  收益率: 0.5118
  距离: 22.1811
  内存使用: 0.3127
  能量使用: 0.6114
  推理时间: 1.5681秒

批次 94:
  奖励值: 87.6842
  收益率: 0.5338
  距离: 23.7970
  内存使用: 0.4662
  能量使用: 0.7683
  推理时间: 1.7101秒

批次 95:
  奖励值: 83.0406
  收益率: 0.5111
  距离: 21.9153
  内存使用: 0.3331
  能量使用: 0.6704
  推理时间: 1.5630秒

批次 96:
  奖励值: 77.8338
  收益率: 0.5017
  距离: 23.0047
  内存使用: 0.3782
  能量使用: 0.5494
  推理时间: 1.4800秒

批次 97:
  奖励值: 87.9200
  收益率: 0.5382
  距离: 23.2408
  内存使用: 0.4193
  能量使用: 0.7000
  推理时间: 1.6631秒

批次 98:
  奖励值: 81.9593
  收益率: 0.5191
  距离: 22.4340
  内存使用: 0.4036
  能量使用: 0.6439
  推理时间: 1.5642秒

批次 99:
  奖励值: 77.2814
  收益率: 0.5071
  距离: 22.1720
  内存使用: 0.3960
  能量使用: 0.6692
  推理时间: 1.5421秒

批次 100:
  奖励值: 86.4728
  收益率: 0.5332
  距离: 21.8849
  内存使用: 0.4050
  能量使用: 0.7645
  推理时间: 1.6174秒


==================== 总结 ====================
平均收益率: 0.5163
平均能量使用: 0.6778
平均推理时间: 1.5915秒
