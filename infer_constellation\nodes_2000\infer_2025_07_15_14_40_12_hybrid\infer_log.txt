推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 144.1827
  收益率: 0.1821
  距离: 35.5469
  内存使用: 0.7811
  能量使用: 1.1066
  推理时间: 2.7426秒

批次 2:
  奖励值: 146.5428
  收益率: 0.1825
  距离: 32.3929
  内存使用: 0.8259
  能量使用: 1.0956
  推理时间: 2.6272秒

批次 3:
  奖励值: 137.6853
  收益率: 0.1713
  距离: 32.0963
  内存使用: 0.7611
  能量使用: 0.9720
  推理时间: 2.5724秒

批次 4:
  奖励值: 107.5108
  收益率: 0.1319
  距离: 24.8887
  内存使用: 0.9121
  能量使用: 0.7775
  推理时间: 1.9498秒

批次 5:
  奖励值: 141.3594
  收益率: 0.1821
  距离: 38.6101
  内存使用: 0.7565
  能量使用: 1.1146
  推理时间: 2.7620秒

批次 6:
  奖励值: 118.0637
  收益率: 0.1479
  距离: 30.4571
  内存使用: 0.9095
  能量使用: 0.9058
  推理时间: 2.2635秒

批次 7:
  奖励值: 137.0162
  收益率: 0.1720
  距离: 37.0302
  内存使用: 0.7586
  能量使用: 0.9787
  推理时间: 2.5401秒

批次 8:
  奖励值: 120.9689
  收益率: 0.1510
  距离: 29.9711
  内存使用: 0.6276
  能量使用: 0.8635
  推理时间: 2.1763秒

批次 9:
  奖励值: 130.9225
  收益率: 0.1634
  距离: 35.2790
  内存使用: 0.6992
  能量使用: 0.9768
  推理时间: 2.3994秒

批次 10:
  奖励值: 116.3639
  收益率: 0.1463
  距离: 28.9952
  内存使用: 0.6241
  能量使用: 0.8529
  推理时间: 2.2619秒

批次 11:
  奖励值: 143.0896
  收益率: 0.1826
  距离: 35.3951
  内存使用: 0.7827
  能量使用: 1.0430
  推理时间: 2.6347秒

批次 12:
  奖励值: 125.0783
  收益率: 0.1539
  距离: 24.6962
  内存使用: 0.6400
  能量使用: 0.9071
  推理时间: 2.2267秒

批次 13:
  奖励值: 141.2664
  收益率: 0.1756
  距离: 32.9592
  内存使用: 0.7817
  能量使用: 1.1261
  推理时间: 2.6571秒

批次 14:
  奖励值: 144.1609
  收益率: 0.1809
  距离: 34.6080
  内存使用: 0.7773
  能量使用: 1.0929
  推理时间: 2.6003秒

批次 15:
  奖励值: 136.1337
  收益率: 0.1686
  距离: 31.0201
  内存使用: 0.7088
  能量使用: 1.0036
  推理时间: 2.4403秒

批次 16:
  奖励值: 121.6177
  收益率: 0.1495
  距离: 27.2524
  内存使用: 0.6470
  能量使用: 0.8651
  推理时间: 2.2265秒

批次 17:
  奖励值: 127.2154
  收益率: 0.1596
  距离: 32.1561
  内存使用: 0.6323
  能量使用: 0.9807
  推理时间: 2.3951秒

批次 18:
  奖励值: 114.9047
  收益率: 0.1450
  距离: 29.5499
  内存使用: 0.9154
  能量使用: 0.9252
  推理时间: 2.2142秒

批次 19:
  奖励值: 118.2847
  收益率: 0.1509
  距离: 32.0051
  内存使用: 0.9183
  能量使用: 0.9072
  推理时间: 2.2379秒

批次 20:
  奖励值: 119.8945
  收益率: 0.1525
  距离: 30.1518
  内存使用: 0.9110
  能量使用: 0.9483
  推理时间: 2.2733秒

批次 21:
  奖励值: 115.5011
  收益率: 0.1465
  距离: 27.2279
  内存使用: 0.9113
  能量使用: 0.8586
  推理时间: 2.0715秒

批次 22:
  奖励值: 115.6896
  收益率: 0.1483
  距离: 29.4343
  内存使用: 0.9076
  能量使用: 0.8789
  推理时间: 2.2046秒

批次 23:
  奖励值: 128.8028
  收益率: 0.1611
  距离: 33.9402
  内存使用: 0.6957
  能量使用: 0.9585
  推理时间: 2.3979秒

批次 24:
  奖励值: 120.6976
  收益率: 0.1499
  距离: 29.1727
  内存使用: 0.9087
  能量使用: 0.9233
  推理时间: 2.2589秒

批次 25:
  奖励值: 116.4957
  收益率: 0.1442
  距离: 25.6846
  内存使用: 0.6108
  能量使用: 0.9132
  推理时间: 2.1111秒

批次 26:
  奖励值: 120.7077
  收益率: 0.1544
  距离: 30.5977
  内存使用: 0.9087
  能量使用: 0.8780
  推理时间: 2.2144秒

批次 27:
  奖励值: 140.9460
  收益率: 0.1756
  距离: 33.8088
  内存使用: 0.7947
  能量使用: 1.1045
  推理时间: 2.5524秒

批次 28:
  奖励值: 121.6810
  收益率: 0.1496
  距离: 28.9533
  内存使用: 0.9089
  能量使用: 0.9758
  推理时间: 2.2713秒

批次 29:
  奖励值: 147.5238
  收益率: 0.1854
  距离: 35.5440
  内存使用: 0.7337
  能量使用: 1.0745
  推理时间: 2.6527秒

批次 30:
  奖励值: 134.8179
  收益率: 0.1691
  距离: 34.9616
  内存使用: 0.7479
  能量使用: 1.0547
  推理时间: 2.5135秒

批次 31:
  奖励值: 119.6719
  收益率: 0.1520
  距离: 31.4128
  内存使用: 0.9079
  能量使用: 0.9355
  推理时间: 2.3702秒

批次 32:
  奖励值: 111.7247
  收益率: 0.1415
  距离: 30.7238
  内存使用: 0.9086
  能量使用: 0.9183
  推理时间: 2.1673秒

批次 33:
  奖励值: 132.9140
  收益率: 0.1648
  距离: 29.5808
  内存使用: 0.6834
  能量使用: 0.9079
  推理时间: 2.4757秒

批次 34:
  奖励值: 142.6218
  收益率: 0.1783
  距离: 36.5382
  内存使用: 0.7617
  能量使用: 1.0564
  推理时间: 2.7292秒

批次 35:
  奖励值: 117.0137
  收益率: 0.1458
  距离: 31.9434
  内存使用: 0.9175
  能量使用: 0.9115
  推理时间: 2.1488秒

批次 36:
  奖励值: 142.9594
  收益率: 0.1741
  距离: 29.3505
  内存使用: 0.7050
  能量使用: 1.0256
  推理时间: 2.5942秒

批次 37:
  奖励值: 117.6734
  收益率: 0.1517
  距离: 32.7574
  内存使用: 0.9170
  能量使用: 0.9788
  推理时间: 2.1928秒

批次 38:
  奖励值: 109.4490
  收益率: 0.1417
  距离: 29.5361
  内存使用: 0.9117
  能量使用: 0.8541
  推理时间: 2.0957秒

批次 39:
  奖励值: 119.4862
  收益率: 0.1513
  距离: 30.0195
  内存使用: 0.9091
  能量使用: 0.8990
  推理时间: 2.3090秒

批次 40:
  奖励值: 118.7722
  收益率: 0.1479
  距离: 33.1953
  内存使用: 0.9154
  能量使用: 0.9448
  推理时间: 2.1692秒

批次 41:
  奖励值: 118.9013
  收益率: 0.1487
  距离: 29.9593
  内存使用: 0.9070
  能量使用: 0.9311
  推理时间: 2.1714秒

批次 42:
  奖励值: 125.5849
  收益率: 0.1584
  距离: 34.2944
  内存使用: 0.7324
  能量使用: 1.0035
  推理时间: 2.2935秒

批次 43:
  奖励值: 130.4655
  收益率: 0.1609
  距离: 30.6213
  内存使用: 0.6552
  能量使用: 0.9924
  推理时间: 2.4350秒

批次 44:
  奖励值: 143.5182
  收益率: 0.1787
  距离: 33.9244
  内存使用: 0.7501
  能量使用: 1.0891
  推理时间: 2.9373秒

批次 45:
  奖励值: 119.1067
  收益率: 0.1471
  距离: 26.4303
  内存使用: 0.9137
  能量使用: 0.8424
  推理时间: 2.1070秒

批次 46:
  奖励值: 133.0301
  收益率: 0.1719
  距离: 33.7438
  内存使用: 0.7524
  能量使用: 1.0437
  推理时间: 2.4673秒

批次 47:
  奖励值: 123.8596
  收益率: 0.1533
  距离: 29.6242
  内存使用: 0.9112
  能量使用: 0.9454
  推理时间: 2.2829秒

批次 48:
  奖励值: 146.3198
  收益率: 0.1802
  距离: 32.0408
  内存使用: 0.8017
  能量使用: 1.1177
  推理时间: 2.6148秒

批次 49:
  奖励值: 137.4820
  收益率: 0.1754
  距离: 36.2542
  内存使用: 0.7495
  能量使用: 1.0324
  推理时间: 2.4856秒

批次 50:
  奖励值: 133.6306
  收益率: 0.1645
  距离: 29.2924
  内存使用: 0.7088
  能量使用: 0.9375
  推理时间: 2.3813秒

批次 51:
  奖励值: 120.3613
  收益率: 0.1520
  距离: 28.2287
  内存使用: 0.9101
  能量使用: 0.9386
  推理时间: 2.1727秒

批次 52:
  奖励值: 144.4968
  收益率: 0.1793
  距离: 37.1688
  内存使用: 0.8091
  能量使用: 1.0285
  推理时间: 2.5832秒

批次 53:
  奖励值: 120.3481
  收益率: 0.1501
  距离: 28.6940
  内存使用: 0.9087
  能量使用: 0.9010
  推理时间: 2.1917秒

批次 54:
  奖励值: 132.8579
  收益率: 0.1677
  距离: 32.5186
  内存使用: 0.6886
  能量使用: 1.0769
  推理时间: 2.4989秒

批次 55:
  奖励值: 107.7184
  收益率: 0.1356
  距离: 26.3979
  内存使用: 0.9081
  能量使用: 0.8571
  推理时间: 2.0348秒

批次 56:
  奖励值: 112.7263
  收益率: 0.1410
  距离: 26.3522
  内存使用: 0.9087
  能量使用: 0.8864
  推理时间: 2.0670秒

批次 57:
  奖励值: 127.4180
  收益率: 0.1572
  距离: 27.7913
  内存使用: 0.6337
  能量使用: 0.9389
  推理时间: 2.2439秒

批次 58:
  奖励值: 115.0378
  收益率: 0.1446
  距离: 29.1649
  内存使用: 0.9015
  能量使用: 0.8579
  推理时间: 2.2363秒

批次 59:
  奖励值: 135.9841
  收益率: 0.1706
  距离: 32.5830
  内存使用: 0.7287
  能量使用: 0.9200
  推理时间: 2.4197秒

批次 60:
  奖励值: 124.6275
  收益率: 0.1607
  距离: 33.3205
  内存使用: 0.6508
  能量使用: 0.9737
  推理时间: 2.2393秒

批次 61:
  奖励值: 126.4175
  收益率: 0.1561
  距离: 28.9990
  内存使用: 0.6200
  能量使用: 0.9460
  推理时间: 2.2657秒

批次 62:
  奖励值: 122.5576
  收益率: 0.1503
  距离: 27.1964
  内存使用: 0.9030
  能量使用: 0.9853
  推理时间: 2.2062秒

批次 63:
  奖励值: 141.5129
  收益率: 0.1768
  距离: 34.8335
  内存使用: 0.6988
  能量使用: 1.0418
  推理时间: 2.5973秒

批次 64:
  奖励值: 120.8786
  收益率: 0.1528
  距离: 30.0564
  内存使用: 0.9177
  能量使用: 0.8686
  推理时间: 2.4335秒

批次 65:
  奖励值: 125.0313
  收益率: 0.1508
  距离: 28.2487
  内存使用: 0.9127
  能量使用: 0.9759
  推理时间: 2.2654秒

批次 66:
  奖励值: 120.6722
  收益率: 0.1514
  距离: 27.0152
  内存使用: 0.9041
  能量使用: 0.9362
  推理时间: 2.1901秒

批次 67:
  奖励值: 138.0564
  收益率: 0.1734
  距离: 34.8298
  内存使用: 0.7597
  能量使用: 1.0978
  推理时间: 2.4884秒

批次 68:
  奖励值: 146.3398
  收益率: 0.1837
  距离: 37.9856
  内存使用: 0.8286
  能量使用: 1.1061
  推理时间: 2.7156秒

批次 69:
  奖励值: 114.3194
  收益率: 0.1442
  距离: 27.5989
  内存使用: 0.9130
  能量使用: 0.8491
  推理时间: 2.1368秒

批次 70:
  奖励值: 130.1698
  收益率: 0.1598
  距离: 30.1999
  内存使用: 0.7184
  能量使用: 0.9421
  推理时间: 2.3925秒

批次 71:
  奖励值: 109.6637
  收益率: 0.1376
  距离: 28.7234
  内存使用: 0.9154
  能量使用: 0.8339
  推理时间: 1.9881秒

批次 72:
  奖励值: 119.9571
  收益率: 0.1479
  距离: 28.2944
  内存使用: 0.9021
  能量使用: 0.8892
  推理时间: 2.1351秒

批次 73:
  奖励值: 137.6161
  收益率: 0.1729
  距离: 36.2365
  内存使用: 0.7361
  能量使用: 0.9951
  推理时间: 2.5350秒

批次 74:
  奖励值: 109.5458
  收益率: 0.1377
  距离: 29.6825
  内存使用: 0.9126
  能量使用: 0.9132
  推理时间: 2.1785秒

批次 75:
  奖励值: 110.2604
  收益率: 0.1388
  距离: 29.1196
  内存使用: 0.9065
  能量使用: 0.8531
  推理时间: 2.1676秒

批次 76:
  奖励值: 130.8647
  收益率: 0.1634
  距离: 31.1703
  内存使用: 0.7074
  能量使用: 0.9928
  推理时间: 2.3851秒

批次 77:
  奖励值: 131.8778
  收益率: 0.1653
  距离: 32.1569
  内存使用: 0.7620
  能量使用: 0.9255
  推理时间: 2.3919秒

批次 78:
  奖励值: 115.6856
  收益率: 0.1484
  距离: 30.5881
  内存使用: 0.6218
  能量使用: 0.8189
  推理时间: 2.0619秒

批次 79:
  奖励值: 119.3605
  收益率: 0.1523
  距离: 28.4791
  内存使用: 0.9062
  能量使用: 0.8937
  推理时间: 2.2091秒

批次 80:
  奖励值: 111.7969
  收益率: 0.1401
  距离: 27.4173
  内存使用: 0.9194
  能量使用: 0.8696
  推理时间: 2.0827秒

批次 81:
  奖励值: 134.9814
  收益率: 0.1703
  距离: 34.3327
  内存使用: 0.7447
  能量使用: 0.9599
  推理时间: 2.5214秒

批次 82:
  奖励值: 120.0452
  收益率: 0.1506
  距离: 29.2316
  内存使用: 0.9066
  能量使用: 0.9714
  推理时间: 2.2447秒

批次 83:
  奖励值: 113.6862
  收益率: 0.1429
  距离: 29.3870
  内存使用: 0.9227
  能量使用: 0.9180
  推理时间: 2.0938秒

批次 84:
  奖励值: 145.7017
  收益率: 0.1835
  距离: 37.0023
  内存使用: 0.7672
  能量使用: 1.0271
  推理时间: 2.9252秒

批次 85:
  奖励值: 153.2981
  收益率: 0.1927
  距离: 37.3810
  内存使用: 0.8277
  能量使用: 1.1952
  推理时间: 2.9140秒

批次 86:
  奖励值: 119.7596
  收益率: 0.1475
  距离: 28.2326
  内存使用: 0.9087
  能量使用: 0.9394
  推理时间: 2.1755秒

批次 87:
  奖励值: 115.5080
  收益率: 0.1490
  距离: 34.2091
  内存使用: 0.9140
  能量使用: 0.8608
  推理时间: 2.2523秒

批次 88:
  奖励值: 119.6284
  收益率: 0.1459
  距离: 27.2903
  内存使用: 0.9061
  能量使用: 0.9469
  推理时间: 2.2215秒

批次 89:
  奖励值: 144.0826
  收益率: 0.1787
  距离: 39.4837
  内存使用: 0.7952
  能量使用: 1.1559
  推理时间: 2.7203秒

批次 90:
  奖励值: 131.6434
  收益率: 0.1645
  距离: 32.7104
  内存使用: 0.6252
  能量使用: 0.9776
  推理时间: 2.4121秒

批次 91:
  奖励值: 128.9525
  收益率: 0.1588
  距离: 28.7028
  内存使用: 0.6201
  能量使用: 0.9721
  推理时间: 2.2673秒

批次 92:
  奖励值: 117.1556
  收益率: 0.1433
  距离: 27.5274
  内存使用: 0.9036
  能量使用: 0.8578
  推理时间: 2.2340秒

批次 93:
  奖励值: 137.7651
  收益率: 0.1689
  距离: 31.2038
  内存使用: 0.7662
  能量使用: 1.0998
  推理时间: 2.4675秒

批次 94:
  奖励值: 134.3113
  收益率: 0.1696
  距离: 32.0590
  内存使用: 0.7287
  能量使用: 0.9896
  推理时间: 2.3882秒

批次 95:
  奖励值: 138.3875
  收益率: 0.1737
  距离: 32.3106
  内存使用: 0.6578
  能量使用: 1.0566
  推理时间: 2.4999秒

批次 96:
  奖励值: 122.7280
  收益率: 0.1542
  距离: 29.6466
  内存使用: 0.6148
  能量使用: 0.8826
  推理时间: 2.2534秒

批次 97:
  奖励值: 116.2652
  收益率: 0.1468
  距离: 29.5651
  内存使用: 0.9099
  能量使用: 0.9946
  推理时间: 2.1856秒

批次 98:
  奖励值: 157.4202
  收益率: 0.1944
  距离: 35.6864
  内存使用: 0.8593
  能量使用: 1.2139
  推理时间: 2.7769秒

批次 99:
  奖励值: 122.8538
  收益率: 0.1548
  距离: 32.3815
  内存使用: 0.9072
  能量使用: 1.0037
  推理时间: 2.3106秒

批次 100:
  奖励值: 139.1534
  收益率: 0.1740
  距离: 32.3137
  内存使用: 0.7525
  能量使用: 1.0269
  推理时间: 2.5069秒


==================== 总结 ====================
平均收益率: 0.1595
平均能量使用: 0.9659
平均推理时间: 2.3576秒
