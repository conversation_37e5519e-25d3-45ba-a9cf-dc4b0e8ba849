推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 76.3314
  收益率: 0.6100
  距离: 18.8027
  内存使用: 0.2903
  能量使用: 0.6441
  推理时间: 1.5615秒

批次 2:
  奖励值: 69.4500
  收益率: 0.5904
  距离: 20.7567
  内存使用: 0.3285
  能量使用: 0.6148
  推理时间: 1.5595秒

批次 3:
  奖励值: 69.4644
  收益率: 0.5867
  距离: 21.1451
  内存使用: 0.3181
  能量使用: 0.6079
  推理时间: 1.4812秒

批次 4:
  奖励值: 71.3769
  收益率: 0.5912
  距离: 18.2889
  内存使用: 0.3196
  能量使用: 0.6377
  推理时间: 1.4391秒

批次 5:
  奖励值: 74.5549
  收益率: 0.5922
  距离: 23.8850
  内存使用: 0.3397
  能量使用: 0.6654
  推理时间: 1.5502秒

批次 6:
  奖励值: 76.5608
  收益率: 0.6287
  距离: 22.6659
  内存使用: 0.3349
  能量使用: 0.6777
  推理时间: 1.5990秒

批次 7:
  奖励值: 77.6647
  收益率: 0.6405
  距离: 23.2112
  内存使用: 0.3733
  能量使用: 0.6862
  推理时间: 1.6442秒

批次 8:
  奖励值: 72.2865
  收益率: 0.6182
  距离: 21.8933
  内存使用: 0.3369
  能量使用: 0.5969
  推理时间: 1.7164秒

批次 9:
  奖励值: 70.4095
  收益率: 0.5746
  距离: 19.3265
  内存使用: 0.2669
  能量使用: 0.5877
  推理时间: 1.4841秒

批次 10:
  奖励值: 72.2344
  收益率: 0.6110
  距离: 18.6068
  内存使用: 0.3489
  能量使用: 0.5755
  推理时间: 1.4933秒

批次 11:
  奖励值: 70.5531
  收益率: 0.5800
  距离: 22.5413
  内存使用: 0.2889
  能量使用: 0.6636
  推理时间: 1.4857秒

批次 12:
  奖励值: 74.7087
  收益率: 0.6244
  距离: 20.7650
  内存使用: 0.3436
  能量使用: 0.6741
  推理时间: 1.5374秒

批次 13:
  奖励值: 66.3567
  收益率: 0.5830
  距离: 20.2085
  内存使用: 0.2791
  能量使用: 0.5990
  推理时间: 1.4361秒

批次 14:
  奖励值: 72.3730
  收益率: 0.6067
  距离: 20.9768
  内存使用: 0.3375
  能量使用: 0.5398
  推理时间: 1.4588秒

批次 15:
  奖励值: 71.7549
  收益率: 0.5791
  距离: 18.1584
  内存使用: 0.3602
  能量使用: 0.5717
  推理时间: 1.6480秒

批次 16:
  奖励值: 68.9922
  收益率: 0.5896
  距离: 21.7025
  内存使用: 0.2631
  能量使用: 0.6500
  推理时间: 1.5657秒

批次 17:
  奖励值: 68.3813
  收益率: 0.5751
  距离: 19.3142
  内存使用: 0.3248
  能量使用: 0.5984
  推理时间: 1.3853秒

批次 18:
  奖励值: 69.6443
  收益率: 0.6074
  距离: 20.3737
  内存使用: 0.2972
  能量使用: 0.6404
  推理时间: 1.4746秒

批次 19:
  奖励值: 77.1752
  收益率: 0.6317
  距离: 19.2114
  内存使用: 0.3759
  能量使用: 0.6622
  推理时间: 1.5585秒

批次 20:
  奖励值: 63.8580
  收益率: 0.5670
  距离: 19.4442
  内存使用: 0.2590
  能量使用: 0.5810
  推理时间: 1.4035秒

批次 21:
  奖励值: 67.7156
  收益率: 0.5693
  距离: 20.6594
  内存使用: 0.3055
  能量使用: 0.6040
  推理时间: 1.3848秒

批次 22:
  奖励值: 70.4233
  收益率: 0.5985
  距离: 19.2500
  内存使用: 0.3187
  能量使用: 0.5847
  推理时间: 1.6324秒

批次 23:
  奖励值: 74.5990
  收益率: 0.6416
  距离: 20.5768
  内存使用: 0.3404
  能量使用: 0.6847
  推理时间: 1.5859秒

批次 24:
  奖励值: 72.7927
  收益率: 0.5865
  距离: 21.6700
  内存使用: 0.3152
  能量使用: 0.6293
  推理时间: 1.5336秒

批次 25:
  奖励值: 70.5260
  收益率: 0.6129
  距离: 19.6614
  内存使用: 0.4068
  能量使用: 0.6797
  推理时间: 1.4844秒

批次 26:
  奖励值: 76.7154
  收益率: 0.6413
  距离: 21.0369
  内存使用: 0.4736
  能量使用: 0.6588
  推理时间: 1.7772秒

批次 27:
  奖励值: 76.2392
  收益率: 0.6054
  距离: 19.8887
  内存使用: 0.3576
  能量使用: 0.6753
  推理时间: 1.5000秒

批次 28:
  奖励值: 55.5424
  收益率: 0.4474
  距离: 14.0658
  内存使用: 0.1665
  能量使用: 0.4827
  推理时间: 1.1786秒

批次 29:
  奖励值: 88.4302
  收益率: 0.6623
  距离: 24.3060
  内存使用: 0.4665
  能量使用: 0.7671
  推理时间: 1.7873秒

批次 30:
  奖励值: 72.6709
  收益率: 0.5944
  距离: 18.1308
  内存使用: 0.3659
  能量使用: 0.5614
  推理时间: 1.6036秒

批次 31:
  奖励值: 73.2756
  收益率: 0.6001
  距离: 17.3030
  内存使用: 0.3133
  能量使用: 0.6325
  推理时间: 1.4882秒

批次 32:
  奖励值: 79.2658
  收益率: 0.6265
  距离: 19.1750
  内存使用: 0.3803
  能量使用: 0.7133
  推理时间: 1.6528秒

批次 33:
  奖励值: 74.1270
  收益率: 0.6467
  距离: 21.8181
  内存使用: 0.3224
  能量使用: 0.6055
  推理时间: 1.5290秒

批次 34:
  奖励值: 69.1033
  收益率: 0.6212
  距离: 21.2262
  内存使用: 0.3471
  能量使用: 0.6201
  推理时间: 1.6034秒

批次 35:
  奖励值: 69.1165
  收益率: 0.5789
  距离: 20.1283
  内存使用: 0.3081
  能量使用: 0.5308
  推理时间: 1.4595秒

批次 36:
  奖励值: 72.1888
  收益率: 0.6006
  距离: 19.4053
  内存使用: 0.3577
  能量使用: 0.6594
  推理时间: 1.5527秒

批次 37:
  奖励值: 70.2760
  收益率: 0.5780
  距离: 17.9752
  内存使用: 0.3338
  能量使用: 0.5389
  推理时间: 1.5638秒

批次 38:
  奖励值: 68.3660
  收益率: 0.5696
  距离: 20.3789
  内存使用: 0.2761
  能量使用: 0.6131
  推理时间: 1.5827秒

批次 39:
  奖励值: 70.1731
  收益率: 0.6284
  距离: 22.6686
  内存使用: 0.3523
  能量使用: 0.6330
  推理时间: 1.5485秒

批次 40:
  奖励值: 81.4543
  收益率: 0.6405
  距离: 17.6397
  内存使用: 0.2725
  能量使用: 0.5792
  推理时间: 1.5054秒

批次 41:
  奖励值: 71.0440
  收益率: 0.6234
  距离: 22.1795
  内存使用: 0.2812
  能量使用: 0.6514
  推理时间: 1.6797秒

批次 42:
  奖励值: 68.8209
  收益率: 0.5668
  距离: 19.3365
  内存使用: 0.2641
  能量使用: 0.5576
  推理时间: 1.4469秒

批次 43:
  奖励值: 68.4211
  收益率: 0.5630
  距离: 18.7501
  内存使用: 0.2637
  能量使用: 0.5441
  推理时间: 1.4092秒

批次 44:
  奖励值: 67.0482
  收益率: 0.5714
  距离: 18.6722
  内存使用: 0.2536
  能量使用: 0.5777
  推理时间: 1.4153秒

批次 45:
  奖励值: 61.7845
  收益率: 0.5223
  距离: 16.0630
  内存使用: 0.2597
  能量使用: 0.5084
  推理时间: 1.4667秒

批次 46:
  奖励值: 73.8458
  收益率: 0.6131
  距离: 22.0056
  内存使用: 0.3790
  能量使用: 0.7239
  推理时间: 1.5422秒

批次 47:
  奖励值: 71.8209
  收益率: 0.5767
  距离: 18.5371
  内存使用: 0.3712
  能量使用: 0.6499
  推理时间: 1.5870秒

批次 48:
  奖励值: 66.0317
  收益率: 0.5500
  距离: 20.1025
  内存使用: 0.2836
  能量使用: 0.5505
  推理时间: 1.4456秒

批次 49:
  奖励值: 78.7069
  收益率: 0.6703
  距离: 24.0887
  内存使用: 0.3848
  能量使用: 0.7128
  推理时间: 1.7071秒

批次 50:
  奖励值: 72.7642
  收益率: 0.6115
  距离: 19.7889
  内存使用: 0.3306
  能量使用: 0.5520
  推理时间: 1.5777秒

批次 51:
  奖励值: 64.8699
  收益率: 0.5376
  距离: 16.7056
  内存使用: 0.2330
  能量使用: 0.5215
  推理时间: 1.4368秒

批次 52:
  奖励值: 70.4464
  收益率: 0.5937
  距离: 19.2020
  内存使用: 0.3331
  能量使用: 0.5722
  推理时间: 1.5370秒

批次 53:
  奖励值: 73.5815
  收益率: 0.6172
  距离: 20.8520
  内存使用: 0.3599
  能量使用: 0.6251
  推理时间: 1.6063秒

批次 54:
  奖励值: 66.7743
  收益率: 0.5985
  距离: 22.9031
  内存使用: 0.3016
  能量使用: 0.6467
  推理时间: 1.5321秒

批次 55:
  奖励值: 68.1479
  收益率: 0.5816
  距离: 19.6601
  内存使用: 0.2792
  能量使用: 0.5650
  推理时间: 1.4373秒

批次 56:
  奖励值: 72.5875
  收益率: 0.6285
  距离: 20.6753
  内存使用: 0.3630
  能量使用: 0.5866
  推理时间: 1.6882秒

批次 57:
  奖励值: 79.8033
  收益率: 0.6422
  距离: 24.8897
  内存使用: 0.4268
  能量使用: 0.6768
  推理时间: 1.7467秒

批次 58:
  奖励值: 78.5588
  收益率: 0.6331
  距离: 20.2499
  内存使用: 0.3681
  能量使用: 0.6644
  推理时间: 1.8153秒

批次 59:
  奖励值: 72.5186
  收益率: 0.6105
  距离: 21.9507
  内存使用: 0.3736
  能量使用: 0.6385
  推理时间: 1.4969秒

批次 60:
  奖励值: 74.9707
  收益率: 0.6289
  距离: 21.2701
  内存使用: 0.3790
  能量使用: 0.6096
  推理时间: 1.5602秒

批次 61:
  奖励值: 71.3067
  收益率: 0.6295
  距离: 21.1857
  内存使用: 0.3342
  能量使用: 0.6668
  推理时间: 1.5024秒

批次 62:
  奖励值: 68.2765
  收益率: 0.5826
  距离: 20.5177
  内存使用: 0.3044
  能量使用: 0.6249
  推理时间: 1.4348秒

批次 63:
  奖励值: 66.4075
  收益率: 0.5661
  距离: 17.9205
  内存使用: 0.2636
  能量使用: 0.6009
  推理时间: 1.3666秒

批次 64:
  奖励值: 72.9939
  收益率: 0.6081
  距离: 19.0269
  内存使用: 0.3661
  能量使用: 0.6193
  推理时间: 1.5037秒

批次 65:
  奖励值: 73.2110
  收益率: 0.5922
  距离: 19.0705
  内存使用: 0.3177
  能量使用: 0.5899
  推理时间: 1.4719秒

批次 66:
  奖励值: 77.2334
  收益率: 0.6180
  距离: 20.5001
  内存使用: 0.3890
  能量使用: 0.6441
  推理时间: 1.6256秒

批次 67:
  奖励值: 69.4656
  收益率: 0.5974
  距离: 18.9057
  内存使用: 0.3656
  能量使用: 0.5976
  推理时间: 1.5411秒

批次 68:
  奖励值: 76.6084
  收益率: 0.6074
  距离: 18.8029
  内存使用: 0.3796
  能量使用: 0.6007
  推理时间: 1.6038秒

批次 69:
  奖励值: 72.7382
  收益率: 0.6141
  距离: 24.6800
  内存使用: 0.4004
  能量使用: 0.6433
  推理时间: 1.5363秒

批次 70:
  奖励值: 76.3206
  收益率: 0.6412
  距离: 19.3220
  内存使用: 0.3662
  能量使用: 0.6706
  推理时间: 1.6371秒

批次 71:
  奖励值: 76.6627
  收益率: 0.6398
  距离: 20.4362
  内存使用: 0.3779
  能量使用: 0.6605
  推理时间: 1.5870秒

批次 72:
  奖励值: 72.1147
  收益率: 0.5906
  距离: 17.8117
  内存使用: 0.3590
  能量使用: 0.6000
  推理时间: 1.6073秒

批次 73:
  奖励值: 72.6927
  收益率: 0.6101
  距离: 20.5228
  内存使用: 0.2943
  能量使用: 0.5976
  推理时间: 1.4863秒

批次 74:
  奖励值: 74.3080
  收益率: 0.6312
  距离: 23.5800
  内存使用: 0.3305
  能量使用: 0.6735
  推理时间: 1.7351秒

批次 75:
  奖励值: 65.8312
  收益率: 0.5729
  距离: 16.1376
  内存使用: 0.2589
  能量使用: 0.5410
  推理时间: 1.3709秒

批次 76:
  奖励值: 72.7505
  收益率: 0.6000
  距离: 20.3533
  内存使用: 0.3274
  能量使用: 0.6182
  推理时间: 1.5146秒

批次 77:
  奖励值: 74.1611
  收益率: 0.6089
  距离: 18.5571
  内存使用: 0.3240
  能量使用: 0.5967
  推理时间: 1.4970秒

批次 78:
  奖励值: 79.7868
  收益率: 0.6431
  距离: 19.5421
  内存使用: 0.4242
  能量使用: 0.6601
  推理时间: 1.6805秒

批次 79:
  奖励值: 74.9337
  收益率: 0.6413
  距离: 21.1049
  内存使用: 0.3770
  能量使用: 0.6507
  推理时间: 1.6151秒

批次 80:
  奖励值: 78.7025
  收益率: 0.6306
  距离: 20.2504
  内存使用: 0.3378
  能量使用: 0.6068
  推理时间: 1.6307秒

批次 81:
  奖励值: 63.4248
  收益率: 0.5457
  距离: 15.5912
  内存使用: 0.2160
  能量使用: 0.5431
  推理时间: 1.2405秒

批次 82:
  奖励值: 74.3682
  收益率: 0.5840
  距离: 17.0846
  内存使用: 0.3247
  能量使用: 0.6542
  推理时间: 1.5293秒

批次 83:
  奖励值: 72.8971
  收益率: 0.5849
  距离: 20.1589
  内存使用: 0.3006
  能量使用: 0.5463
  推理时间: 1.4635秒

批次 84:
  奖励值: 74.0471
  收益率: 0.6278
  距离: 21.9641
  内存使用: 0.3572
  能量使用: 0.5845
  推理时间: 1.5804秒

批次 85:
  奖励值: 68.2159
  收益率: 0.5803
  距离: 20.3145
  内存使用: 0.2827
  能量使用: 0.5711
  推理时间: 1.4727秒

批次 86:
  奖励值: 67.2468
  收益率: 0.5737
  距离: 18.0331
  内存使用: 0.3030
  能量使用: 0.5703
  推理时间: 1.3467秒

批次 87:
  奖励值: 75.2082
  收益率: 0.6199
  距离: 20.7563
  内存使用: 0.4152
  能量使用: 0.6122
  推理时间: 1.5101秒

批次 88:
  奖励值: 67.6619
  收益率: 0.5599
  距离: 19.8794
  内存使用: 0.2764
  能量使用: 0.5204
  推理时间: 1.4402秒

批次 89:
  奖励值: 70.2227
  收益率: 0.5806
  距离: 19.2793
  内存使用: 0.2909
  能量使用: 0.5844
  推理时间: 1.4886秒

批次 90:
  奖励值: 79.6635
  收益率: 0.6432
  距离: 22.0419
  内存使用: 0.3870
  能量使用: 0.6440
  推理时间: 1.6768秒

批次 91:
  奖励值: 76.9979
  收益率: 0.6411
  距离: 20.7881
  内存使用: 0.3624
  能量使用: 0.6536
  推理时间: 1.6210秒

批次 92:
  奖励值: 68.1246
  收益率: 0.5775
  距离: 19.2423
  内存使用: 0.2795
  能量使用: 0.6290
  推理时间: 1.5368秒

批次 93:
  奖励值: 72.3227
  收益率: 0.6041
  距离: 19.2272
  内存使用: 0.3418
  能量使用: 0.5855
  推理时间: 1.4790秒

批次 94:
  奖励值: 63.8373
  收益率: 0.5489
  距离: 22.9133
  内存使用: 0.2854
  能量使用: 0.5267
  推理时间: 1.4503秒

批次 95:
  奖励值: 70.3838
  收益率: 0.6271
  距离: 20.9400
  内存使用: 0.3281
  能量使用: 0.7643
  推理时间: 1.6395秒

批次 96:
  奖励值: 78.4055
  收益率: 0.6557
  距离: 25.3222
  内存使用: 0.3678
  能量使用: 0.6990
  推理时间: 1.8658秒

批次 97:
  奖励值: 71.0685
  收益率: 0.6108
  距离: 24.4811
  内存使用: 0.3174
  能量使用: 0.6646
  推理时间: 1.5722秒

批次 98:
  奖励值: 67.0389
  收益率: 0.5666
  距离: 20.6829
  内存使用: 0.2728
  能量使用: 0.5664
  推理时间: 1.3975秒

批次 99:
  奖励值: 67.8518
  收益率: 0.5806
  距离: 20.5352
  内存使用: 0.2255
  能量使用: 0.5358
  推理时间: 1.3702秒

批次 100:
  奖励值: 60.2010
  收益率: 0.4912
  距离: 12.7674
  内存使用: 0.1825
  能量使用: 0.4481
  推理时间: 1.1031秒


==================== 总结 ====================
平均收益率: 0.6001
平均能量使用: 0.6143
平均推理时间: 1.5330秒
