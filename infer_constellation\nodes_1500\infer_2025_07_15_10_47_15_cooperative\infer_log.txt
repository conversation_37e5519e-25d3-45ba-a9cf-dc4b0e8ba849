推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 124.1693
  收益率: 0.2118
  距离: 34.3087
  内存使用: 0.7430
  能量使用: 1.0096
  推理时间: 2.2628秒

批次 2:
  奖励值: 113.7509
  收益率: 0.1912
  距离: 28.9083
  内存使用: 0.6552
  能量使用: 0.9346
  推理时间: 2.0681秒

批次 3:
  奖励值: 136.3006
  收益率: 0.2340
  距离: 35.6187
  内存使用: 0.6977
  能量使用: 1.0823
  推理时间: 2.6367秒

批次 4:
  奖励值: 117.6616
  收益率: 0.1943
  距离: 30.0241
  内存使用: 0.7084
  能量使用: 0.9444
  推理时间: 2.0312秒

批次 5:
  奖励值: 129.7182
  收益率: 0.2189
  距离: 33.8050
  内存使用: 0.7421
  能量使用: 1.0164
  推理时间: 2.2681秒

批次 6:
  奖励值: 111.6144
  收益率: 0.1811
  距离: 25.9799
  内存使用: 0.9059
  能量使用: 0.8853
  推理时间: 2.0529秒

批次 7:
  奖励值: 109.0120
  收益率: 0.1803
  距离: 27.0245
  内存使用: 0.9061
  能量使用: 0.8454
  推理时间: 1.9683秒

批次 8:
  奖励值: 113.0632
  收益率: 0.1881
  距离: 27.0873
  内存使用: 0.9147
  能量使用: 0.8715
  推理时间: 2.0775秒

批次 9:
  奖励值: 133.6337
  收益率: 0.2256
  距离: 35.7700
  内存使用: 0.7541
  能量使用: 1.0085
  推理时间: 2.4655秒

批次 10:
  奖励值: 114.7426
  收益率: 0.1949
  距离: 29.7315
  内存使用: 0.6540
  能量使用: 0.9293
  推理时间: 2.0346秒

批次 11:
  奖励值: 121.2263
  收益率: 0.2045
  距离: 31.4895
  内存使用: 0.6919
  能量使用: 0.9660
  推理时间: 2.1251秒

批次 12:
  奖励值: 106.1859
  收益率: 0.1756
  距离: 26.1191
  内存使用: 0.5047
  能量使用: 0.8043
  推理时间: 1.8391秒

批次 13:
  奖励值: 140.2628
  收益率: 0.2334
  距离: 32.3172
  内存使用: 0.6794
  能量使用: 1.0777
  推理时间: 2.4027秒

批次 14:
  奖励值: 120.5214
  收益率: 0.2003
  距离: 29.5774
  内存使用: 0.6319
  能量使用: 0.9478
  推理时间: 2.0427秒

批次 15:
  奖励值: 134.3210
  收益率: 0.2248
  距离: 33.2869
  内存使用: 0.7455
  能量使用: 1.1256
  推理时间: 2.3913秒

批次 16:
  奖励值: 113.3848
  收益率: 0.1905
  距离: 26.6122
  内存使用: 0.9117
  能量使用: 0.9065
  推理时间: 2.1104秒

批次 17:
  奖励值: 118.4364
  收益率: 0.1969
  距离: 30.2110
  内存使用: 0.6798
  能量使用: 0.8363
  推理时间: 2.1073秒

批次 18:
  奖励值: 130.8139
  收益率: 0.2254
  距离: 33.8907
  内存使用: 0.7452
  能量使用: 0.9362
  推理时间: 2.2824秒

批次 19:
  奖励值: 112.2996
  收益率: 0.1894
  距离: 28.5786
  内存使用: 0.9132
  能量使用: 0.9090
  推理时间: 1.9873秒

批次 20:
  奖励值: 122.8027
  收益率: 0.2052
  距离: 29.0607
  内存使用: 0.7233
  能量使用: 1.0106
  推理时间: 2.1836秒

批次 21:
  奖励值: 115.7951
  收益率: 0.1946
  距离: 29.5058
  内存使用: 0.5806
  能量使用: 0.9232
  推理时间: 2.0308秒

批次 22:
  奖励值: 116.0563
  收益率: 0.1886
  距离: 26.6743
  内存使用: 0.9081
  能量使用: 0.9050
  推理时间: 2.0497秒

批次 23:
  奖励值: 114.4539
  收益率: 0.1908
  距离: 30.2261
  内存使用: 0.9082
  能量使用: 0.9579
  推理时间: 2.1565秒

批次 24:
  奖励值: 125.5440
  收益率: 0.2186
  距离: 36.0940
  内存使用: 0.7492
  能量使用: 1.0688
  推理时间: 2.3625秒

批次 25:
  奖励值: 128.9431
  收益率: 0.2127
  距离: 32.9914
  内存使用: 0.6968
  能量使用: 1.0256
  推理时间: 2.1909秒

批次 26:
  奖励值: 124.4102
  收益率: 0.2091
  距离: 32.7585
  内存使用: 0.7467
  能量使用: 1.0745
  推理时间: 2.1890秒

批次 27:
  奖励值: 132.2647
  收益率: 0.2214
  距离: 31.3113
  内存使用: 0.6907
  能量使用: 1.0684
  推理时间: 2.2669秒

批次 28:
  奖励值: 108.3758
  收益率: 0.1859
  距离: 33.0712
  内存使用: 0.9079
  能量使用: 0.8693
  推理时间: 1.9358秒

批次 29:
  奖励值: 123.6401
  收益率: 0.2129
  距离: 31.4662
  内存使用: 0.6739
  能量使用: 0.9797
  推理时间: 2.2527秒

批次 30:
  奖励值: 133.5910
  收益率: 0.2247
  距离: 29.3791
  内存使用: 0.7619
  能量使用: 1.0769
  推理时间: 2.2885秒

批次 31:
  奖励值: 105.5672
  收益率: 0.1786
  距离: 25.3701
  内存使用: 0.9064
  能量使用: 0.7891
  推理时间: 1.8349秒

批次 32:
  奖励值: 132.0141
  收益率: 0.2184
  距离: 34.6468
  内存使用: 0.6805
  能量使用: 1.0445
  推理时间: 2.2730秒

批次 33:
  奖励值: 112.0467
  收益率: 0.1884
  距离: 27.4796
  内存使用: 0.9073
  能量使用: 0.9197
  推理时间: 1.9626秒

批次 34:
  奖励值: 116.8865
  收益率: 0.1958
  距离: 28.6668
  内存使用: 0.6487
  能量使用: 0.9115
  推理时间: 2.1215秒

批次 35:
  奖励值: 134.4368
  收益率: 0.2221
  距离: 30.3428
  内存使用: 0.7438
  能量使用: 1.0398
  推理时间: 2.3175秒

批次 36:
  奖励值: 112.3902
  收益率: 0.1921
  距离: 31.3819
  内存使用: 0.9130
  能量使用: 0.9247
  推理时间: 2.0189秒

批次 37:
  奖励值: 121.3968
  收益率: 0.2016
  距离: 33.0942
  内存使用: 0.7505
  能量使用: 0.9616
  推理时间: 2.1741秒

批次 38:
  奖励值: 112.4255
  收益率: 0.1851
  距离: 27.3277
  内存使用: 0.5723
  能量使用: 0.8489
  推理时间: 1.9223秒

批次 39:
  奖励值: 109.6131
  收益率: 0.1842
  距离: 28.2136
  内存使用: 0.9085
  能量使用: 0.8518
  推理时间: 2.0586秒

批次 40:
  奖励值: 111.5569
  收益率: 0.1873
  距离: 29.5110
  内存使用: 0.9052
  能量使用: 0.8700
  推理时间: 1.9669秒

批次 41:
  奖励值: 145.6337
  收益率: 0.2418
  距离: 37.5222
  内存使用: 0.7892
  能量使用: 1.2210
  推理时间: 2.5201秒

批次 42:
  奖励值: 120.1291
  收益率: 0.2002
  距离: 29.8646
  内存使用: 0.6719
  能量使用: 1.0054
  推理时间: 2.3118秒

批次 43:
  奖励值: 140.4969
  收益率: 0.2388
  距离: 35.9243
  内存使用: 0.8392
  能量使用: 1.0427
  推理时间: 2.6025秒

批次 44:
  奖励值: 127.0639
  收益率: 0.2180
  距离: 35.3052
  内存使用: 0.6876
  能量使用: 1.0794
  推理时间: 2.2962秒

批次 45:
  奖励值: 136.5044
  收益率: 0.2334
  距离: 34.3857
  内存使用: 0.6792
  能量使用: 1.1263
  推理时间: 2.5153秒

批次 46:
  奖励值: 137.1422
  收益率: 0.2275
  距离: 36.8356
  内存使用: 0.7452
  能量使用: 1.1348
  推理时间: 2.3398秒

批次 47:
  奖励值: 111.8810
  收益率: 0.1901
  距离: 28.3907
  内存使用: 0.5550
  能量使用: 0.8923
  推理时间: 2.0300秒

批次 48:
  奖励值: 142.3151
  收益率: 0.2383
  距离: 35.8508
  内存使用: 0.8285
  能量使用: 1.0905
  推理时间: 2.4994秒

批次 49:
  奖励值: 113.4169
  收益率: 0.1875
  距离: 28.0683
  内存使用: 0.6193
  能量使用: 0.8786
  推理时间: 1.9031秒

批次 50:
  奖励值: 108.3149
  收益率: 0.1875
  距离: 32.0997
  内存使用: 0.9090
  能量使用: 0.9613
  推理时间: 1.9939秒

批次 51:
  奖励值: 133.8034
  收益率: 0.2240
  距离: 31.1776
  内存使用: 0.7701
  能量使用: 1.0455
  推理时间: 2.4638秒

批次 52:
  奖励值: 122.5588
  收益率: 0.2097
  距离: 30.5798
  内存使用: 0.7028
  能量使用: 0.9998
  推理时间: 2.2315秒

批次 53:
  奖励值: 136.1003
  收益率: 0.2231
  距离: 35.2984
  内存使用: 0.6640
  能量使用: 1.0370
  推理时间: 2.3765秒

批次 54:
  奖励值: 119.8376
  收益率: 0.1985
  距离: 29.4049
  内存使用: 0.5771
  能量使用: 0.9427
  推理时间: 2.0310秒

批次 55:
  奖励值: 115.6316
  收益率: 0.1901
  距离: 26.1218
  内存使用: 0.9065
  能量使用: 0.9084
  推理时间: 1.9958秒

批次 56:
  奖励值: 126.4120
  收益率: 0.2154
  距离: 33.7926
  内存使用: 0.7460
  能量使用: 1.0374
  推理时间: 2.2229秒

批次 57:
  奖励值: 108.3652
  收益率: 0.1837
  距离: 29.2808
  内存使用: 0.9039
  能量使用: 0.8848
  推理时间: 1.9028秒

批次 58:
  奖励值: 126.8526
  收益率: 0.2127
  距离: 34.3172
  内存使用: 0.7262
  能量使用: 1.0513
  推理时间: 2.2089秒

批次 59:
  奖励值: 97.4467
  收益率: 0.1630
  距离: 25.9934
  内存使用: 0.7754
  能量使用: 0.7377
  推理时间: 1.8380秒

批次 60:
  奖励值: 108.2088
  收益率: 0.1789
  距离: 27.0679
  内存使用: 0.9061
  能量使用: 0.8084
  推理时间: 1.8750秒

批次 61:
  奖励值: 115.6427
  收益率: 0.1980
  距离: 32.5574
  内存使用: 0.6488
  能量使用: 0.9084
  推理时间: 2.0109秒

批次 62:
  奖励值: 137.3659
  收益率: 0.2335
  距离: 36.5264
  内存使用: 0.6830
  能量使用: 1.0587
  推理时间: 2.3775秒

批次 63:
  奖励值: 129.1538
  收益率: 0.2147
  距离: 30.1605
  内存使用: 0.7012
  能量使用: 1.0306
  推理时间: 2.3184秒

批次 64:
  奖励值: 129.6701
  收益率: 0.2210
  距离: 34.5317
  内存使用: 0.6909
  能量使用: 1.0370
  推理时间: 2.2443秒

批次 65:
  奖励值: 115.7002
  收益率: 0.1909
  距离: 27.7268
  内存使用: 0.5900
  能量使用: 0.8827
  推理时间: 2.0173秒

批次 66:
  奖励值: 110.3786
  收益率: 0.1811
  距离: 28.4410
  内存使用: 0.9110
  能量使用: 0.8906
  推理时间: 2.0807秒

批次 67:
  奖励值: 135.3344
  收益率: 0.2259
  距离: 32.5791
  内存使用: 0.7329
  能量使用: 1.0192
  推理时间: 2.3066秒

批次 68:
  奖励值: 127.2323
  收益率: 0.2140
  距离: 36.4145
  内存使用: 0.6998
  能量使用: 1.0211
  推理时间: 2.2426秒

批次 69:
  奖励值: 141.8847
  收益率: 0.2383
  距离: 33.7879
  内存使用: 0.7840
  能量使用: 1.0588
  推理时间: 2.4264秒

批次 70:
  奖励值: 121.7444
  收益率: 0.2071
  距离: 33.0648
  内存使用: 0.6847
  能量使用: 0.9616
  推理时间: 2.1393秒

批次 71:
  奖励值: 123.0285
  收益率: 0.2069
  距离: 30.0760
  内存使用: 0.6828
  能量使用: 0.9843
  推理时间: 2.1227秒

批次 72:
  奖励值: 114.3695
  收益率: 0.1917
  距离: 30.6918
  内存使用: 0.9108
  能量使用: 0.8584
  推理时间: 2.0498秒

批次 73:
  奖励值: 115.2517
  收益率: 0.1941
  距离: 28.7269
  内存使用: 0.6485
  能量使用: 0.9212
  推理时间: 1.9982秒

批次 74:
  奖励值: 142.1485
  收益率: 0.2393
  距离: 37.7422
  内存使用: 0.8218
  能量使用: 1.1391
  推理时间: 2.4680秒

批次 75:
  奖励值: 127.4783
  收益率: 0.2155
  距离: 33.4277
  内存使用: 0.6454
  能量使用: 0.9702
  推理时间: 2.3025秒

批次 76:
  奖励值: 134.8008
  收益率: 0.2222
  距离: 32.2356
  内存使用: 0.7072
  能量使用: 1.0556
  推理时间: 2.4435秒

批次 77:
  奖励值: 104.1502
  收益率: 0.1774
  距离: 29.7656
  内存使用: 0.9134
  能量使用: 0.8205
  推理时间: 1.9846秒

批次 78:
  奖励值: 114.1602
  收益率: 0.1916
  距离: 29.0252
  内存使用: 0.9100
  能量使用: 0.9444
  推理时间: 2.0001秒

批次 79:
  奖励值: 119.5071
  收益率: 0.2043
  距离: 31.7068
  内存使用: 0.6802
  能量使用: 0.9987
  推理时间: 2.0952秒

批次 80:
  奖励值: 134.8365
  收益率: 0.2325
  距离: 36.5392
  内存使用: 0.8160
  能量使用: 0.9717
  推理时间: 2.4379秒

批次 81:
  奖励值: 126.9921
  收益率: 0.2129
  距离: 28.9706
  内存使用: 0.6527
  能量使用: 0.9925
  推理时间: 2.1879秒

批次 82:
  奖励值: 122.3067
  收益率: 0.1999
  距离: 30.1831
  内存使用: 0.7179
  能量使用: 0.8856
  推理时间: 2.1497秒

批次 83:
  奖励值: 125.9442
  收益率: 0.2071
  距离: 33.4733
  内存使用: 0.6536
  能量使用: 0.9847
  推理时间: 2.2019秒

批次 84:
  奖励值: 114.5944
  收益率: 0.1899
  距离: 28.1652
  内存使用: 0.9189
  能量使用: 0.9270
  推理时间: 2.0953秒

批次 85:
  奖励值: 101.5037
  收益率: 0.1688
  距离: 28.6209
  内存使用: 0.5631
  能量使用: 0.8086
  推理时间: 1.8148秒

批次 86:
  奖励值: 130.6848
  收益率: 0.2178
  距离: 34.9809
  内存使用: 0.7743
  能量使用: 1.0751
  推理时间: 2.4140秒

批次 87:
  奖励值: 108.2978
  收益率: 0.1780
  距离: 27.4010
  内存使用: 0.9068
  能量使用: 0.8966
  推理时间: 1.9314秒

批次 88:
  奖励值: 110.2804
  收益率: 0.1873
  距离: 26.0835
  内存使用: 0.9115
  能量使用: 0.8442
  推理时间: 1.9628秒

批次 89:
  奖励值: 125.1948
  收益率: 0.2103
  距离: 33.3666
  内存使用: 0.6412
  能量使用: 1.0026
  推理时间: 2.2376秒

批次 90:
  奖励值: 121.5109
  收益率: 0.2003
  距离: 30.6719
  内存使用: 0.6584
  能量使用: 1.0479
  推理时间: 2.1414秒

批次 91:
  奖励值: 110.7544
  收益率: 0.1878
  距离: 27.5202
  内存使用: 0.9106
  能量使用: 0.8604
  推理时间: 1.9667秒

批次 92:
  奖励值: 124.2653
  收益率: 0.2082
  距离: 32.5944
  内存使用: 0.7067
  能量使用: 0.9683
  推理时间: 2.1920秒

批次 93:
  奖励值: 135.1669
  收益率: 0.2262
  距离: 32.6810
  内存使用: 0.7147
  能量使用: 1.0705
  推理时间: 2.3221秒

批次 94:
  奖励值: 110.5849
  收益率: 0.1856
  距离: 30.0778
  内存使用: 0.5797
  能量使用: 0.7728
  推理时间: 1.9052秒

批次 95:
  奖励值: 107.4320
  收益率: 0.1809
  距离: 26.8070
  内存使用: 0.9069
  能量使用: 0.8222
  推理时间: 1.9745秒

批次 96:
  奖励值: 119.9341
  收益率: 0.2012
  距离: 29.9250
  内存使用: 0.6825
  能量使用: 0.9244
  推理时间: 2.1085秒

批次 97:
  奖励值: 117.7666
  收益率: 0.1972
  距离: 29.8500
  内存使用: 0.9055
  能量使用: 0.9111
  推理时间: 2.1808秒

批次 98:
  奖励值: 123.6820
  收益率: 0.2072
  距离: 31.6014
  内存使用: 0.7304
  能量使用: 0.9042
  推理时间: 2.1026秒

批次 99:
  奖励值: 134.9769
  收益率: 0.2291
  距离: 34.6317
  内存使用: 0.7289
  能量使用: 1.0854
  推理时间: 2.3499秒

批次 100:
  奖励值: 129.7693
  收益率: 0.2185
  距离: 33.7924
  内存使用: 0.6721
  能量使用: 0.9967
  推理时间: 2.3554秒


==================== 总结 ====================
平均收益率: 0.2046
平均能量使用: 0.9636
平均推理时间: 2.1653秒
