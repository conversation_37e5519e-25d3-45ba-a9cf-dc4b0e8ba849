推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 127.9403
  收益率: 0.2178
  距离: 34.5699
  内存使用: 0.9103
  能量使用: 1.0391
  推理时间: 2.6367秒

批次 2:
  奖励值: 121.6968
  收益率: 0.2063
  距离: 33.4217
  内存使用: 0.6570
  能量使用: 0.9276
  推理时间: 2.3399秒

批次 3:
  奖励值: 121.2664
  收益率: 0.2075
  距离: 30.5893
  内存使用: 0.9093
  能量使用: 0.9684
  推理时间: 2.3125秒

批次 4:
  奖励值: 108.6743
  收益率: 0.1799
  距离: 28.0999
  内存使用: 0.9085
  能量使用: 0.8429
  推理时间: 2.0526秒

批次 5:
  奖励值: 132.1578
  收益率: 0.2227
  距离: 34.0338
  内存使用: 0.7597
  能量使用: 1.0908
  推理时间: 2.4745秒

批次 6:
  奖励值: 131.6531
  收益率: 0.2136
  距离: 30.9939
  内存使用: 0.6847
  能量使用: 1.0153
  推理时间: 2.4823秒

批次 7:
  奖励值: 130.2587
  收益率: 0.2155
  距离: 32.8348
  内存使用: 0.6768
  能量使用: 0.9709
  推理时间: 2.4143秒

批次 8:
  奖励值: 140.6992
  收益率: 0.2352
  距离: 35.4940
  内存使用: 0.8175
  能量使用: 1.0900
  推理时间: 2.6321秒

批次 9:
  奖励值: 135.8759
  收益率: 0.2269
  距离: 33.0928
  内存使用: 0.7233
  能量使用: 1.0319
  推理时间: 2.7207秒

批次 10:
  奖励值: 117.3321
  收益率: 0.1978
  距离: 28.3506
  内存使用: 0.6486
  能量使用: 0.9365
  推理时间: 2.2218秒

批次 11:
  奖励值: 121.7795
  收益率: 0.2045
  距离: 30.4260
  内存使用: 0.6740
  能量使用: 0.9410
  推理时间: 2.2176秒

批次 12:
  奖励值: 124.2025
  收益率: 0.2059
  距离: 31.2044
  内存使用: 0.6765
  能量使用: 0.9139
  推理时间: 2.2869秒

批次 13:
  奖励值: 133.8178
  收益率: 0.2212
  距离: 28.9002
  内存使用: 0.6731
  能量使用: 0.9910
  推理时间: 2.3951秒

批次 14:
  奖励值: 115.6282
  收益率: 0.1928
  距离: 29.1992
  内存使用: 0.6190
  能量使用: 0.9254
  推理时间: 2.0878秒

批次 15:
  奖励值: 137.4814
  收益率: 0.2306
  距离: 34.7911
  内存使用: 0.7164
  能量使用: 1.1297
  推理时间: 2.6277秒

批次 16:
  奖励值: 140.8877
  收益率: 0.2369
  距离: 33.8189
  内存使用: 0.7573
  能量使用: 1.0484
  推理时间: 2.5047秒

批次 17:
  奖励值: 126.3066
  收益率: 0.2102
  距离: 32.5928
  内存使用: 0.6592
  能量使用: 0.9347
  推理时间: 2.2774秒

批次 18:
  奖励值: 135.7013
  收益率: 0.2325
  距离: 33.4834
  内存使用: 0.7296
  能量使用: 0.9843
  推理时间: 2.4418秒

批次 19:
  奖励值: 129.2018
  收益率: 0.2148
  距离: 29.2002
  内存使用: 0.6553
  能量使用: 0.9408
  推理时间: 2.2169秒

批次 20:
  奖励值: 121.9675
  收益率: 0.2037
  距离: 28.6288
  内存使用: 0.9159
  能量使用: 0.9203
  推理时间: 2.3196秒

批次 21:
  奖励值: 117.9090
  收益率: 0.1971
  距离: 28.6329
  内存使用: 0.6425
  能量使用: 0.9066
  推理时间: 2.2224秒

批次 22:
  奖励值: 136.3923
  收益率: 0.2233
  距离: 34.0381
  内存使用: 0.7534
  能量使用: 1.0237
  推理时间: 2.4819秒

批次 23:
  奖励值: 130.1761
  收益率: 0.2149
  距离: 31.9187
  内存使用: 0.7130
  能量使用: 1.0159
  推理时间: 2.3143秒

批次 24:
  奖励值: 149.1479
  收益率: 0.2566
  距离: 38.8643
  内存使用: 0.7987
  能量使用: 1.2412
  推理时间: 2.7612秒

批次 25:
  奖励值: 134.1871
  收益率: 0.2205
  距离: 33.2283
  内存使用: 0.7407
  能量使用: 1.0567
  推理时间: 2.5213秒

批次 26:
  奖励值: 125.6181
  收益率: 0.2122
  距离: 34.6273
  内存使用: 0.6829
  能量使用: 1.0536
  推理时间: 2.3160秒

批次 27:
  奖励值: 130.8230
  收益率: 0.2217
  距离: 34.6192
  内存使用: 0.7011
  能量使用: 1.0337
  推理时间: 2.4361秒

批次 28:
  奖励值: 120.2557
  收益率: 0.2019
  距离: 31.1060
  内存使用: 0.6625
  能量使用: 0.9201
  推理时间: 2.2063秒

批次 29:
  奖励值: 139.3417
  收益率: 0.2378
  距离: 32.7879
  内存使用: 0.7105
  能量使用: 1.0181
  推理时间: 2.4285秒

批次 30:
  奖励值: 119.1199
  收益率: 0.2006
  距离: 26.2901
  内存使用: 0.9124
  能量使用: 0.9577
  推理时间: 2.1256秒

批次 31:
  奖励值: 111.9796
  收益率: 0.1905
  距离: 28.3330
  内存使用: 0.9033
  能量使用: 0.8044
  推理时间: 2.0650秒

批次 32:
  奖励值: 148.8090
  收益率: 0.2440
  距离: 36.0726
  内存使用: 0.7639
  能量使用: 1.1454
  推理时间: 2.7265秒

批次 33:
  奖励值: 133.7285
  收益率: 0.2262
  距离: 34.8976
  内存使用: 0.7637
  能量使用: 1.0816
  推理时间: 2.4284秒

批次 34:
  奖励值: 132.3636
  收益率: 0.2244
  距离: 36.1068
  内存使用: 0.7329
  能量使用: 1.0330
  推理时间: 2.4040秒

批次 35:
  奖励值: 119.1732
  收益率: 0.1986
  距离: 29.0571
  内存使用: 0.9114
  能量使用: 0.8851
  推理时间: 2.2335秒

批次 36:
  奖励值: 139.7910
  收益率: 0.2358
  距离: 35.1441
  内存使用: 0.7871
  能量使用: 1.0866
  推理时间: 2.5916秒

批次 37:
  奖励值: 142.0272
  收益率: 0.2330
  距离: 34.8658
  内存使用: 0.8176
  能量使用: 1.0622
  推理时间: 2.4928秒

批次 38:
  奖励值: 119.4769
  收益率: 0.1970
  距离: 29.2965
  内存使用: 0.6262
  能量使用: 0.9234
  推理时间: 2.1151秒

批次 39:
  奖励值: 106.3545
  收益率: 0.1777
  距离: 26.0342
  内存使用: 0.8593
  能量使用: 0.7975
  推理时间: 1.9329秒

批次 40:
  奖励值: 115.1329
  收益率: 0.1933
  距离: 30.5313
  内存使用: 0.9057
  能量使用: 0.8971
  推理时间: 2.0747秒

批次 41:
  奖励值: 128.8833
  收益率: 0.2132
  距离: 32.1348
  内存使用: 0.6738
  能量使用: 1.0247
  推理时间: 2.3734秒

批次 42:
  奖励值: 129.8093
  收益率: 0.2149
  距离: 30.3731
  内存使用: 0.7397
  能量使用: 1.0686
  推理时间: 2.2881秒

批次 43:
  奖励值: 152.0305
  收益率: 0.2588
  距离: 39.3370
  内存使用: 0.8907
  能量使用: 1.1113
  推理时间: 2.7205秒

批次 44:
  奖励值: 145.8742
  收益率: 0.2478
  距离: 37.2291
  内存使用: 0.8328
  能量使用: 1.1897
  推理时间: 2.7010秒

批次 45:
  奖励值: 144.1256
  收益率: 0.2456
  距离: 35.3056
  内存使用: 0.7189
  能量使用: 1.1243
  推理时间: 2.6625秒

批次 46:
  奖励值: 139.6426
  收益率: 0.2302
  距离: 35.7409
  内存使用: 0.7102
  能量使用: 1.0635
  推理时间: 2.5119秒

批次 47:
  奖励值: 120.1143
  收益率: 0.2041
  距离: 30.1072
  内存使用: 0.9131
  能量使用: 0.9511
  推理时间: 2.1926秒

批次 48:
  奖励值: 128.3376
  收益率: 0.2187
  距离: 37.6450
  内存使用: 0.7138
  能量使用: 0.9659
  推理时间: 2.3616秒

批次 49:
  奖励值: 111.6759
  收益率: 0.1835
  距离: 25.9099
  内存使用: 0.9025
  能量使用: 0.8359
  推理时间: 2.0955秒

批次 50:
  奖励值: 114.9257
  收益率: 0.1960
  距离: 30.3731
  内存使用: 0.9094
  能量使用: 0.8660
  推理时间: 2.1077秒

批次 51:
  奖励值: 132.8506
  收益率: 0.2260
  距离: 36.0061
  内存使用: 0.6871
  能量使用: 1.0055
  推理时间: 2.5137秒

批次 52:
  奖励值: 126.0172
  收益率: 0.2175
  距离: 34.1200
  内存使用: 0.6908
  能量使用: 0.9548
  推理时间: 2.4009秒

批次 53:
  奖励值: 133.4112
  收益率: 0.2153
  距离: 29.9324
  内存使用: 0.6499
  能量使用: 0.9951
  推理时间: 2.3584秒

批次 54:
  奖励值: 125.7726
  收益率: 0.2097
  距离: 32.6647
  内存使用: 0.6298
  能量使用: 0.9604
  推理时间: 2.3471秒

批次 55:
  奖励值: 117.5962
  收益率: 0.1924
  距离: 25.7332
  内存使用: 0.6245
  能量使用: 0.8498
  推理时间: 2.1706秒

批次 56:
  奖励值: 123.5526
  收益率: 0.2080
  距离: 29.9036
  内存使用: 0.6493
  能量使用: 0.9419
  推理时间: 2.2496秒

批次 57:
  奖励值: 115.0781
  收益率: 0.1927
  距离: 28.0803
  内存使用: 0.9051
  能量使用: 0.8583
  推理时间: 2.1215秒

批次 58:
  奖励值: 136.1161
  收益率: 0.2272
  距离: 35.4654
  内存使用: 0.8190
  能量使用: 1.0833
  推理时间: 2.4920秒

批次 59:
  奖励值: 137.4124
  收益率: 0.2264
  距离: 32.4458
  内存使用: 0.7202
  能量使用: 1.0091
  推理时间: 2.5580秒

批次 60:
  奖励值: 132.4181
  收益率: 0.2162
  距离: 29.8067
  内存使用: 0.7421
  能量使用: 1.0150
  推理时间: 2.3326秒

批次 61:
  奖励值: 132.1852
  收益率: 0.2241
  距离: 34.2500
  内存使用: 0.7451
  能量使用: 1.0345
  推理时间: 2.3720秒

批次 62:
  奖励值: 117.5048
  收益率: 0.1968
  距离: 26.9390
  内存使用: 0.9158
  能量使用: 0.9356
  推理时间: 2.1378秒

批次 63:
  奖励值: 132.3109
  收益率: 0.2227
  距离: 34.6669
  内存使用: 0.7500
  能量使用: 0.9612
  推理时间: 2.3790秒

批次 64:
  奖励值: 132.0203
  收益率: 0.2250
  距离: 35.3106
  内存使用: 0.6696
  能量使用: 0.9793
  推理时间: 2.3620秒

批次 65:
  奖励值: 136.1905
  收益率: 0.2230
  距离: 30.2278
  内存使用: 0.7328
  能量使用: 1.0584
  推理时间: 2.4820秒

批次 66:
  奖励值: 134.0897
  收益率: 0.2206
  距离: 35.7927
  内存使用: 0.7675
  能量使用: 1.0605
  推理时间: 2.5326秒

批次 67:
  奖励值: 141.4773
  收益率: 0.2345
  距离: 31.9187
  内存使用: 0.7096
  能量使用: 1.0169
  推理时间: 2.5754秒

批次 68:
  奖励值: 119.0216
  收益率: 0.1989
  距离: 32.3639
  内存使用: 0.6879
  能量使用: 0.9048
  推理时间: 2.2392秒

批次 69:
  奖励值: 148.7613
  收益率: 0.2538
  距离: 40.7473
  内存使用: 0.8086
  能量使用: 1.1215
  推理时间: 2.7643秒

批次 70:
  奖励值: 135.6336
  收益率: 0.2288
  距离: 34.3261
  内存使用: 0.7413
  能量使用: 1.0654
  推理时间: 2.5258秒

批次 71:
  奖励值: 122.9937
  收益率: 0.2078
  距离: 31.4128
  内存使用: 0.6480
  能量使用: 0.9518
  推理时间: 2.2804秒

批次 72:
  奖励值: 134.1093
  收益率: 0.2236
  距离: 34.6894
  内存使用: 0.7389
  能量使用: 0.9532
  推理时间: 2.5262秒

批次 73:
  奖励值: 126.0746
  收益率: 0.2127
  距离: 32.0510
  内存使用: 0.6528
  能量使用: 0.9471
  推理时间: 2.2962秒

批次 74:
  奖励值: 135.9138
  收益率: 0.2285
  距离: 35.6828
  内存使用: 0.8177
  能量使用: 1.0718
  推理时间: 2.5064秒

批次 75:
  奖励值: 124.1540
  收益率: 0.2094
  距离: 31.8810
  内存使用: 0.6241
  能量使用: 0.9868
  推理时间: 2.4581秒

批次 76:
  奖励值: 119.7495
  收益率: 0.1976
  距离: 28.7081
  内存使用: 0.9113
  能量使用: 0.8816
  推理时间: 2.2153秒

批次 77:
  奖励值: 117.0797
  收益率: 0.1972
  距离: 30.6475
  内存使用: 0.9092
  能量使用: 0.8838
  推理时间: 2.1892秒

批次 78:
  奖励值: 114.1912
  收益率: 0.1938
  距离: 32.0012
  内存使用: 0.9105
  能量使用: 0.9057
  推理时间: 2.0991秒

批次 79:
  奖励值: 115.3330
  收益率: 0.1962
  距离: 29.0668
  内存使用: 0.9124
  能量使用: 0.9548
  推理时间: 2.1585秒

批次 80:
  奖励值: 103.0369
  收益率: 0.1758
  距离: 25.0468
  内存使用: 0.9108
  能量使用: 0.7685
  推理时间: 1.9732秒

批次 81:
  奖励值: 125.7382
  收益率: 0.2145
  距离: 33.6612
  内存使用: 0.6605
  能量使用: 1.0019
  推理时间: 2.4137秒

批次 82:
  奖励值: 126.4526
  收益率: 0.2067
  距离: 31.2375
  内存使用: 0.6975
  能量使用: 0.9598
  推理时间: 2.3398秒

批次 83:
  奖励值: 138.1019
  收益率: 0.2234
  距离: 31.6878
  内存使用: 0.6687
  能量使用: 1.0396
  推理时间: 2.4800秒

批次 84:
  奖励值: 104.8049
  收益率: 0.1754
  距离: 28.0642
  内存使用: 0.9099
  能量使用: 0.8160
  推理时间: 1.9532秒

批次 85:
  奖励值: 112.9764
  收益率: 0.1857
  距离: 28.3589
  内存使用: 0.9194
  能量使用: 0.9689
  推理时间: 2.1601秒

批次 86:
  奖励值: 132.7950
  收益率: 0.2172
  距离: 30.0500
  内存使用: 0.7265
  能量使用: 1.0334
  推理时间: 2.3514秒

批次 87:
  奖励值: 125.1409
  收益率: 0.2048
  距离: 30.8503
  内存使用: 0.6826
  能量使用: 0.9852
  推理时间: 2.2636秒

批次 88:
  奖励值: 125.0321
  收益率: 0.2145
  距离: 32.7402
  内存使用: 0.6970
  能量使用: 0.8986
  推理时间: 2.2963秒

批次 89:
  奖励值: 120.3759
  收益率: 0.2007
  距离: 29.7913
  内存使用: 0.9129
  能量使用: 0.9440
  推理时间: 2.2272秒

批次 90:
  奖励值: 139.4380
  收益率: 0.2271
  距离: 31.4217
  内存使用: 0.7115
  能量使用: 1.1906
  推理时间: 2.4778秒

批次 91:
  奖励值: 131.4864
  收益率: 0.2241
  距离: 34.7263
  内存使用: 0.7022
  能量使用: 0.9987
  推理时间: 2.4274秒

批次 92:
  奖励值: 136.8272
  收益率: 0.2275
  距离: 33.5505
  内存使用: 0.8021
  能量使用: 0.9930
  推理时间: 2.5120秒

批次 93:
  奖励值: 127.4565
  收益率: 0.2133
  距离: 30.8388
  内存使用: 0.6720
  能量使用: 0.9762
  推理时间: 2.2439秒

批次 94:
  奖励值: 131.1239
  收益率: 0.2197
  距离: 35.1309
  内存使用: 0.6552
  能量使用: 0.9660
  推理时间: 2.3321秒

批次 95:
  奖励值: 131.6468
  收益率: 0.2212
  距离: 32.7425
  内存使用: 0.6940
  能量使用: 0.8698
  推理时间: 2.3378秒

批次 96:
  奖励值: 129.0272
  收益率: 0.2138
  距离: 28.7267
  内存使用: 0.6644
  能量使用: 0.9546
  推理时间: 2.2718秒

批次 97:
  奖励值: 132.4842
  收益率: 0.2174
  距离: 28.0369
  内存使用: 0.6714
  能量使用: 0.9232
  推理时间: 2.3596秒

批次 98:
  奖励值: 129.9798
  收益率: 0.2159
  距离: 30.7809
  内存使用: 0.7084
  能量使用: 0.9511
  推理时间: 2.3812秒

批次 99:
  奖励值: 130.4947
  收益率: 0.2222
  距离: 34.4705
  内存使用: 0.6777
  能量使用: 1.0624
  推理时间: 2.4001秒

批次 100:
  奖励值: 127.1347
  收益率: 0.2108
  距离: 28.6897
  内存使用: 0.6436
  能量使用: 0.9973
  推理时间: 2.3133秒


==================== 总结 ====================
平均收益率: 0.2145
平均能量使用: 0.9843
平均推理时间: 2.3572秒
