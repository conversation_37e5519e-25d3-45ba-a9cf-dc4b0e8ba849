推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 100.1297
  收益率: 0.4878
  距离: 26.3556
  内存使用: 0.4592
  能量使用: 0.7257
  推理时间: 1.8556秒

批次 2:
  奖励值: 100.0691
  收益率: 0.4847
  距离: 23.0114
  内存使用: 0.5285
  能量使用: 0.7218
  推理时间: 1.8376秒

批次 3:
  奖励值: 94.3877
  收益率: 0.4763
  距离: 27.2114
  内存使用: 0.4832
  能量使用: 0.7300
  推理时间: 1.7503秒

批次 4:
  奖励值: 96.2331
  收益率: 0.4796
  距离: 25.0107
  内存使用: 0.5153
  能量使用: 0.8078
  推理时间: 1.8328秒

批次 5:
  奖励值: 89.2409
  收益率: 0.4562
  距离: 24.4831
  内存使用: 0.4694
  能量使用: 0.7659
  推理时间: 1.7176秒

批次 6:
  奖励值: 91.7532
  收益率: 0.4741
  距离: 25.4913
  内存使用: 0.4645
  能量使用: 0.7379
  推理时间: 1.7567秒

批次 7:
  奖励值: 92.6398
  收益率: 0.4697
  距离: 24.4990
  内存使用: 0.4705
  能量使用: 0.7509
  推理时间: 1.7165秒

批次 8:
  奖励值: 89.2082
  收益率: 0.4576
  距离: 23.2128
  内存使用: 0.4606
  能量使用: 0.7103
  推理时间: 1.6891秒

批次 9:
  奖励值: 91.9424
  收益率: 0.4816
  距离: 24.3988
  内存使用: 0.5088
  能量使用: 0.8077
  推理时间: 1.8829秒

批次 10:
  奖励值: 99.4608
  收益率: 0.4957
  距离: 28.5078
  内存使用: 0.5524
  能量使用: 0.8182
  推理时间: 1.9022秒

批次 11:
  奖励值: 96.1537
  收益率: 0.4723
  距离: 26.3981
  内存使用: 0.5347
  能量使用: 0.7598
  推理时间: 1.8363秒

批次 12:
  奖励值: 92.6560
  收益率: 0.4693
  距离: 27.4295
  内存使用: 0.5009
  能量使用: 0.7575
  推理时间: 1.8038秒

批次 13:
  奖励值: 89.7944
  收益率: 0.4724
  距离: 26.2317
  内存使用: 0.4328
  能量使用: 0.7467
  推理时间: 1.7374秒

批次 14:
  奖励值: 91.1911
  收益率: 0.4543
  距离: 24.5108
  内存使用: 0.4367
  能量使用: 0.6701
  推理时间: 1.7266秒

批次 15:
  奖励值: 102.9169
  收益率: 0.5164
  距离: 28.4552
  内存使用: 0.4902
  能量使用: 0.8207
  推理时间: 1.9856秒

批次 16:
  奖励值: 90.8651
  收益率: 0.4599
  距离: 23.4979
  内存使用: 0.4137
  能量使用: 0.7584
  推理时间: 1.6820秒

批次 17:
  奖励值: 97.7281
  收益率: 0.4921
  距离: 28.2521
  内存使用: 0.5144
  能量使用: 0.7773
  推理时间: 1.8491秒

批次 18:
  奖励值: 84.2806
  收益率: 0.4411
  距离: 22.7282
  内存使用: 0.4067
  能量使用: 0.7040
  推理时间: 1.5779秒

批次 19:
  奖励值: 95.6437
  收益率: 0.4693
  距离: 25.8207
  内存使用: 0.5438
  能量使用: 0.7481
  推理时间: 1.7822秒

批次 20:
  奖励值: 89.7677
  收益率: 0.4508
  距离: 22.7016
  内存使用: 0.4551
  能量使用: 0.7078
  推理时间: 1.7590秒

批次 21:
  奖励值: 88.1839
  收益率: 0.4442
  距离: 23.6117
  内存使用: 0.4418
  能量使用: 0.7788
  推理时间: 1.6850秒

批次 22:
  奖励值: 95.9165
  收益率: 0.5016
  距离: 28.2403
  内存使用: 0.5327
  能量使用: 0.7941
  推理时间: 1.8516秒

批次 23:
  奖励值: 93.6511
  收益率: 0.4712
  距离: 27.6407
  内存使用: 0.4828
  能量使用: 0.7743
  推理时间: 1.7680秒

批次 24:
  奖励值: 91.1589
  收益率: 0.4798
  距离: 25.6207
  内存使用: 0.4316
  能量使用: 0.8300
  推理时间: 1.8175秒

批次 25:
  奖励值: 91.7529
  收益率: 0.4547
  距离: 24.3208
  内存使用: 0.4748
  能量使用: 0.6988
  推理时间: 1.7354秒

批次 26:
  奖励值: 91.1577
  收益率: 0.4542
  距离: 23.9107
  内存使用: 0.4391
  能量使用: 0.7349
  推理时间: 1.6977秒

批次 27:
  奖励值: 95.6825
  收益率: 0.4834
  距离: 26.0439
  内存使用: 0.5258
  能量使用: 0.8653
  推理时间: 1.7921秒

批次 28:
  奖励值: 101.8753
  收益率: 0.5139
  距离: 28.3232
  内存使用: 0.4854
  能量使用: 0.8408
  推理时间: 1.9340秒

批次 29:
  奖励值: 90.1786
  收益率: 0.4551
  距离: 24.0833
  内存使用: 0.4432
  能量使用: 0.7162
  推理时间: 1.6392秒

批次 30:
  奖励值: 91.0047
  收益率: 0.4624
  距离: 25.6035
  内存使用: 0.4041
  能量使用: 0.7831
  推理时间: 1.7392秒

批次 31:
  奖励值: 92.9511
  收益率: 0.4712
  距离: 26.8294
  内存使用: 0.4630
  能量使用: 0.8054
  推理时间: 1.8367秒

批次 32:
  奖励值: 103.8651
  收益率: 0.5141
  距离: 24.1230
  内存使用: 0.5113
  能量使用: 0.8353
  推理时间: 1.9044秒

批次 33:
  奖励值: 97.3602
  收益率: 0.4888
  距离: 27.3106
  内存使用: 0.3957
  能量使用: 0.7732
  推理时间: 1.8586秒

批次 34:
  奖励值: 96.6908
  收益率: 0.4909
  距离: 26.3284
  内存使用: 0.5296
  能量使用: 0.7604
  推理时间: 1.8154秒

批次 35:
  奖励值: 89.8993
  收益率: 0.4708
  距离: 27.0089
  内存使用: 0.4532
  能量使用: 0.7393
  推理时间: 1.7989秒

批次 36:
  奖励值: 96.3386
  收益率: 0.4748
  距离: 24.2690
  内存使用: 0.4997
  能量使用: 0.7540
  推理时间: 1.8220秒

批次 37:
  奖励值: 91.8383
  收益率: 0.4604
  距离: 23.3436
  内存使用: 0.4515
  能量使用: 0.7284
  推理时间: 1.7310秒

批次 38:
  奖励值: 88.2935
  收益率: 0.4454
  距离: 25.1607
  内存使用: 0.4762
  能量使用: 0.7762
  推理时间: 1.6931秒

批次 39:
  奖励值: 98.1913
  收益率: 0.4870
  距离: 26.5797
  内存使用: 0.4818
  能量使用: 0.7558
  推理时间: 1.8475秒

批次 40:
  奖励值: 91.1162
  收益率: 0.4856
  距离: 25.9206
  内存使用: 0.4944
  能量使用: 0.7527
  推理时间: 1.8110秒

批次 41:
  奖励值: 95.5693
  收益率: 0.4795
  距离: 24.6070
  内存使用: 0.5162
  能量使用: 0.7381
  推理时间: 1.8251秒

批次 42:
  奖励值: 100.2975
  收益率: 0.5015
  距离: 27.8820
  内存使用: 0.4838
  能量使用: 0.8391
  推理时间: 1.9141秒

批次 43:
  奖励值: 98.0061
  收益率: 0.4840
  距离: 25.9129
  内存使用: 0.4393
  能量使用: 0.8355
  推理时间: 1.8583秒

批次 44:
  奖励值: 95.9621
  收益率: 0.4789
  距离: 22.4914
  内存使用: 0.4625
  能量使用: 0.7217
  推理时间: 1.7726秒

批次 45:
  奖励值: 100.6348
  收益率: 0.4830
  距离: 27.2383
  内存使用: 0.5362
  能量使用: 0.7558
  推理时间: 1.8738秒

批次 46:
  奖励值: 92.0085
  收益率: 0.4603
  距离: 24.3894
  内存使用: 0.4199
  能量使用: 0.7737
  推理时间: 1.7568秒

批次 47:
  奖励值: 95.9919
  收益率: 0.4885
  距离: 24.0381
  内存使用: 0.4366
  能量使用: 0.7415
  推理时间: 1.8025秒

批次 48:
  奖励值: 91.3789
  收益率: 0.4658
  距离: 26.9915
  内存使用: 0.4785
  能量使用: 0.7723
  推理时间: 1.7830秒

批次 49:
  奖励值: 97.6627
  收益率: 0.4842
  距离: 26.6441
  内存使用: 0.4969
  能量使用: 0.7336
  推理时间: 1.7992秒

批次 50:
  奖励值: 95.2437
  收益率: 0.4696
  距离: 26.0724
  内存使用: 0.4859
  能量使用: 0.7543
  推理时间: 1.7781秒

批次 51:
  奖励值: 88.9512
  收益率: 0.4570
  距离: 23.9793
  内存使用: 0.4304
  能量使用: 0.6880
  推理时间: 1.7207秒

批次 52:
  奖励值: 95.6313
  收益率: 0.4877
  距离: 25.9892
  内存使用: 0.5367
  能量使用: 0.8116
  推理时间: 1.8234秒

批次 53:
  奖励值: 95.8545
  收益率: 0.4996
  距离: 31.5552
  内存使用: 0.5165
  能量使用: 0.8193
  推理时间: 1.9028秒

批次 54:
  奖励值: 95.7836
  收益率: 0.4723
  距离: 25.3812
  内存使用: 0.4977
  能量使用: 0.7540
  推理时间: 1.7544秒

批次 55:
  奖励值: 92.4656
  收益率: 0.4635
  距离: 23.5469
  内存使用: 0.4187
  能量使用: 0.7031
  推理时间: 1.7396秒

批次 56:
  奖励值: 99.1199
  收益率: 0.4980
  距离: 27.2942
  内存使用: 0.4782
  能量使用: 0.7879
  推理时间: 1.8405秒

批次 57:
  奖励值: 94.0342
  收益率: 0.4744
  距离: 25.6524
  内存使用: 0.5344
  能量使用: 0.7447
  推理时间: 1.7836秒

批次 58:
  奖励值: 96.4786
  收益率: 0.4830
  距离: 28.0148
  内存使用: 0.4734
  能量使用: 0.7547
  推理时间: 1.8503秒

批次 59:
  奖励值: 91.0638
  收益率: 0.4594
  距离: 23.4317
  内存使用: 0.4755
  能量使用: 0.7652
  推理时间: 1.7014秒

批次 60:
  奖励值: 100.4806
  收益率: 0.4954
  距离: 26.3389
  内存使用: 0.4789
  能量使用: 0.8210
  推理时间: 1.9001秒

批次 61:
  奖励值: 87.7778
  收益率: 0.4479
  距离: 26.5669
  内存使用: 0.7189
  能量使用: 0.7586
  推理时间: 1.7566秒

批次 62:
  奖励值: 85.7407
  收益率: 0.4441
  距离: 23.6218
  内存使用: 0.3402
  能量使用: 0.7567
  推理时间: 1.6546秒

批次 63:
  奖励值: 102.1009
  收益率: 0.4947
  距离: 27.9684
  内存使用: 0.5870
  能量使用: 0.8428
  推理时间: 1.9268秒

批次 64:
  奖励值: 102.0808
  收益率: 0.5048
  距离: 24.0513
  内存使用: 0.4166
  能量使用: 0.7705
  推理时间: 1.8308秒

批次 65:
  奖励值: 94.7968
  收益率: 0.4774
  距离: 26.6844
  内存使用: 0.5302
  能量使用: 0.7918
  推理时间: 1.7874秒

批次 66:
  奖励值: 99.9561
  收益率: 0.5011
  距离: 25.4915
  内存使用: 0.5140
  能量使用: 0.8823
  推理时间: 1.9361秒

批次 67:
  奖励值: 96.7625
  收益率: 0.4785
  距离: 25.3406
  内存使用: 0.4659
  能量使用: 0.8686
  推理时间: 1.8380秒

批次 68:
  奖励值: 103.3006
  收益率: 0.5128
  距离: 27.8264
  内存使用: 0.5491
  能量使用: 0.8233
  推理时间: 1.9658秒

批次 69:
  奖励值: 90.5660
  收益率: 0.4659
  距离: 25.4304
  内存使用: 0.4839
  能量使用: 0.7537
  推理时间: 1.7707秒

批次 70:
  奖励值: 99.5289
  收益率: 0.4824
  距离: 21.0236
  内存使用: 0.5109
  能量使用: 0.8078
  推理时间: 1.8564秒

批次 71:
  奖励值: 93.9867
  收益率: 0.4619
  距离: 22.7889
  内存使用: 0.4887
  能量使用: 0.7490
  推理时间: 1.7260秒

批次 72:
  奖励值: 82.1180
  收益率: 0.4461
  距离: 23.3573
  内存使用: 0.3743
  能量使用: 0.6325
  推理时间: 1.5974秒

批次 73:
  奖励值: 89.0120
  收益率: 0.4505
  距离: 24.6080
  内存使用: 0.4532
  能量使用: 0.7149
  推理时间: 1.6622秒

批次 74:
  奖励值: 90.2207
  收益率: 0.4440
  距离: 23.0481
  内存使用: 0.4342
  能量使用: 0.6763
  推理时间: 1.7361秒

批次 75:
  奖励值: 97.3639
  收益率: 0.4863
  距离: 26.9642
  内存使用: 0.5051
  能量使用: 0.8029
  推理时间: 1.8743秒

批次 76:
  奖励值: 88.0171
  收益率: 0.4442
  距离: 23.6065
  内存使用: 0.3981
  能量使用: 0.7612
  推理时间: 1.6302秒

批次 77:
  奖励值: 99.8146
  收益率: 0.5007
  距离: 25.3004
  内存使用: 0.4907
  能量使用: 0.7797
  推理时间: 1.9168秒

批次 78:
  奖励值: 100.8115
  收益率: 0.4808
  距离: 24.9693
  内存使用: 0.5314
  能量使用: 0.8763
  推理时间: 1.9015秒

批次 79:
  奖励值: 87.1848
  收益率: 0.4452
  距离: 22.0858
  内存使用: 0.4067
  能量使用: 0.7379
  推理时间: 1.7085秒

批次 80:
  奖励值: 93.0426
  收益率: 0.4981
  距离: 27.2491
  内存使用: 0.4634
  能量使用: 0.8023
  推理时间: 1.8643秒

批次 81:
  奖励值: 95.9241
  收益率: 0.4796
  距离: 27.8421
  内存使用: 0.4977
  能量使用: 0.8120
  推理时间: 1.8023秒

批次 82:
  奖励值: 93.4329
  收益率: 0.4681
  距离: 26.1014
  内存使用: 0.4623
  能量使用: 0.7705
  推理时间: 1.8061秒

批次 83:
  奖励值: 95.5458
  收益率: 0.4763
  距离: 26.6446
  内存使用: 0.4452
  能量使用: 0.8186
  推理时间: 1.8220秒

批次 84:
  奖励值: 90.3362
  收益率: 0.4547
  距离: 24.7524
  内存使用: 0.4255
  能量使用: 0.7497
  推理时间: 1.7027秒

批次 85:
  奖励值: 91.9991
  收益率: 0.4590
  距离: 22.8048
  内存使用: 0.4727
  能量使用: 0.7244
  推理时间: 1.7135秒

批次 86:
  奖励值: 96.9081
  收益率: 0.4856
  距离: 27.9099
  内存使用: 0.4605
  能量使用: 0.8135
  推理时间: 1.8425秒

批次 87:
  奖励值: 80.6309
  收益率: 0.4170
  距离: 20.7128
  内存使用: 0.4129
  能量使用: 0.6124
  推理时间: 1.5133秒

批次 88:
  奖励值: 90.5995
  收益率: 0.4723
  距离: 24.1684
  内存使用: 0.4706
  能量使用: 0.7619
  推理时间: 1.7401秒

批次 89:
  奖励值: 94.5072
  收益率: 0.4937
  距离: 29.6826
  内存使用: 0.4857
  能量使用: 0.8174
  推理时间: 1.8587秒

批次 90:
  奖励值: 97.0045
  收益率: 0.4971
  距离: 26.8394
  内存使用: 0.5005
  能量使用: 0.8511
  推理时间: 1.9194秒

批次 91:
  奖励值: 94.7136
  收益率: 0.4761
  距离: 26.0406
  内存使用: 0.4960
  能量使用: 0.8363
  推理时间: 1.8196秒

批次 92:
  奖励值: 83.4755
  收益率: 0.4371
  距离: 23.2104
  内存使用: 0.3944
  能量使用: 0.6442
  推理时间: 1.6155秒

批次 93:
  奖励值: 91.9570
  收益率: 0.4663
  距离: 24.1064
  内存使用: 0.4148
  能量使用: 0.7647
  推理时间: 1.7486秒

批次 94:
  奖励值: 88.0377
  收益率: 0.4653
  距离: 22.5254
  内存使用: 0.4628
  能量使用: 0.7365
  推理时间: 1.7432秒

批次 95:
  奖励值: 90.5240
  收益率: 0.4577
  距离: 26.7990
  内存使用: 0.4237
  能量使用: 0.7294
  推理时间: 1.7215秒

批次 96:
  奖励值: 88.3936
  收益率: 0.4517
  距离: 21.4883
  内存使用: 0.4032
  能量使用: 0.7213
  推理时间: 1.6222秒

批次 97:
  奖励值: 98.7654
  收益率: 0.4815
  距离: 22.7816
  内存使用: 0.5527
  能量使用: 0.7348
  推理时间: 1.8487秒

批次 98:
  奖励值: 81.4107
  收益率: 0.4343
  距离: 23.1277
  内存使用: 0.3971
  能量使用: 0.6538
  推理时间: 1.5577秒

批次 99:
  奖励值: 99.8426
  收益率: 0.4934
  距离: 24.8516
  内存使用: 0.4861
  能量使用: 0.7771
  推理时间: 1.8578秒

批次 100:
  奖励值: 86.8323
  收益率: 0.4332
  距离: 25.2932
  内存使用: 0.7042
  能量使用: 0.7381
  推理时间: 1.7056秒


==================== 总结 ====================
平均收益率: 0.4732
平均能量使用: 0.7650
平均推理时间: 1.7854秒
