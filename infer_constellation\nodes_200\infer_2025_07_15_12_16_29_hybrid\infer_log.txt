推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 44.3301
  收益率: 0.5682
  距离: 12.4933
  内存使用: 0.0952
  能量使用: 0.3512
  推理时间: 1.0329秒

批次 2:
  奖励值: 48.5938
  收益率: 0.5901
  距离: 11.7103
  内存使用: 0.1072
  能量使用: 0.3950
  推理时间: 0.9820秒

批次 3:
  奖励值: 46.9625
  收益率: 0.6036
  距离: 12.7829
  内存使用: 0.1115
  能量使用: 0.3806
  推理时间: 0.9341秒

批次 4:
  奖励值: 53.0068
  收益率: 0.6427
  距离: 14.9777
  内存使用: 0.1816
  能量使用: 0.4271
  推理时间: 1.0115秒

批次 5:
  奖励值: 49.7399
  收益率: 0.6484
  距离: 15.7372
  内存使用: 0.1713
  能量使用: 0.4117
  推理时间: 1.0136秒

批次 6:
  奖励值: 57.2322
  收益率: 0.6591
  距离: 12.1238
  内存使用: 0.1394
  能量使用: 0.4724
  推理时间: 1.0675秒

批次 7:
  奖励值: 61.1550
  收益率: 0.6866
  距离: 12.8992
  内存使用: 0.1233
  能量使用: 0.4674
  推理时间: 1.1170秒

批次 8:
  奖励值: 51.4920
  收益率: 0.6254
  距离: 13.6597
  内存使用: 0.1421
  能量使用: 0.4142
  推理时间: 1.0156秒

批次 9:
  奖励值: 52.3239
  收益率: 0.6352
  距离: 11.8052
  内存使用: 0.1310
  能量使用: 0.4160
  推理时间: 1.0058秒

批次 10:
  奖励值: 53.4528
  收益率: 0.6351
  距离: 15.7080
  内存使用: 0.1937
  能量使用: 0.3927
  推理时间: 1.0249秒

批次 11:
  奖励值: 48.1955
  收益率: 0.5991
  距离: 13.7782
  内存使用: 0.0706
  能量使用: 0.3602
  推理时间: 0.9595秒

批次 12:
  奖励值: 51.3745
  收益率: 0.6499
  距离: 15.4400
  内存使用: 0.1598
  能量使用: 0.4513
  推理时间: 1.0173秒

批次 13:
  奖励值: 51.9039
  收益率: 0.6517
  距离: 14.5986
  内存使用: 0.1403
  能量使用: 0.4533
  推理时间: 1.0572秒

批次 14:
  奖励值: 62.0885
  收益率: 0.7036
  距离: 12.4008
  内存使用: 0.1760
  能量使用: 0.4463
  推理时间: 1.1565秒

批次 15:
  奖励值: 51.8990
  收益率: 0.6414
  距离: 14.0548
  内存使用: 0.1403
  能量使用: 0.4068
  推理时间: 1.0175秒

批次 16:
  奖励值: 50.1614
  收益率: 0.6098
  距离: 13.5417
  内存使用: 0.1145
  能量使用: 0.4209
  推理时间: 0.9674秒

批次 17:
  奖励值: 50.7544
  收益率: 0.5931
  距离: 11.9954
  内存使用: 0.1243
  能量使用: 0.3714
  推理时间: 0.9699秒

批次 18:
  奖励值: 53.8598
  收益率: 0.6358
  距离: 13.7900
  内存使用: 0.2025
  能量使用: 0.4879
  推理时间: 1.0500秒

批次 19:
  奖励值: 48.4210
  收益率: 0.6095
  距离: 12.8410
  内存使用: 0.0998
  能量使用: 0.4172
  推理时间: 0.9586秒

批次 20:
  奖励值: 49.5605
  收益率: 0.6065
  距离: 13.1563
  内存使用: 0.0747
  能量使用: 0.4480
  推理时间: 0.9717秒

批次 21:
  奖励值: 57.1899
  收益率: 0.6746
  距离: 15.5731
  内存使用: 0.2058
  能量使用: 0.4413
  推理时间: 1.1030秒

批次 22:
  奖励值: 52.7369
  收益率: 0.6447
  距离: 13.4124
  内存使用: 0.1594
  能量使用: 0.3544
  推理时间: 0.9861秒

批次 23:
  奖励值: 51.8465
  收益率: 0.6437
  距离: 15.3144
  内存使用: 0.1516
  能量使用: 0.4411
  推理时间: 1.0720秒

批次 24:
  奖励值: 51.2310
  收益率: 0.6266
  距离: 14.8255
  内存使用: 0.1138
  能量使用: 0.3987
  推理时间: 1.0085秒

批次 25:
  奖励值: 48.3514
  收益率: 0.5976
  距离: 11.1429
  内存使用: 0.1240
  能量使用: 0.4302
  推理时间: 0.9191秒

批次 26:
  奖励值: 52.4038
  收益率: 0.6481
  距离: 15.3927
  内存使用: 0.1337
  能量使用: 0.4661
  推理时间: 1.0312秒

批次 27:
  奖励值: 52.2406
  收益率: 0.6572
  距离: 15.6529
  内存使用: 0.1946
  能量使用: 0.4713
  推理时间: 1.0517秒

批次 28:
  奖励值: 46.4727
  收益率: 0.5971
  距离: 12.8930
  内存使用: 0.1054
  能量使用: 0.4563
  推理时间: 0.9629秒

批次 29:
  奖励值: 53.1729
  收益率: 0.6263
  距离: 16.0849
  内存使用: 0.1338
  能量使用: 0.4355
  推理时间: 1.0568秒

批次 30:
  奖励值: 53.3415
  收益率: 0.6733
  距离: 13.8935
  内存使用: 0.1880
  能量使用: 0.4356
  推理时间: 1.0568秒

批次 31:
  奖励值: 49.5033
  收益率: 0.6377
  距离: 16.2124
  内存使用: 0.1149
  能量使用: 0.4661
  推理时间: 1.0004秒

批次 32:
  奖励值: 50.2615
  收益率: 0.6357
  距离: 13.7221
  内存使用: 0.1339
  能量使用: 0.4212
  推理时间: 1.0365秒

批次 33:
  奖励值: 50.4848
  收益率: 0.6206
  距离: 11.7438
  内存使用: 0.0911
  能量使用: 0.3955
  推理时间: 0.9472秒

批次 34:
  奖励值: 51.5932
  收益率: 0.6335
  距离: 12.1338
  内存使用: 0.1220
  能量使用: 0.3578
  推理时间: 0.9774秒

批次 35:
  奖励值: 49.5312
  收益率: 0.6167
  距离: 12.3929
  内存使用: 0.0938
  能量使用: 0.4509
  推理时间: 0.9534秒

批次 36:
  奖励值: 48.6365
  收益率: 0.6233
  距离: 12.3612
  内存使用: 0.1030
  能量使用: 0.4628
  推理时间: 0.9525秒

批次 37:
  奖励值: 51.1804
  收益率: 0.6458
  距离: 14.5000
  内存使用: 0.0951
  能量使用: 0.4442
  推理时间: 1.0088秒

批次 38:
  奖励值: 48.9568
  收益率: 0.6077
  距离: 12.7404
  内存使用: 0.1207
  能量使用: 0.4252
  推理时间: 0.9538秒

批次 39:
  奖励值: 45.0284
  收益率: 0.5684
  距离: 12.4283
  内存使用: 0.0992
  能量使用: 0.4158
  推理时间: 0.9112秒

批次 40:
  奖励值: 45.3337
  收益率: 0.5810
  距离: 12.8556
  内存使用: 0.0768
  能量使用: 0.4001
  推理时间: 0.8659秒

批次 41:
  奖励值: 50.6255
  收益率: 0.6481
  距离: 12.4554
  内存使用: 0.0585
  能量使用: 0.4352
  推理时间: 0.9706秒

批次 42:
  奖励值: 52.0785
  收益率: 0.6432
  距离: 14.2593
  内存使用: 0.1616
  能量使用: 0.4220
  推理时间: 1.0125秒

批次 43:
  奖励值: 50.4847
  收益率: 0.6273
  距离: 14.3072
  内存使用: 0.0955
  能量使用: 0.4279
  推理时间: 0.9586秒

批次 44:
  奖励值: 44.5268
  收益率: 0.5741
  距离: 15.1197
  内存使用: 0.0705
  能量使用: 0.3562
  推理时间: 0.8952秒

批次 45:
  奖励值: 46.6248
  收益率: 0.5871
  距离: 13.4761
  内存使用: 0.1086
  能量使用: 0.3656
  推理时间: 0.8995秒

批次 46:
  奖励值: 55.2594
  收益率: 0.6856
  距离: 17.0687
  内存使用: 0.1361
  能量使用: 0.4764
  推理时间: 1.1025秒

批次 47:
  奖励值: 47.7337
  收益率: 0.6115
  距离: 14.2347
  内存使用: 0.0791
  能量使用: 0.4187
  推理时间: 0.9463秒

批次 48:
  奖励值: 49.4745
  收益率: 0.6381
  距离: 13.6831
  内存使用: 0.1066
  能量使用: 0.4613
  推理时间: 1.0048秒

批次 49:
  奖励值: 46.7751
  收益率: 0.5935
  距离: 12.5313
  内存使用: 0.0860
  能量使用: 0.4326
  推理时间: 0.9254秒

批次 50:
  奖励值: 46.7933
  收益率: 0.5814
  距离: 12.3035
  内存使用: 0.1099
  能量使用: 0.4000
  推理时间: 0.9276秒

批次 51:
  奖励值: 48.7205
  收益率: 0.5957
  距离: 12.8637
  内存使用: 0.0967
  能量使用: 0.3986
  推理时间: 0.9278秒

批次 52:
  奖励值: 48.9141
  收益率: 0.6043
  距离: 15.2169
  内存使用: 0.1547
  能量使用: 0.4003
  推理时间: 0.9481秒

批次 53:
  奖励值: 63.1841
  收益率: 0.7085
  距离: 15.2502
  内存使用: 0.2225
  能量使用: 0.5591
  推理时间: 1.1635秒

批次 54:
  奖励值: 44.3345
  收益率: 0.5730
  距离: 15.2117
  内存使用: 0.0899
  能量使用: 0.4293
  推理时间: 0.9059秒

批次 55:
  奖励值: 50.7136
  收益率: 0.6369
  距离: 12.0313
  内存使用: 0.1555
  能量使用: 0.3860
  推理时间: 0.9696秒

批次 56:
  奖励值: 55.7289
  收益率: 0.6443
  距离: 12.9663
  内存使用: 0.1516
  能量使用: 0.4244
  推理时间: 1.0291秒

批次 57:
  奖励值: 50.5574
  收益率: 0.6386
  距离: 14.8567
  内存使用: 0.1612
  能量使用: 0.4672
  推理时间: 1.0612秒

批次 58:
  奖励值: 54.0235
  收益率: 0.6526
  距离: 11.9517
  内存使用: 0.1198
  能量使用: 0.4815
  推理时间: 1.0289秒

批次 59:
  奖励值: 49.7847
  收益率: 0.6112
  距离: 10.9743
  内存使用: 0.0616
  能量使用: 0.3947
  推理时间: 0.9641秒

批次 60:
  奖励值: 51.6925
  收益率: 0.6522
  距离: 16.0708
  内存使用: 0.1266
  能量使用: 0.4177
  推理时间: 1.0130秒

批次 61:
  奖励值: 49.8462
  收益率: 0.6185
  距离: 14.5416
  内存使用: 0.1303
  能量使用: 0.4357
  推理时间: 1.0008秒

批次 62:
  奖励值: 50.6339
  收益率: 0.6303
  距离: 14.7793
  内存使用: 0.1275
  能量使用: 0.4255
  推理时间: 0.9893秒

批次 63:
  奖励值: 45.3631
  收益率: 0.6021
  距离: 13.1364
  内存使用: 0.1086
  能量使用: 0.4288
  推理时间: 0.9342秒

批次 64:
  奖励值: 52.7147
  收益率: 0.6511
  距离: 13.7398
  内存使用: 0.1984
  能量使用: 0.4325
  推理时间: 1.0583秒

批次 65:
  奖励值: 52.8718
  收益率: 0.6468
  距离: 17.0339
  内存使用: 0.1805
  能量使用: 0.4419
  推理时间: 1.1178秒

批次 66:
  奖励值: 49.6346
  收益率: 0.6160
  距离: 14.3684
  内存使用: 0.1515
  能量使用: 0.4109
  推理时间: 0.9904秒

批次 67:
  奖励值: 50.5776
  收益率: 0.6306
  距离: 13.7155
  内存使用: 0.1250
  能量使用: 0.4043
  推理时间: 1.0137秒

批次 68:
  奖励值: 47.4990
  收益率: 0.6125
  距离: 16.0798
  内存使用: 0.1028
  能量使用: 0.4193
  推理时间: 0.9380秒

批次 69:
  奖励值: 50.4269
  收益率: 0.6421
  距离: 16.9477
  内存使用: 0.1256
  能量使用: 0.4729
  推理时间: 1.0302秒

批次 70:
  奖励值: 46.4013
  收益率: 0.5868
  距离: 14.8659
  内存使用: 0.1137
  能量使用: 0.4178
  推理时间: 0.9600秒

批次 71:
  奖励值: 48.1641
  收益率: 0.6096
  距离: 14.9856
  内存使用: 0.0735
  能量使用: 0.4127
  推理时间: 0.9187秒

批次 72:
  奖励值: 48.1989
  收益率: 0.6435
  距离: 16.0313
  内存使用: 0.1258
  能量使用: 0.4451
  推理时间: 1.0375秒

批次 73:
  奖励值: 51.4737
  收益率: 0.6413
  距离: 14.4781
  内存使用: 0.1229
  能量使用: 0.4516
  推理时间: 1.0397秒

批次 74:
  奖励值: 47.2350
  收益率: 0.5861
  距离: 12.6410
  内存使用: 0.0793
  能量使用: 0.4099
  推理时间: 0.9110秒

批次 75:
  奖励值: 49.0792
  收益率: 0.6021
  距离: 13.8477
  内存使用: 0.1550
  能量使用: 0.4388
  推理时间: 0.9956秒

批次 76:
  奖励值: 48.9717
  收益率: 0.6004
  距离: 11.5757
  内存使用: 0.0814
  能量使用: 0.3995
  推理时间: 0.9432秒

批次 77:
  奖励值: 47.8185
  收益率: 0.6130
  距离: 14.9950
  内存使用: 0.1083
  能量使用: 0.4597
  推理时间: 0.9896秒

批次 78:
  奖励值: 47.9591
  收益率: 0.6166
  距离: 13.5579
  内存使用: 0.0920
  能量使用: 0.4320
  推理时间: 0.9496秒

批次 79:
  奖励值: 48.0447
  收益率: 0.6087
  距离: 12.5462
  内存使用: 0.1428
  能量使用: 0.4216
  推理时间: 0.9518秒

批次 80:
  奖励值: 53.0229
  收益率: 0.6328
  距离: 15.3340
  内存使用: 0.1593
  能量使用: 0.4609
  推理时间: 1.0198秒

批次 81:
  奖励值: 45.3762
  收益率: 0.5979
  距离: 13.5054
  内存使用: 0.3810
  能量使用: 0.3612
  推理时间: 0.9254秒

批次 82:
  奖励值: 50.9646
  收益率: 0.6370
  距离: 16.5132
  内存使用: 0.1647
  能量使用: 0.3930
  推理时间: 1.0541秒

批次 83:
  奖励值: 50.2159
  收益率: 0.6021
  距离: 12.2333
  内存使用: 0.1173
  能量使用: 0.4176
  推理时间: 0.9409秒

批次 84:
  奖励值: 50.1523
  收益率: 0.6344
  距离: 13.3839
  内存使用: 0.0909
  能量使用: 0.4206
  推理时间: 0.9679秒

批次 85:
  奖励值: 49.8237
  收益率: 0.6292
  距离: 11.8140
  内存使用: 0.1014
  能量使用: 0.4372
  推理时间: 0.9769秒

批次 86:
  奖励值: 42.5235
  收益率: 0.5964
  距离: 14.6263
  内存使用: 0.1534
  能量使用: 0.3733
  推理时间: 0.8878秒

批次 87:
  奖励值: 53.1265
  收益率: 0.6556
  距离: 15.7558
  内存使用: 0.1559
  能量使用: 0.4353
  推理时间: 1.0326秒

批次 88:
  奖励值: 48.4927
  收益率: 0.5996
  距离: 14.4706
  内存使用: 0.1251
  能量使用: 0.4188
  推理时间: 0.9465秒

批次 89:
  奖励值: 47.7984
  收益率: 0.5859
  距离: 11.6140
  内存使用: 0.1098
  能量使用: 0.4795
  推理时间: 0.9067秒

批次 90:
  奖励值: 50.5021
  收益率: 0.6107
  距离: 14.8566
  内存使用: 0.1112
  能量使用: 0.4280
  推理时间: 0.9823秒

批次 91:
  奖励值: 44.2227
  收益率: 0.5575
  距离: 10.9570
  内存使用: 0.0447
  能量使用: 0.3530
  推理时间: 0.8236秒

批次 92:
  奖励值: 51.0064
  收益率: 0.6542
  距离: 16.6388
  内存使用: 0.1526
  能量使用: 0.3957
  推理时间: 1.0032秒

批次 93:
  奖励值: 45.6951
  收益率: 0.5918
  距离: 14.4627
  内存使用: 0.1163
  能量使用: 0.4307
  推理时间: 0.9237秒

批次 94:
  奖励值: 52.0131
  收益率: 0.6363
  距离: 12.6586
  内存使用: 0.1299
  能量使用: 0.3854
  推理时间: 0.9834秒

批次 95:
  奖励值: 53.3246
  收益率: 0.6547
  距离: 16.3459
  内存使用: 0.1476
  能量使用: 0.4573
  推理时间: 1.0843秒

批次 96:
  奖励值: 52.7451
  收益率: 0.6526
  距离: 14.1064
  内存使用: 0.1443
  能量使用: 0.4591
  推理时间: 1.0409秒

批次 97:
  奖励值: 54.4450
  收益率: 0.6524
  距离: 15.0802
  内存使用: 0.1818
  能量使用: 0.4481
  推理时间: 1.0550秒

批次 98:
  奖励值: 45.9004
  收益率: 0.5823
  距离: 11.8952
  内存使用: 0.0141
  能量使用: 0.2899
  推理时间: 0.8792秒

批次 99:
  奖励值: 54.1487
  收益率: 0.6412
  距离: 15.1443
  内存使用: 0.1247
  能量使用: 0.4336
  推理时间: 1.0221秒

批次 100:
  奖励值: 46.6098
  收益率: 0.6120
  距离: 14.4879
  内存使用: 0.0840
  能量使用: 0.4084
  推理时间: 0.9470秒


==================== 总结 ====================
平均收益率: 0.6244
平均能量使用: 0.4236
平均推理时间: 0.9901秒
