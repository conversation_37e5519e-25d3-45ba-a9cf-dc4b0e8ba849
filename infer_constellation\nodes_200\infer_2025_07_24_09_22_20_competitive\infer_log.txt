推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 42.8562
  收益率: 0.5626
  距离: 14.4197
  内存使用: 0.1231
  能量使用: 0.3447
  推理时间: 0.9897秒

批次 2:
  奖励值: 47.3177
  收益率: 0.5922
  距离: 14.6571
  内存使用: 0.1124
  能量使用: 0.3951
  推理时间: 0.9334秒

批次 3:
  奖励值: 41.4977
  收益率: 0.5248
  距离: 9.8216
  内存使用: 0.0610
  能量使用: 0.3221
  推理时间: 0.7880秒

批次 4:
  奖励值: 53.1013
  收益率: 0.6332
  距离: 13.0204
  内存使用: 0.1451
  能量使用: 0.4319
  推理时间: 1.0014秒

批次 5:
  奖励值: 46.5145
  收益率: 0.5935
  距离: 12.5233
  内存使用: 0.1064
  能量使用: 0.3413
  推理时间: 0.9317秒

批次 6:
  奖励值: 55.2292
  收益率: 0.6642
  距离: 17.0958
  内存使用: 0.1681
  能量使用: 0.4777
  推理时间: 1.0841秒

批次 7:
  奖励值: 54.9613
  收益率: 0.6229
  距离: 12.7874
  内存使用: 0.0813
  能量使用: 0.4087
  推理时间: 0.9863秒

批次 8:
  奖励值: 52.3410
  收益率: 0.6574
  距离: 17.8680
  内存使用: 0.1860
  能量使用: 0.4641
  推理时间: 1.1555秒

批次 9:
  奖励值: 50.5423
  收益率: 0.6200
  距离: 12.5797
  内存使用: 0.1342
  能量使用: 0.4015
  推理时间: 1.0028秒

批次 10:
  奖励值: 49.9815
  收益率: 0.5928
  距离: 14.5183
  内存使用: 0.1494
  能量使用: 0.3693
  推理时间: 0.9476秒

批次 11:
  奖励值: 44.9972
  收益率: 0.5601
  距离: 13.0217
  内存使用: 0.0472
  能量使用: 0.3366
  推理时间: 0.8806秒

批次 12:
  奖励值: 48.9242
  收益率: 0.6160
  距离: 14.1949
  内存使用: 0.1287
  能量使用: 0.4282
  推理时间: 0.9363秒

批次 13:
  奖励值: 48.3893
  收益率: 0.6177
  距离: 15.5033
  内存使用: 0.0969
  能量使用: 0.4212
  推理时间: 0.9901秒

批次 14:
  奖励值: 54.5690
  收益率: 0.6318
  距离: 13.5067
  内存使用: 0.1388
  能量使用: 0.4164
  推理时间: 1.0259秒

批次 15:
  奖励值: 51.3121
  收益率: 0.6446
  距离: 15.7833
  内存使用: 0.1710
  能量使用: 0.4214
  推理时间: 1.0515秒

批次 16:
  奖励值: 48.2515
  收益率: 0.5938
  距离: 14.4160
  内存使用: 0.0947
  能量使用: 0.3862
  推理时间: 0.9491秒

批次 17:
  奖励值: 53.1295
  收益率: 0.6275
  距离: 13.7487
  内存使用: 0.1726
  能量使用: 0.4197
  推理时间: 1.0294秒

批次 18:
  奖励值: 53.9092
  收益率: 0.6316
  距离: 12.9455
  内存使用: 0.1762
  能量使用: 0.4599
  推理时间: 1.0073秒

批次 19:
  奖励值: 50.1383
  收益率: 0.6272
  距离: 12.5675
  内存使用: 0.0976
  能量使用: 0.4583
  推理时间: 1.0039秒

批次 20:
  奖励值: 51.8156
  收益率: 0.6290
  距离: 12.7943
  内存使用: 0.1080
  能量使用: 0.4664
  推理时间: 1.0372秒

批次 21:
  奖励值: 56.2013
  收益率: 0.6653
  距离: 15.7757
  内存使用: 0.1896
  能量使用: 0.4320
  推理时间: 1.0456秒

批次 22:
  奖励值: 49.0944
  收益率: 0.6058
  距离: 13.5285
  内存使用: 0.1445
  能量使用: 0.3382
  推理时间: 0.9335秒

批次 23:
  奖励值: 47.6780
  收益率: 0.5985
  距离: 15.2823
  内存使用: 0.1442
  能量使用: 0.4175
  推理时间: 1.0006秒

批次 24:
  奖励值: 52.0689
  收益率: 0.6372
  距离: 15.0902
  内存使用: 0.1633
  能量使用: 0.4088
  推理时间: 1.0579秒

批次 25:
  奖励值: 46.6155
  收益率: 0.5843
  距离: 12.2384
  内存使用: 0.1233
  能量使用: 0.4072
  推理时间: 0.8919秒

批次 26:
  奖励值: 53.8540
  收益率: 0.6588
  距离: 14.5083
  内存使用: 0.1387
  能量使用: 0.4451
  推理时间: 1.1003秒

批次 27:
  奖励值: 48.5150
  收益率: 0.6061
  距离: 13.8145
  内存使用: 0.1353
  能量使用: 0.4204
  推理时间: 0.9527秒

批次 28:
  奖励值: 45.5269
  收益率: 0.5870
  距离: 13.0220
  内存使用: 0.0916
  能量使用: 0.4325
  推理时间: 0.9266秒

批次 29:
  奖励值: 52.3940
  收益率: 0.6018
  距离: 12.9177
  内存使用: 0.1067
  能量使用: 0.3876
  推理时间: 1.0445秒

批次 30:
  奖励值: 49.5355
  收益率: 0.6133
  距离: 10.8542
  内存使用: 0.1217
  能量使用: 0.3531
  推理时间: 0.9106秒

批次 31:
  奖励值: 48.3881
  收益率: 0.6002
  距离: 11.6918
  内存使用: 0.0939
  能量使用: 0.4380
  推理时间: 0.9463秒

批次 32:
  奖励值: 51.4254
  收益率: 0.6645
  距离: 16.5564
  内存使用: 0.1546
  能量使用: 0.4384
  推理时间: 1.0866秒

批次 33:
  奖励值: 53.4372
  收益率: 0.6557
  距离: 12.1836
  内存使用: 0.1201
  能量使用: 0.4219
  推理时间: 1.1042秒

批次 34:
  奖励值: 51.3616
  收益率: 0.6269
  距离: 11.3741
  内存使用: 0.1303
  能量使用: 0.3724
  推理时间: 0.9510秒

批次 35:
  奖励值: 47.8187
  收益率: 0.5969
  距离: 12.2425
  内存使用: 0.0717
  能量使用: 0.4483
  推理时间: 0.9087秒

批次 36:
  奖励值: 49.6221
  收益率: 0.6346
  距离: 12.4036
  内存使用: 0.0957
  能量使用: 0.4566
  推理时间: 1.0467秒

批次 37:
  奖励值: 50.0877
  收益率: 0.6304
  距离: 13.8915
  内存使用: 0.0728
  能量使用: 0.4603
  推理时间: 1.0327秒

批次 38:
  奖励值: 49.8865
  收益率: 0.6153
  距离: 12.2971
  内存使用: 0.1062
  能量使用: 0.4237
  推理时间: 0.9418秒

批次 39:
  奖励值: 48.2427
  收益率: 0.6214
  距离: 15.5493
  内存使用: 0.1314
  能量使用: 0.4339
  推理时间: 0.9978秒

批次 40:
  奖励值: 45.7408
  收益率: 0.5810
  距离: 12.0388
  内存使用: 0.0719
  能量使用: 0.4077
  推理时间: 0.8614秒

批次 41:
  奖励值: 49.3933
  收益率: 0.6300
  距离: 11.7627
  内存使用: 0.0427
  能量使用: 0.4081
  推理时间: 0.9290秒

批次 42:
  奖励值: 46.6993
  收益率: 0.5773
  距离: 12.5807
  内存使用: 0.4301
  能量使用: 0.3906
  推理时间: 0.9100秒

批次 43:
  奖励值: 50.1953
  收益率: 0.6219
  距离: 13.8956
  内存使用: 0.1007
  能量使用: 0.4132
  推理时间: 0.9098秒

批次 44:
  奖励值: 48.2405
  收益率: 0.6158
  距离: 15.2448
  内存使用: 0.1083
  能量使用: 0.3659
  推理时间: 0.9687秒

批次 45:
  奖励值: 50.2495
  收益率: 0.6232
  距离: 12.7779
  内存使用: 0.0996
  能量使用: 0.4236
  推理时间: 0.9360秒

批次 46:
  奖励值: 49.5818
  收益率: 0.6000
  距离: 12.5771
  内存使用: 0.0537
  能量使用: 0.4056
  推理时间: 0.9091秒

批次 47:
  奖励值: 44.6368
  收益率: 0.5805
  距离: 14.8455
  内存使用: 0.0748
  能量使用: 0.4060
  推理时间: 0.8573秒

批次 48:
  奖励值: 46.6937
  收益率: 0.6077
  距离: 13.8591
  内存使用: 0.0819
  能量使用: 0.4716
  推理时间: 0.8960秒

批次 49:
  奖励值: 43.8779
  收益率: 0.5624
  距离: 12.7708
  内存使用: 0.0613
  能量使用: 0.4122
  推理时间: 0.8414秒

批次 50:
  奖励值: 45.9205
  收益率: 0.5760
  距离: 13.0278
  内存使用: 0.1107
  能量使用: 0.4206
  推理时间: 0.8772秒

批次 51:
  奖励值: 52.1430
  收益率: 0.6398
  距离: 14.1372
  内存使用: 0.1410
  能量使用: 0.4358
  推理时间: 0.9621秒

批次 52:
  奖励值: 48.5617
  收益率: 0.6032
  距离: 15.7344
  内存使用: 0.1376
  能量使用: 0.4045
  推理时间: 0.9105秒

批次 53:
  奖励值: 60.1015
  收益率: 0.6706
  距离: 13.9094
  内存使用: 0.1933
  能量使用: 0.4943
  推理时间: 1.0644秒

批次 54:
  奖励值: 45.4819
  收益率: 0.5774
  距离: 13.7109
  内存使用: 0.1214
  能量使用: 0.4040
  推理时间: 0.9085秒

批次 55:
  奖励值: 46.4849
  收益率: 0.5821
  距离: 10.7574
  内存使用: 0.1210
  能量使用: 0.3518
  推理时间: 0.9261秒

批次 56:
  奖励值: 51.9072
  收益率: 0.6021
  距离: 12.4724
  内存使用: 0.1077
  能量使用: 0.4055
  推理时间: 0.9504秒

批次 57:
  奖励值: 46.2317
  收益率: 0.5772
  距离: 12.4370
  内存使用: 0.1079
  能量使用: 0.3917
  推理时间: 0.9247秒

批次 58:
  奖励值: 49.7791
  收益率: 0.6158
  距离: 13.7015
  内存使用: 0.0809
  能量使用: 0.4594
  推理时间: 0.9557秒

批次 59:
  奖励值: 48.5308
  收益率: 0.6013
  距离: 11.7179
  内存使用: 0.0504
  能量使用: 0.3700
  推理时间: 0.9390秒

批次 60:
  奖励值: 49.4231
  收益率: 0.6239
  距离: 15.4889
  内存使用: 0.0893
  能量使用: 0.3757
  推理时间: 0.9486秒

批次 61:
  奖励值: 47.0395
  收益率: 0.5916
  距离: 15.2142
  内存使用: 0.1064
  能量使用: 0.4003
  推理时间: 0.9364秒

批次 62:
  奖励值: 44.5187
  收益率: 0.5632
  距离: 14.6999
  内存使用: 0.0843
  能量使用: 0.3784
  推理时间: 0.8664秒

批次 63:
  奖励值: 45.2921
  收益率: 0.5952
  距离: 12.0969
  内存使用: 0.0973
  能量使用: 0.4216
  推理时间: 0.9211秒

批次 64:
  奖励值: 48.6983
  收益率: 0.6087
  距离: 14.0438
  内存使用: 0.1483
  能量使用: 0.4113
  推理时间: 1.0050秒

批次 65:
  奖励值: 48.6436
  收益率: 0.6101
  距离: 18.5767
  内存使用: 0.1381
  能量使用: 0.3979
  推理时间: 1.0069秒

批次 66:
  奖励值: 49.3351
  收益率: 0.6084
  距离: 13.6549
  内存使用: 0.1064
  能量使用: 0.3684
  推理时间: 0.9505秒

批次 67:
  奖励值: 47.5999
  收益率: 0.5902
  距离: 12.3259
  内存使用: 0.1014
  能量使用: 0.3729
  推理时间: 0.9303秒

批次 68:
  奖励值: 49.5594
  收益率: 0.6235
  距离: 13.9192
  内存使用: 0.1236
  能量使用: 0.4383
  推理时间: 0.9664秒

批次 69:
  奖励值: 48.7056
  收益率: 0.6063
  距离: 13.8594
  内存使用: 0.0945
  能量使用: 0.4351
  推理时间: 0.9702秒

批次 70:
  奖励值: 44.5307
  收益率: 0.5618
  距离: 14.0629
  内存使用: 0.0939
  能量使用: 0.3818
  推理时间: 0.9164秒

批次 71:
  奖励值: 45.6589
  收益率: 0.5703
  距离: 12.8567
  内存使用: 0.0491
  能量使用: 0.3764
  推理时间: 0.8704秒

批次 72:
  奖励值: 47.3886
  收益率: 0.6264
  距离: 14.6644
  内存使用: 0.1364
  能量使用: 0.4221
  推理时间: 0.9806秒

批次 73:
  奖励值: 52.8347
  收益率: 0.6587
  距离: 14.9083
  内存使用: 0.1450
  能量使用: 0.4773
  推理时间: 1.0565秒

批次 74:
  奖励值: 50.8054
  收益率: 0.6340
  距离: 14.1667
  内存使用: 0.1449
  能量使用: 0.4776
  推理时间: 1.0271秒

批次 75:
  奖励值: 53.2478
  收益率: 0.6545
  距离: 15.2532
  内存使用: 0.1799
  能量使用: 0.4712
  推理时间: 1.0668秒

批次 76:
  奖励值: 44.2893
  收益率: 0.5448
  距离: 10.8626
  内存使用: 0.0159
  能量使用: 0.3430
  推理时间: 0.8081秒

批次 77:
  奖励值: 46.4563
  收益率: 0.5910
  距离: 13.7871
  内存使用: 0.0764
  能量使用: 0.4238
  推理时间: 0.9347秒

批次 78:
  奖励值: 50.0309
  收益率: 0.6379
  距离: 13.2128
  内存使用: 0.0994
  能量使用: 0.4260
  推理时间: 0.9880秒

批次 79:
  奖励值: 50.5085
  收益率: 0.6321
  距离: 11.7954
  内存使用: 0.1460
  能量使用: 0.4417
  推理时间: 1.0155秒

批次 80:
  奖励值: 50.5866
  收益率: 0.6006
  距离: 14.0782
  内存使用: 0.1185
  能量使用: 0.4301
  推理时间: 1.0478秒

批次 81:
  奖励值: 44.2277
  收益率: 0.5854
  距离: 13.6236
  内存使用: 0.3716
  能量使用: 0.3495
  推理时间: 2.2691秒

批次 82:
  奖励值: 51.9506
  收益率: 0.6445
  距离: 15.9269
  内存使用: 0.1632
  能量使用: 0.4088
  推理时间: 1.1235秒

批次 83:
  奖励值: 51.5515
  收益率: 0.6234
  距离: 13.5289
  内存使用: 0.1148
  能量使用: 0.4534
  推理时间: 1.1042秒

批次 84:
  奖励值: 48.3098
  收益率: 0.6067
  距离: 12.1168
  内存使用: 0.0651
  能量使用: 0.3985
  推理时间: 0.9531秒

批次 85:
  奖励值: 47.2977
  收益率: 0.6079
  距离: 13.0841
  内存使用: 0.0891
  能量使用: 0.4315
  推理时间: 0.9333秒

批次 86:
  奖励值: 41.1832
  收益率: 0.5619
  距离: 11.5864
  内存使用: 0.1131
  能量使用: 0.3342
  推理时间: 0.9482秒

批次 87:
  奖励值: 50.5955
  收益率: 0.6118
  距离: 12.6652
  内存使用: 0.1286
  能量使用: 0.4150
  推理时间: 0.9540秒

批次 88:
  奖励值: 48.0340
  收益率: 0.5921
  距离: 13.9934
  内存使用: 0.1132
  能量使用: 0.4253
  推理时间: 0.9571秒

批次 89:
  奖励值: 48.7066
  收益率: 0.6022
  距离: 12.4031
  内存使用: 0.4527
  能量使用: 0.5309
  推理时间: 1.0156秒

批次 90:
  奖励值: 50.8919
  收益率: 0.6285
  距离: 17.4208
  内存使用: 0.1509
  能量使用: 0.4445
  推理时间: 0.9954秒

批次 91:
  奖励值: 46.7171
  收益率: 0.5899
  距离: 11.7120
  内存使用: 0.0719
  能量使用: 0.3818
  推理时间: 0.9054秒

批次 92:
  奖励值: 47.0980
  收益率: 0.5862
  距离: 12.1790
  内存使用: 0.0878
  能量使用: 0.3372
  推理时间: 0.8772秒

批次 93:
  奖励值: 47.9997
  收益率: 0.6207
  距离: 15.0067
  内存使用: 0.1542
  能量使用: 0.4397
  推理时间: 1.0299秒

批次 94:
  奖励值: 48.9865
  收益率: 0.6069
  距离: 13.3549
  内存使用: 0.1160
  能量使用: 0.3561
  推理时间: 0.9145秒

批次 95:
  奖励值: 50.2415
  收益率: 0.5996
  距离: 12.1763
  内存使用: 0.1177
  能量使用: 0.4231
  推理时间: 0.9744秒

批次 96:
  奖励值: 52.3720
  收益率: 0.6460
  距离: 13.6401
  内存使用: 0.1464
  能量使用: 0.4695
  推理时间: 1.0241秒

批次 97:
  奖励值: 51.1315
  收益率: 0.6021
  距离: 12.1732
  内存使用: 0.1350
  能量使用: 0.4289
  推理时间: 0.9713秒

批次 98:
  奖励值: 44.6383
  收益率: 0.5733
  距离: 12.8299
  内存使用: -0.0039
  能量使用: 0.2974
  推理时间: 0.8663秒

批次 99:
  奖励值: 53.9091
  收益率: 0.6236
  距离: 12.2583
  内存使用: 0.1072
  能量使用: 0.4163
  推理时间: 0.9807秒

批次 100:
  奖励值: 45.0508
  收益率: 0.5894
  距离: 13.6647
  内存使用: 0.0724
  能量使用: 0.3615
  推理时间: 0.9068秒


==================== 总结 ====================
平均收益率: 0.6093
平均能量使用: 0.4112
平均推理时间: 0.9797秒
