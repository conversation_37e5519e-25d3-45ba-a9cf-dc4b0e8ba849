推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 115.8053
  收益率: 0.3838
  距离: 28.1150
  内存使用: 0.5892
  能量使用: 0.8853
  推理时间: 2.2525秒

批次 2:
  奖励值: 116.1025
  收益率: 0.3862
  距离: 28.8031
  内存使用: 0.6429
  能量使用: 0.9489
  推理时间: 2.2771秒

批次 3:
  奖励值: 114.3242
  收益率: 0.3877
  距离: 30.5475
  内存使用: 0.6549
  能量使用: 0.9491
  推理时间: 2.2372秒

批次 4:
  奖励值: 100.9416
  收益率: 0.3468
  距离: 28.0292
  内存使用: 0.5353
  能量使用: 0.7524
  推理时间: 1.9558秒

批次 5:
  奖励值: 110.9725
  收益率: 0.3780
  距离: 26.3416
  内存使用: 0.5665
  能量使用: 0.9470
  推理时间: 2.1562秒

批次 6:
  奖励值: 110.4042
  收益率: 0.3821
  距离: 30.5464
  内存使用: 0.9036
  能量使用: 0.9416
  推理时间: 2.1077秒

批次 7:
  奖励值: 108.0552
  收益率: 0.3672
  距离: 30.9561
  内存使用: 0.6140
  能量使用: 0.9194
  推理时间: 2.0917秒

批次 8:
  奖励值: 112.6140
  收益率: 0.3810
  距离: 32.4536
  内存使用: 0.6029
  能量使用: 0.9156
  推理时间: 2.1176秒

批次 9:
  奖励值: 124.7391
  收益率: 0.4307
  距离: 27.4772
  内存使用: 0.6152
  能量使用: 1.0292
  推理时间: 2.2571秒

批次 10:
  奖励值: 111.2354
  收益率: 0.3689
  距离: 32.4936
  内存使用: 0.6477
  能量使用: 0.9879
  推理时间: 2.1347秒

批次 11:
  奖励值: 105.5441
  收益率: 0.3697
  距离: 29.6262
  内存使用: 0.6650
  能量使用: 0.8205
  推理时间: 2.0782秒

批次 12:
  奖励值: 115.7069
  收益率: 0.3858
  距离: 31.0943
  内存使用: 0.6908
  能量使用: 1.0011
  推理时间: 1.9920秒

批次 13:
  奖励值: 112.7741
  收益率: 0.3704
  距离: 29.4696
  内存使用: 0.6446
  能量使用: 0.9376
  推理时间: 2.0160秒

批次 14:
  奖励值: 113.3961
  收益率: 0.3919
  距离: 30.9526
  内存使用: 0.9112
  能量使用: 0.9440
  推理时间: 2.0042秒

批次 15:
  奖励值: 118.0161
  收益率: 0.3919
  距离: 28.0508
  内存使用: 0.5854
  能量使用: 0.9316
  推理时间: 2.0231秒

批次 16:
  奖励值: 119.7575
  收益率: 0.3922
  距离: 29.0764
  内存使用: 0.6296
  能量使用: 0.9789
  推理时间: 2.1560秒

批次 17:
  奖励值: 119.1701
  收益率: 0.3879
  距离: 33.0147
  内存使用: 0.6904
  能量使用: 0.9547
  推理时间: 2.1993秒

批次 18:
  奖励值: 112.9515
  收益率: 0.3818
  距离: 29.3234
  内存使用: 0.6094
  能量使用: 0.9642
  推理时间: 2.0789秒

批次 19:
  奖励值: 101.6146
  收益率: 0.3344
  距离: 28.6524
  内存使用: 0.5567
  能量使用: 0.7616
  推理时间: 1.8062秒

批次 20:
  奖励值: 112.8991
  收益率: 0.3817
  距离: 32.3798
  内存使用: 0.6372
  能量使用: 0.9848
  推理时间: 2.0085秒

批次 21:
  奖励值: 114.7759
  收益率: 0.3741
  距离: 30.6180
  内存使用: 0.6290
  能量使用: 1.0011
  推理时间: 2.0345秒

批次 22:
  奖励值: 106.4319
  收益率: 0.3587
  距离: 28.7987
  内存使用: 0.8661
  能量使用: 0.8714
  推理时间: 1.9645秒

批次 23:
  奖励值: 99.2780
  收益率: 0.3348
  距离: 28.2927
  内存使用: 0.5872
  能量使用: 0.7641
  推理时间: 1.7825秒

批次 24:
  奖励值: 111.9100
  收益率: 0.3819
  距离: 32.2549
  内存使用: 0.6259
  能量使用: 0.8991
  推理时间: 2.0591秒

批次 25:
  奖励值: 106.1053
  收益率: 0.3463
  距离: 28.5139
  内存使用: 0.6448
  能量使用: 0.8307
  推理时间: 1.9304秒

批次 26:
  奖励值: 100.6120
  收益率: 0.3445
  距离: 28.5031
  内存使用: 0.5713
  能量使用: 0.9016
  推理时间: 2.0262秒

批次 27:
  奖励值: 119.3730
  收益率: 0.3866
  距离: 30.6007
  内存使用: 0.6482
  能量使用: 1.0051
  推理时间: 2.1638秒

批次 28:
  奖励值: 118.5321
  收益率: 0.3891
  距离: 29.6154
  内存使用: 0.6237
  能量使用: 0.8967
  推理时间: 2.1276秒

批次 29:
  奖励值: 112.9690
  收益率: 0.3820
  距离: 31.5950
  内存使用: 0.5929
  能量使用: 0.8741
  推理时间: 2.0444秒

批次 30:
  奖励值: 123.9165
  收益率: 0.3977
  距离: 30.6728
  内存使用: 0.6940
  能量使用: 1.0002
  推理时间: 2.2178秒

批次 31:
  奖励值: 115.1380
  收益率: 0.3756
  距离: 28.1003
  内存使用: 0.6503
  能量使用: 0.9733
  推理时间: 2.0582秒

批次 32:
  奖励值: 110.6538
  收益率: 0.3638
  距离: 31.2492
  内存使用: 0.5588
  能量使用: 0.8844
  推理时间: 1.9846秒

批次 33:
  奖励值: 128.4476
  收益率: 0.4270
  距离: 32.0107
  内存使用: 0.6795
  能量使用: 1.0146
  推理时间: 2.3640秒

批次 34:
  奖励值: 110.8431
  收益率: 0.3779
  距离: 27.2411
  内存使用: 0.5782
  能量使用: 0.8946
  推理时间: 1.9782秒

批次 35:
  奖励值: 115.3223
  收益率: 0.3852
  距离: 30.0371
  内存使用: 0.6232
  能量使用: 0.8950
  推理时间: 2.0260秒

批次 36:
  奖励值: 103.4132
  收益率: 0.3502
  距离: 27.9824
  内存使用: 0.5716
  能量使用: 0.8198
  推理时间: 1.8390秒

批次 37:
  奖励值: 113.7209
  收益率: 0.3899
  距离: 32.0652
  内存使用: 0.7028
  能量使用: 0.9902
  推理时间: 2.0934秒

批次 38:
  奖励值: 110.8640
  收益率: 0.3651
  距离: 27.1610
  内存使用: 0.5415
  能量使用: 0.9695
  推理时间: 1.9898秒

批次 39:
  奖励值: 115.8158
  收益率: 0.3844
  距离: 28.7912
  内存使用: 0.6408
  能量使用: 0.9364
  推理时间: 2.0766秒

批次 40:
  奖励值: 119.3551
  收益率: 0.3987
  距离: 29.3930
  内存使用: 0.6427
  能量使用: 0.8542
  推理时间: 2.1124秒

批次 41:
  奖励值: 118.1920
  收益率: 0.3990
  距离: 29.0240
  内存使用: 0.6836
  能量使用: 0.9085
  推理时间: 2.0949秒

批次 42:
  奖励值: 116.5853
  收益率: 0.3717
  距离: 30.2720
  内存使用: 0.6758
  能量使用: 0.8816
  推理时间: 2.0650秒

批次 43:
  奖励值: 127.6388
  收益率: 0.4265
  距离: 34.4329
  内存使用: 0.7181
  能量使用: 0.9713
  推理时间: 2.2530秒

批次 44:
  奖励值: 115.9546
  收益率: 0.3802
  距离: 29.0280
  内存使用: 0.5548
  能量使用: 0.9080
  推理时间: 2.0368秒

批次 45:
  奖励值: 87.5021
  收益率: 0.2952
  距离: 21.6995
  内存使用: 0.3614
  能量使用: 0.7347
  推理时间: 1.5420秒

批次 46:
  奖励值: 106.3352
  收益率: 0.3532
  距离: 29.9429
  内存使用: 0.6146
  能量使用: 0.7720
  推理时间: 1.9356秒

批次 47:
  奖励值: 122.9502
  收益率: 0.4023
  距离: 28.4263
  内存使用: 0.6183
  能量使用: 0.8550
  推理时间: 2.1999秒

批次 48:
  奖励值: 110.9064
  收益率: 0.3726
  距离: 27.6734
  内存使用: 0.5987
  能量使用: 0.9151
  推理时间: 2.6629秒

批次 49:
  奖励值: 122.4826
  收益率: 0.4058
  距离: 30.4388
  内存使用: 0.6774
  能量使用: 0.9185
  推理时间: 2.1582秒

批次 50:
  奖励值: 113.3192
  收益率: 0.3664
  距离: 28.4401
  内存使用: 0.5930
  能量使用: 0.9286
  推理时间: 2.0164秒

批次 51:
  奖励值: 116.5614
  收益率: 0.4082
  距离: 30.6885
  内存使用: 0.6141
  能量使用: 0.9745
  推理时间: 2.0687秒

批次 52:
  奖励值: 119.2281
  收益率: 0.3971
  距离: 33.0496
  内存使用: 0.6223
  能量使用: 1.0719
  推理时间: 2.1156秒

批次 53:
  奖励值: 105.3874
  收益率: 0.3576
  距离: 28.3028
  内存使用: 0.4947
  能量使用: 0.8277
  推理时间: 1.8791秒

批次 54:
  奖励值: 118.3392
  收益率: 0.4008
  距离: 30.8453
  内存使用: 0.6814
  能量使用: 0.9948
  推理时间: 2.0874秒

批次 55:
  奖励值: 113.8020
  收益率: 0.3942
  距离: 31.7364
  内存使用: 0.9113
  能量使用: 0.9483
  推理时间: 2.0533秒

批次 56:
  奖励值: 116.2359
  收益率: 0.3928
  距离: 29.3786
  内存使用: 0.6486
  能量使用: 0.9010
  推理时间: 2.0572秒

批次 57:
  奖励值: 111.0541
  收益率: 0.3655
  距离: 26.8987
  内存使用: 0.5656
  能量使用: 0.8274
  推理时间: 1.9669秒

批次 58:
  奖励值: 117.7653
  收益率: 0.3842
  距离: 29.8913
  内存使用: 0.6604
  能量使用: 0.9177
  推理时间: 2.0922秒

批次 59:
  奖励值: 112.9113
  收益率: 0.3750
  距离: 30.5535
  内存使用: 0.6262
  能量使用: 0.9976
  推理时间: 2.0267秒

批次 60:
  奖励值: 85.5947
  收益率: 0.3029
  距离: 25.5211
  内存使用: 0.4082
  能量使用: 0.6812
  推理时间: 1.6085秒

批次 61:
  奖励值: 117.7852
  收益率: 0.3934
  距离: 30.4803
  内存使用: 0.5721
  能量使用: 0.9772
  推理时间: 2.0589秒

批次 62:
  奖励值: 116.8000
  收益率: 0.3955
  距离: 31.3945
  内存使用: 0.6452
  能量使用: 0.9603
  推理时间: 2.0754秒

批次 63:
  奖励值: 103.9761
  收益率: 0.3559
  距离: 27.9919
  内存使用: 0.5572
  能量使用: 0.8988
  推理时间: 1.9121秒

批次 64:
  奖励值: 115.1711
  收益率: 0.3810
  距离: 28.2242
  内存使用: 0.5918
  能量使用: 0.8419
  推理时间: 2.0421秒

批次 65:
  奖励值: 116.5319
  收益率: 0.4022
  距离: 29.7804
  内存使用: 0.5792
  能量使用: 0.9766
  推理时间: 2.0939秒

批次 66:
  奖励值: 117.5978
  收益率: 0.4034
  距离: 33.4693
  内存使用: 0.6310
  能量使用: 0.9041
  推理时间: 2.1286秒

批次 67:
  奖励值: 124.9547
  收益率: 0.4244
  距离: 31.8494
  内存使用: 0.6070
  能量使用: 1.0341
  推理时间: 2.2970秒

批次 68:
  奖励值: 104.1146
  收益率: 0.3466
  距离: 28.4294
  内存使用: 0.5211
  能量使用: 0.8203
  推理时间: 1.8515秒

批次 69:
  奖励值: 121.7945
  收益率: 0.3972
  距离: 29.5106
  内存使用: 0.6667
  能量使用: 1.0337
  推理时间: 2.1157秒

批次 70:
  奖励值: 108.0491
  收益率: 0.3659
  距离: 29.0511
  内存使用: 0.9012
  能量使用: 0.9495
  推理时间: 2.2003秒

批次 71:
  奖励值: 113.8384
  收益率: 0.3851
  距离: 30.0081
  内存使用: 0.5890
  能量使用: 0.9260
  推理时间: 2.2562秒

批次 72:
  奖励值: 116.4299
  收益率: 0.3826
  距离: 29.8075
  内存使用: 0.6386
  能量使用: 0.8941
  推理时间: 2.1168秒

批次 73:
  奖励值: 121.5349
  收益率: 0.4055
  距离: 31.9352
  内存使用: 0.6723
  能量使用: 0.9226
  推理时间: 2.2048秒

批次 74:
  奖励值: 113.1773
  收益率: 0.3862
  距离: 31.1375
  内存使用: 0.6009
  能量使用: 0.9070
  推理时间: 2.1062秒

批次 75:
  奖励值: 113.0457
  收益率: 0.3866
  距离: 30.6945
  内存使用: 0.6860
  能量使用: 0.9281
  推理时间: 2.0608秒

批次 76:
  奖励值: 117.8352
  收益率: 0.3991
  距离: 32.5039
  内存使用: 0.7017
  能量使用: 0.9240
  推理时间: 2.2031秒

批次 77:
  奖励值: 121.8248
  收益率: 0.4012
  距离: 33.5487
  内存使用: 0.6325
  能量使用: 0.9692
  推理时间: 2.2426秒

批次 78:
  奖励值: 125.6286
  收益率: 0.4117
  距离: 33.3493
  内存使用: 0.7378
  能量使用: 1.0557
  推理时间: 2.2682秒

批次 79:
  奖励值: 116.2992
  收益率: 0.3856
  距离: 33.3751
  内存使用: 0.6379
  能量使用: 0.9887
  推理时间: 2.1569秒

批次 80:
  奖励值: 100.8153
  收益率: 0.3401
  距离: 25.7513
  内存使用: 0.5536
  能量使用: 0.8644
  推理时间: 1.9431秒

批次 81:
  奖励值: 123.5817
  收益率: 0.4283
  距离: 32.3762
  内存使用: 0.6801
  能量使用: 0.9802
  推理时间: 2.2556秒

批次 82:
  奖励值: 112.5507
  收益率: 0.3804
  距离: 28.7073
  内存使用: 0.6979
  能量使用: 0.8934
  推理时间: 2.1137秒

批次 83:
  奖励值: 115.4541
  收益率: 0.3963
  距离: 29.3139
  内存使用: 0.6372
  能量使用: 0.9407
  推理时间: 2.1872秒

批次 84:
  奖励值: 100.9325
  收益率: 0.3374
  距离: 29.6584
  内存使用: 0.6058
  能量使用: 0.8708
  推理时间: 1.8588秒

批次 85:
  奖励值: 111.7749
  收益率: 0.3785
  距离: 28.9999
  内存使用: 0.6182
  能量使用: 0.8321
  推理时间: 2.0221秒

批次 86:
  奖励值: 121.2306
  收益率: 0.4075
  距离: 32.8767
  内存使用: 0.6549
  能量使用: 1.0073
  推理时间: 2.2077秒

批次 87:
  奖励值: 112.5124
  收益率: 0.3782
  距离: 31.0130
  内存使用: 0.6320
  能量使用: 0.9302
  推理时间: 2.0782秒

批次 88:
  奖励值: 124.1103
  收益率: 0.4124
  距离: 31.7436
  内存使用: 0.6455
  能量使用: 0.9903
  推理时间: 2.2035秒

批次 89:
  奖励值: 113.1033
  收益率: 0.3885
  距离: 32.4280
  内存使用: 0.6319
  能量使用: 0.9334
  推理时间: 2.0339秒

批次 90:
  奖励值: 105.6903
  收益率: 0.3617
  距离: 31.7291
  内存使用: 0.5806
  能量使用: 0.9097
  推理时间: 1.9037秒

批次 91:
  奖励值: 111.7868
  收益率: 0.3678
  距离: 29.5361
  内存使用: 0.6470
  能量使用: 0.8433
  推理时间: 1.9577秒

批次 92:
  奖励值: 113.9955
  收益率: 0.3681
  距离: 30.4978
  内存使用: 0.6189
  能量使用: 0.8922
  推理时间: 1.9758秒

批次 93:
  奖励值: 121.9919
  收益率: 0.3935
  距离: 31.4407
  内存使用: 0.6342
  能量使用: 0.9413
  推理时间: 2.1907秒

批次 94:
  奖励值: 110.6183
  收益率: 0.3861
  距离: 27.7926
  内存使用: 0.6212
  能量使用: 0.9496
  推理时间: 2.0013秒

批次 95:
  奖励值: 109.8774
  收益率: 0.3725
  距离: 27.5294
  内存使用: 0.6108
  能量使用: 0.9050
  推理时间: 1.9469秒

批次 96:
  奖励值: 111.5690
  收益率: 0.3781
  距离: 29.0698
  内存使用: 0.6262
  能量使用: 0.9660
  推理时间: 1.9780秒

批次 97:
  奖励值: 109.9185
  收益率: 0.3756
  距离: 28.7315
  内存使用: 0.5953
  能量使用: 0.8361
  推理时间: 1.9769秒

批次 98:
  奖励值: 110.6460
  收益率: 0.3767
  距离: 32.7494
  内存使用: 0.6205
  能量使用: 0.9382
  推理时间: 2.0121秒

批次 99:
  奖励值: 104.0097
  收益率: 0.3434
  距离: 29.9266
  内存使用: 0.6080
  能量使用: 0.8460
  推理时间: 1.8604秒

批次 100:
  奖励值: 121.8704
  收益率: 0.4006
  距离: 34.1574
  内存使用: 0.7368
  能量使用: 0.9650
  推理时间: 2.3073秒


==================== 总结 ====================
平均收益率: 0.3805
平均能量使用: 0.9191
平均推理时间: 2.0715秒
