推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 120.1136
  收益率: 0.3976
  距离: 28.8654
  内存使用: 0.6256
  能量使用: 0.8817
  推理时间: 2.1835秒

批次 2:
  奖励值: 112.7118
  收益率: 0.3762
  距离: 28.8700
  内存使用: 0.6174
  能量使用: 0.8890
  推理时间: 2.0764秒

批次 3:
  奖励值: 108.8315
  收益率: 0.3681
  距离: 28.4815
  内存使用: 0.5687
  能量使用: 0.8866
  推理时间: 1.9890秒

批次 4:
  奖励值: 122.8432
  收益率: 0.4191
  距离: 32.1705
  内存使用: 0.6313
  能量使用: 0.9118
  推理时间: 2.2667秒

批次 5:
  奖励值: 118.8568
  收益率: 0.4086
  距离: 30.6932
  内存使用: 0.6296
  能量使用: 0.9636
  推理时间: 2.2343秒

批次 6:
  奖励值: 111.7504
  收益率: 0.3855
  距离: 30.3608
  内存使用: 0.6175
  能量使用: 0.9209
  推理时间: 2.0458秒

批次 7:
  奖励值: 103.5803
  收益率: 0.3474
  距离: 26.7069
  内存使用: 0.5670
  能量使用: 0.7656
  推理时间: 1.8910秒

批次 8:
  奖励值: 110.5367
  收益率: 0.3718
  距离: 30.4447
  内存使用: 0.6191
  能量使用: 0.8628
  推理时间: 2.0410秒

批次 9:
  奖励值: 114.9184
  收益率: 0.4011
  距离: 28.0877
  内存使用: 0.5260
  能量使用: 0.9494
  推理时间: 2.0984秒

批次 10:
  奖励值: 115.2754
  收益率: 0.3787
  距离: 31.2933
  内存使用: 0.6344
  能量使用: 0.9215
  推理时间: 2.1282秒

批次 11:
  奖励值: 113.6725
  收益率: 0.3901
  距离: 26.8144
  内存使用: 0.5948
  能量使用: 0.8457
  推理时间: 2.0980秒

批次 12:
  奖励值: 120.4997
  收益率: 0.3989
  距离: 30.5224
  内存使用: 0.7010
  能量使用: 0.9773
  推理时间: 2.1875秒

批次 13:
  奖励值: 124.9161
  收益率: 0.4076
  距离: 30.9368
  内存使用: 0.6793
  能量使用: 0.9518
  推理时间: 2.2577秒

批次 14:
  奖励值: 110.9263
  收益率: 0.3829
  距离: 30.2498
  内存使用: 0.5820
  能量使用: 0.9156
  推理时间: 2.0572秒

批次 15:
  奖励值: 113.5954
  收益率: 0.3806
  距离: 29.3468
  内存使用: 0.5687
  能量使用: 0.8937
  推理时间: 2.0593秒

批次 16:
  奖励值: 121.5925
  收益率: 0.4032
  距离: 32.9313
  内存使用: 0.6791
  能量使用: 1.0046
  推理时间: 2.2871秒

批次 17:
  奖励值: 116.5849
  收益率: 0.3731
  距离: 27.9034
  内存使用: 0.6399
  能量使用: 0.8867
  推理时间: 2.1608秒

批次 18:
  奖励值: 118.2047
  收益率: 0.3982
  距离: 29.8353
  内存使用: 0.6126
  能量使用: 0.9427
  推理时间: 2.1266秒

批次 19:
  奖励值: 113.8564
  收益率: 0.3745
  距离: 32.0283
  内存使用: 0.6338
  能量使用: 0.8252
  推理时间: 2.1171秒

批次 20:
  奖励值: 105.8949
  收益率: 0.3532
  距离: 27.1801
  内存使用: 0.5359
  能量使用: 0.8942
  推理时间: 1.9185秒

批次 21:
  奖励值: 114.3700
  收益率: 0.3712
  距离: 29.5589
  内存使用: 0.5632
  能量使用: 0.9379
  推理时间: 2.0565秒

批次 22:
  奖励值: 114.3575
  收益率: 0.3837
  距离: 30.2054
  内存使用: 0.5794
  能量使用: 0.9001
  推理时间: 2.0731秒

批次 23:
  奖励值: 108.9789
  收益率: 0.3624
  距离: 27.5481
  内存使用: 0.6171
  能量使用: 0.8769
  推理时间: 1.9949秒

批次 24:
  奖励值: 109.3476
  收益率: 0.3673
  距离: 27.6823
  内存使用: 0.5726
  能量使用: 0.8499
  推理时间: 2.0207秒

批次 25:
  奖励值: 115.2689
  收益率: 0.3766
  距离: 31.2837
  内存使用: 0.6942
  能量使用: 0.8843
  推理时间: 2.1454秒

批次 26:
  奖励值: 108.9789
  收益率: 0.3659
  距离: 26.0757
  内存使用: 0.6349
  能量使用: 0.9317
  推理时间: 2.0166秒

批次 27:
  奖励值: 117.5036
  收益率: 0.3815
  距离: 30.7431
  内存使用: 0.6204
  能量使用: 1.0292
  推理时间: 2.1985秒

批次 28:
  奖励值: 119.4548
  收益率: 0.3894
  距离: 27.9288
  内存使用: 0.6478
  能量使用: 0.9137
  推理时间: 2.1787秒

批次 29:
  奖励值: 99.7651
  收益率: 0.3348
  距离: 26.2428
  内存使用: 0.4741
  能量使用: 0.7528
  推理时间: 1.8342秒

批次 30:
  奖励值: 113.4724
  收益率: 0.3676
  距离: 30.5252
  内存使用: 0.5953
  能量使用: 0.9346
  推理时间: 2.1150秒

批次 31:
  奖励值: 109.7895
  收益率: 0.3594
  距离: 27.7879
  内存使用: 0.5604
  能量使用: 0.8727
  推理时间: 1.9891秒

批次 32:
  奖励值: 120.5916
  收益率: 0.3930
  距离: 31.6180
  内存使用: 0.6220
  能量使用: 0.9767
  推理时间: 2.2494秒

批次 33:
  奖励值: 115.7204
  收益率: 0.3882
  距离: 31.2648
  内存使用: 0.5644
  能量使用: 0.9299
  推理时间: 2.1535秒

批次 34:
  奖励值: 103.1945
  收益率: 0.3559
  距离: 28.0277
  内存使用: 0.5446
  能量使用: 0.8387
  推理时间: 1.9471秒

批次 35:
  奖励值: 118.0652
  收益率: 0.3973
  距离: 32.6838
  内存使用: 0.6424
  能量使用: 0.9433
  推理时间: 2.2397秒

批次 36:
  奖励值: 111.1908
  收益率: 0.3775
  距离: 30.8432
  内存使用: 0.5219
  能量使用: 0.8534
  推理时间: 2.0325秒

批次 37:
  奖励值: 118.9076
  收益率: 0.4015
  距离: 29.5119
  内存使用: 0.6793
  能量使用: 0.9935
  推理时间: 2.2941秒

批次 38:
  奖励值: 109.8708
  收益率: 0.3640
  距离: 28.3513
  内存使用: 0.5631
  能量使用: 0.9439
  推理时间: 2.1105秒

批次 39:
  奖励值: 101.0048
  收益率: 0.3336
  距离: 24.1271
  内存使用: 0.5076
  能量使用: 0.7557
  推理时间: 1.8673秒

批次 40:
  奖励值: 123.2027
  收益率: 0.4108
  距离: 29.8401
  内存使用: 0.6176
  能量使用: 0.9368
  推理时间: 2.3199秒

批次 41:
  奖励值: 119.1966
  收益率: 0.4014
  距离: 28.5613
  内存使用: 0.6873
  能量使用: 0.9581
  推理时间: 2.1485秒

批次 42:
  奖励值: 113.9957
  收益率: 0.3588
  距离: 26.3281
  内存使用: 0.6407
  能量使用: 0.8398
  推理时间: 2.0147秒

批次 43:
  奖励值: 113.6001
  收益率: 0.3779
  距离: 29.5467
  内存使用: 0.6251
  能量使用: 0.8280
  推理时间: 2.0588秒

批次 44:
  奖励值: 123.5683
  收益率: 0.4083
  距离: 33.0353
  内存使用: 0.6245
  能量使用: 1.0036
  推理时间: 2.2874秒

批次 45:
  奖励值: 120.6409
  收益率: 0.4051
  距离: 28.5385
  内存使用: 0.5909
  能量使用: 0.9888
  推理时间: 2.3042秒

批次 46:
  奖励值: 108.6217
  收益率: 0.3567
  距离: 27.7537
  内存使用: 0.6123
  能量使用: 0.7906
  推理时间: 1.9876秒

批次 47:
  奖励值: 123.4679
  收益率: 0.4011
  距离: 26.6033
  内存使用: 0.6140
  能量使用: 0.8468
  推理时间: 2.2005秒

批次 48:
  奖励值: 117.0352
  收益率: 0.3955
  距离: 30.7750
  内存使用: 0.6372
  能量使用: 0.9174
  推理时间: 2.2159秒

批次 49:
  奖励值: 116.4696
  收益率: 0.3859
  距离: 29.0158
  内存使用: 0.6015
  能量使用: 0.8434
  推理时间: 2.1851秒

批次 50:
  奖励值: 101.4940
  收益率: 0.3327
  距离: 28.6919
  内存使用: 0.5457
  能量使用: 0.7744
  推理时间: 1.8801秒

批次 51:
  奖励值: 108.9586
  收益率: 0.3802
  距离: 27.5005
  内存使用: 0.8627
  能量使用: 0.9197
  推理时间: 2.0965秒

批次 52:
  奖励值: 115.5751
  收益率: 0.3843
  距离: 31.6777
  内存使用: 0.5742
  能量使用: 0.9978
  推理时间: 2.1206秒

批次 53:
  奖励值: 114.0562
  收益率: 0.3892
  距离: 32.0078
  内存使用: 0.5903
  能量使用: 0.8895
  推理时间: 2.1341秒

批次 54:
  奖励值: 118.8150
  收益率: 0.4041
  距离: 32.1075
  内存使用: 0.6743
  能量使用: 0.9881
  推理时间: 2.2150秒

批次 55:
  奖励值: 118.9114
  收益率: 0.4063
  距离: 29.8889
  内存使用: 0.5485
  能量使用: 0.9398
  推理时间: 2.2037秒

批次 56:
  奖励值: 113.7608
  收益率: 0.3892
  距离: 31.9554
  内存使用: 0.6322
  能量使用: 0.8908
  推理时间: 2.0944秒

批次 57:
  奖励值: 121.6727
  收益率: 0.3997
  距离: 28.9265
  内存使用: 0.5998
  能量使用: 0.9283
  推理时间: 2.2524秒

批次 58:
  奖励值: 123.3571
  收益率: 0.3971
  距离: 27.6168
  内存使用: 0.6834
  能量使用: 0.9856
  推理时间: 2.2165秒

批次 59:
  奖励值: 111.7669
  收益率: 0.3666
  距离: 26.8409
  内存使用: 0.8967
  能量使用: 0.9286
  推理时间: 2.1062秒

批次 60:
  奖励值: 116.1124
  收益率: 0.4089
  距离: 33.3163
  内存使用: 0.5756
  能量使用: 0.8832
  推理时间: 2.2119秒

批次 61:
  奖励值: 116.2383
  收益率: 0.3928
  距离: 33.1745
  内存使用: 0.6068
  能量使用: 0.9421
  推理时间: 2.1515秒

批次 62:
  奖励值: 104.7890
  收益率: 0.3516
  距离: 26.0731
  内存使用: 0.5425
  能量使用: 0.8064
  推理时间: 1.9173秒

批次 63:
  奖励值: 103.9736
  收益率: 0.3553
  距离: 27.6471
  内存使用: 0.5614
  能量使用: 0.8442
  推理时间: 1.9640秒

批次 64:
  奖励值: 121.6251
  收益率: 0.4091
  距离: 34.3283
  内存使用: 0.6910
  能量使用: 0.9305
  推理时间: 2.3014秒

批次 65:
  奖励值: 107.9158
  收益率: 0.3754
  距离: 29.6233
  内存使用: 0.4948
  能量使用: 0.8504
  推理时间: 2.0355秒

批次 66:
  奖励值: 117.8285
  收益率: 0.4031
  距离: 32.8061
  内存使用: 0.6444
  能量使用: 0.8925
  推理时间: 2.1823秒

批次 67:
  奖励值: 121.4700
  收益率: 0.4073
  距离: 27.5393
  内存使用: 0.5870
  能量使用: 0.9338
  推理时间: 2.1979秒

批次 68:
  奖励值: 109.6176
  收益率: 0.3635
  距离: 29.0411
  内存使用: 0.5233
  能量使用: 0.8004
  推理时间: 1.9862秒

批次 69:
  奖励值: 111.4991
  收益率: 0.3677
  距离: 29.9050
  内存使用: 0.6188
  能量使用: 0.8780
  推理时间: 2.0621秒

批次 70:
  奖励值: 119.1562
  收益率: 0.4024
  距离: 31.6108
  内存使用: 0.6260
  能量使用: 1.0508
  推理时间: 2.2338秒

批次 71:
  奖励值: 113.1347
  收益率: 0.3899
  距离: 34.5814
  内存使用: 0.5756
  能量使用: 0.9736
  推理时间: 2.1332秒

批次 72:
  奖励值: 117.9846
  收益率: 0.3800
  距离: 24.9629
  内存使用: 0.6469
  能量使用: 0.8210
  推理时间: 2.0898秒

批次 73:
  奖励值: 115.4589
  收益率: 0.3797
  距离: 26.6851
  内存使用: 0.5987
  能量使用: 0.7983
  推理时间: 2.0702秒

批次 74:
  奖励值: 104.6384
  收益率: 0.3549
  距离: 27.3796
  内存使用: 0.5298
  能量使用: 0.8139
  推理时间: 1.9415秒

批次 75:
  奖励值: 114.1807
  收益率: 0.3887
  距离: 29.8298
  内存使用: 0.6621
  能量使用: 0.9467
  推理时间: 2.1435秒

批次 76:
  奖励值: 116.3266
  收益率: 0.3938
  距离: 32.0400
  内存使用: 0.6399
  能量使用: 0.8669
  推理时间: 2.1852秒

批次 77:
  奖励值: 120.6910
  收益率: 0.3954
  距离: 31.9245
  内存使用: 0.5828
  能量使用: 0.9106
  推理时间: 2.1794秒

批次 78:
  奖励值: 122.8229
  收益率: 0.4034
  距离: 33.3424
  内存使用: 0.6104
  能量使用: 1.0013
  推理时间: 2.2740秒

批次 79:
  奖励值: 120.5277
  收益率: 0.3988
  距离: 34.0969
  内存使用: 0.6369
  能量使用: 1.0108
  推理时间: 2.2558秒

批次 80:
  奖励值: 103.8410
  收益率: 0.3514
  距离: 27.2802
  内存使用: 0.5737
  能量使用: 0.8640
  推理时间: 1.9690秒

批次 81:
  奖励值: 112.4807
  收益率: 0.3875
  距离: 27.9646
  内存使用: 0.5762
  能量使用: 0.8977
  推理时间: 2.1575秒

批次 82:
  奖励值: 110.9786
  收益率: 0.3759
  距离: 28.9478
  内存使用: 0.6374
  能量使用: 0.8576
  推理时间: 2.0282秒

批次 83:
  奖励值: 110.6818
  收益率: 0.3787
  距离: 27.3716
  内存使用: 0.5779
  能量使用: 0.8869
  推理时间: 2.0624秒

批次 84:
  奖励值: 118.4589
  收益率: 0.3904
  距离: 31.0215
  内存使用: 0.6982
  能量使用: 0.9626
  推理时间: 2.2237秒

批次 85:
  奖励值: 103.3414
  收益率: 0.3535
  距离: 28.8777
  内存使用: 0.8608
  能量使用: 0.7786
  推理时间: 1.9677秒

批次 86:
  奖励值: 104.3286
  收益率: 0.3480
  距离: 26.5885
  内存使用: 0.5175
  能量使用: 0.8369
  推理时间: 1.9158秒

批次 87:
  奖励值: 118.1904
  收益率: 0.3940
  距离: 30.3951
  内存使用: 0.6426
  能量使用: 0.9815
  推理时间: 2.1536秒

批次 88:
  奖励值: 117.1133
  收益率: 0.3869
  距离: 28.4818
  内存使用: 0.5721
  能量使用: 0.9195
  推理时间: 2.1446秒

批次 89:
  奖励值: 116.2930
  收益率: 0.3912
  距离: 27.9412
  内存使用: 0.5812
  能量使用: 0.8917
  推理时间: 2.2609秒

批次 90:
  奖励值: 114.2072
  收益率: 0.3857
  距离: 30.9295
  内存使用: 0.5753
  能量使用: 0.8808
  推理时间: 2.0784秒

批次 91:
  奖励值: 116.2858
  收益率: 0.3843
  距离: 31.8917
  内存使用: 0.6761
  能量使用: 0.8604
  推理时间: 2.1271秒

批次 92:
  奖励值: 118.6014
  收益率: 0.3794
  距离: 29.2629
  内存使用: 0.6134
  能量使用: 0.9210
  推理时间: 2.1317秒

批次 93:
  奖励值: 117.0479
  收益率: 0.3747
  距离: 28.2140
  内存使用: 0.5756
  能量使用: 0.9146
  推理时间: 2.1488秒

批次 94:
  奖励值: 115.4086
  收益率: 0.4028
  距离: 29.0863
  内存使用: 0.5890
  能量使用: 0.9075
  推理时间: 2.1015秒

批次 95:
  奖励值: 112.3422
  收益率: 0.3862
  距离: 31.8506
  内存使用: 0.5978
  能量使用: 0.8671
  推理时间: 2.0964秒

批次 96:
  奖励值: 105.9953
  收益率: 0.3608
  距离: 28.7903
  内存使用: 0.5044
  能量使用: 0.9146
  推理时间: 1.9428秒

批次 97:
  奖励值: 118.6419
  收益率: 0.4005
  距离: 27.7837
  内存使用: 0.6478
  能量使用: 0.8846
  推理时间: 2.1707秒

批次 98:
  奖励值: 108.1083
  收益率: 0.3560
  距离: 24.0105
  内存使用: 0.5588
  能量使用: 0.8140
  推理时间: 1.9648秒

批次 99:
  奖励值: 119.3176
  收益率: 0.3893
  距离: 31.1739
  内存使用: 0.6709
  能量使用: 0.9199
  推理时间: 2.2049秒

批次 100:
  奖励值: 116.5951
  收益率: 0.3794
  距离: 30.0773
  内存使用: 0.6287
  能量使用: 0.9038
  推理时间: 2.1261秒


==================== 总结 ====================
平均收益率: 0.3822
平均能量使用: 0.9003
平均推理时间: 2.1128秒
