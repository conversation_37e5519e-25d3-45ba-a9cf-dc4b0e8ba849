推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 27.6273
  收益率: 0.6940
  距离: 7.1425
  内存使用: -0.0310
  能量使用: 0.2338
  推理时间: 0.8980秒

批次 2:
  奖励值: 28.6147
  收益率: 0.6940
  距离: 7.0123
  内存使用: -0.0543
  能量使用: 0.2126
  推理时间: 0.5757秒

批次 3:
  奖励值: 25.2656
  收益率: 0.6166
  距离: 5.9124
  内存使用: -0.0670
  能量使用: 0.2234
  推理时间: 0.5072秒

批次 4:
  奖励值: 28.0178
  收益率: 0.6970
  距离: 8.1191
  内存使用: -0.0393
  能量使用: 0.2845
  推理时间: 0.5717秒

批次 5:
  奖励值: 26.5028
  收益率: 0.6795
  距离: 6.6652
  内存使用: -0.0968
  能量使用: 0.2260
  推理时间: 0.5036秒

批次 6:
  奖励值: 31.2370
  收益率: 0.7413
  距离: 10.1244
  内存使用: -0.0612
  能量使用: 0.2628
  推理时间: 0.6203秒

批次 7:
  奖励值: 24.4521
  收益率: 0.6303
  距离: 7.5262
  内存使用: -0.0778
  能量使用: 0.2472
  推理时间: 0.4889秒

批次 8:
  奖励值: 28.0913
  收益率: 0.7097
  距离: 9.6230
  内存使用: -0.0727
  能量使用: 0.2671
  推理时间: 0.5592秒

批次 9:
  奖励值: 28.0734
  收益率: 0.7032
  距离: 9.0742
  内存使用: -0.0854
  能量使用: 0.2644
  推理时间: 0.5723秒

批次 10:
  奖励值: 29.7500
  收益率: 0.7290
  距离: 9.3357
  内存使用: 0.2710
  能量使用: 0.2933
  推理时间: 0.6171秒

批次 11:
  奖励值: 27.6412
  收益率: 0.6976
  距离: 7.7447
  内存使用: -0.0891
  能量使用: 0.2621
  推理时间: 0.5625秒

批次 12:
  奖励值: 32.4168
  收益率: 0.7531
  距离: 6.9481
  内存使用: -0.0449
  能量使用: 0.2633
  推理时间: 0.6152秒

批次 13:
  奖励值: 30.7253
  收益率: 0.7335
  距离: 9.3387
  内存使用: -0.0320
  能量使用: 0.2427
  推理时间: 0.6155秒

批次 14:
  奖励值: 28.8246
  收益率: 0.7184
  距离: 6.9233
  内存使用: -0.0414
  能量使用: 0.2689
  推理时间: 0.5737秒

批次 15:
  奖励值: 29.4142
  收益率: 0.7086
  距离: 8.5585
  内存使用: -0.0412
  能量使用: 0.2543
  推理时间: 0.5535秒

批次 16:
  奖励值: 28.9623
  收益率: 0.6888
  距离: 8.2863
  内存使用: -0.0447
  能量使用: 0.2339
  推理时间: 0.5898秒

批次 17:
  奖励值: 29.7518
  收益率: 0.7210
  距离: 7.4139
  内存使用: -0.0282
  能量使用: 0.3106
  推理时间: 0.6284秒

批次 18:
  奖励值: 23.6913
  收益率: 0.6161
  距离: 7.7031
  内存使用: -0.1001
  能量使用: 0.2144
  推理时间: 0.4800秒

批次 19:
  奖励值: 30.9234
  收益率: 0.7433
  距离: 9.7095
  内存使用: -0.0488
  能量使用: 0.2925
  推理时间: 0.6534秒

批次 20:
  奖励值: 26.8608
  收益率: 0.6652
  距离: 5.7288
  内存使用: -0.0658
  能量使用: 0.2154
  推理时间: 0.5156秒

批次 21:
  奖励值: 28.5951
  收益率: 0.7000
  距离: 8.4232
  内存使用: -0.0688
  能量使用: 0.2555
  推理时间: 0.5752秒

批次 22:
  奖励值: 27.2919
  收益率: 0.6730
  距离: 8.5829
  内存使用: 0.0005
  能量使用: 0.2327
  推理时间: 0.5740秒

批次 23:
  奖励值: 28.9448
  收益率: 0.6923
  距离: 6.6804
  内存使用: -0.0373
  能量使用: 0.2672
  推理时间: 0.5492秒

批次 24:
  奖励值: 26.6291
  收益率: 0.6934
  距离: 7.1306
  内存使用: -0.0481
  能量使用: 0.2594
  推理时间: 0.5450秒

批次 25:
  奖励值: 32.9929
  收益率: 0.7753
  距离: 8.9503
  内存使用: -0.0429
  能量使用: 0.3069
  推理时间: 0.6898秒

批次 26:
  奖励值: 28.1641
  收益率: 0.7229
  距离: 10.2177
  内存使用: -0.0183
  能量使用: 0.2723
  推理时间: 0.6220秒

批次 27:
  奖励值: 29.8304
  收益率: 0.6906
  距离: 7.5126
  内存使用: -0.0214
  能量使用: 0.2480
  推理时间: 0.6036秒

批次 28:
  奖励值: 27.3638
  收益率: 0.6695
  距离: 7.0845
  内存使用: -0.0095
  能量使用: 0.1974
  推理时间: 0.5740秒

批次 29:
  奖励值: 28.6140
  收益率: 0.6949
  距离: 5.0759
  内存使用: -0.1068
  能量使用: 0.2029
  推理时间: 0.4984秒

批次 30:
  奖励值: 29.1189
  收益率: 0.7100
  距离: 7.1477
  内存使用: -0.0456
  能量使用: 0.2602
  推理时间: 0.5561秒

批次 31:
  奖励值: 30.5478
  收益率: 0.7012
  距离: 9.0850
  内存使用: -0.0307
  能量使用: 0.2501
  推理时间: 0.6195秒

批次 32:
  奖励值: 29.3539
  收益率: 0.7207
  距离: 8.6344
  内存使用: -0.0169
  能量使用: 0.2749
  推理时间: 0.5886秒

批次 33:
  奖励值: 27.4669
  收益率: 0.6987
  距离: 10.3000
  内存使用: -0.0746
  能量使用: 0.2408
  推理时间: 0.5521秒

批次 34:
  奖励值: 27.6532
  收益率: 0.6731
  距离: 7.1589
  内存使用: -0.0617
  能量使用: 0.1964
  推理时间: 0.5403秒

批次 35:
  奖励值: 28.5125
  收益率: 0.6864
  距离: 7.5627
  内存使用: -0.0455
  能量使用: 0.2578
  推理时间: 0.5646秒

批次 36:
  奖励值: 34.3843
  收益率: 0.7764
  距离: 8.7845
  内存使用: -0.0615
  能量使用: 0.3085
  推理时间: 0.6658秒

批次 37:
  奖励值: 25.8444
  收益率: 0.6932
  距离: 5.5775
  内存使用: -0.0941
  能量使用: 0.2277
  推理时间: 0.5134秒

批次 38:
  奖励值: 24.1273
  收益率: 0.6359
  距离: 6.8783
  内存使用: -0.1331
  能量使用: 0.2003
  推理时间: 0.4748秒

批次 39:
  奖励值: 30.8530
  收益率: 0.7543
  距离: 8.6660
  内存使用: -0.0437
  能量使用: 0.2716
  推理时间: 0.6072秒

批次 40:
  奖励值: 25.7284
  收益率: 0.6444
  距离: 6.4015
  内存使用: -0.0944
  能量使用: 0.2360
  推理时间: 0.5026秒

批次 41:
  奖励值: 25.6453
  收益率: 0.6697
  距离: 7.7865
  内存使用: -0.0929
  能量使用: 0.2158
  推理时间: 0.5039秒

批次 42:
  奖励值: 31.7528
  收益率: 0.7578
  距离: 9.4680
  内存使用: -0.0297
  能量使用: 0.2560
  推理时间: 0.6474秒

批次 43:
  奖励值: 28.2774
  收益率: 0.6926
  距离: 7.2356
  内存使用: -0.0159
  能量使用: 0.2255
  推理时间: 0.5935秒

批次 44:
  奖励值: 28.0380
  收益率: 0.6695
  距离: 7.7468
  内存使用: -0.0826
  能量使用: 0.2598
  推理时间: 0.5570秒

批次 45:
  奖励值: 29.8066
  收益率: 0.7381
  距离: 8.4160
  内存使用: -0.0464
  能量使用: 0.2172
  推理时间: 0.5918秒

批次 46:
  奖励值: 29.3300
  收益率: 0.7229
  距离: 7.9914
  内存使用: -0.0874
  能量使用: 0.2360
  推理时间: 0.5613秒

批次 47:
  奖励值: 24.8276
  收益率: 0.6253
  距离: 6.6230
  内存使用: -0.1042
  能量使用: 0.2259
  推理时间: 0.4828秒

批次 48:
  奖励值: 27.7353
  收益率: 0.6851
  距离: 8.4315
  内存使用: 0.2431
  能量使用: 0.2547
  推理时间: 0.5956秒

批次 49:
  奖励值: 33.0411
  收益率: 0.7520
  距离: 7.7636
  内存使用: -0.0711
  能量使用: 0.2252
  推理时间: 0.6040秒

批次 50:
  奖励值: 27.4034
  收益率: 0.6534
  距离: 7.6592
  内存使用: -0.0948
  能量使用: 0.2288
  推理时间: 0.5282秒

批次 51:
  奖励值: 27.3045
  收益率: 0.6630
  距离: 6.2211
  内存使用: -0.0627
  能量使用: 0.2326
  推理时间: 0.5517秒

批次 52:
  奖励值: 28.9992
  收益率: 0.7229
  距离: 8.5911
  内存使用: -0.0268
  能量使用: 0.2372
  推理时间: 0.5757秒

批次 53:
  奖励值: 30.1459
  收益率: 0.7182
  距离: 8.2868
  内存使用: -0.0285
  能量使用: 0.2500
  推理时间: 0.6090秒

批次 54:
  奖励值: 27.7761
  收益率: 0.6856
  距离: 7.1068
  内存使用: -0.0710
  能量使用: 0.2119
  推理时间: 0.5635秒

批次 55:
  奖励值: 31.0693
  收益率: 0.7257
  距离: 6.4289
  内存使用: -0.0240
  能量使用: 0.2564
  推理时间: 0.6147秒

批次 56:
  奖励值: 25.9582
  收益率: 0.6452
  距离: 6.1404
  内存使用: -0.0652
  能量使用: 0.2083
  推理时间: 0.5124秒

批次 57:
  奖励值: 27.1621
  收益率: 0.7051
  距离: 9.0435
  内存使用: -0.0305
  能量使用: 0.2628
  推理时间: 0.5713秒

批次 58:
  奖励值: 27.7413
  收益率: 0.7171
  距离: 8.7093
  内存使用: -0.0721
  能量使用: 0.2802
  推理时间: 0.5755秒

批次 59:
  奖励值: 33.6563
  收益率: 0.7589
  距离: 9.2786
  内存使用: -0.0386
  能量使用: 0.2473
  推理时间: 0.6514秒

批次 60:
  奖励值: 27.4917
  收益率: 0.6911
  距离: 8.8119
  内存使用: -0.0313
  能量使用: 0.2360
  推理时间: 0.5643秒

批次 61:
  奖励值: 27.9524
  收益率: 0.7009
  距离: 8.1070
  内存使用: -0.0613
  能量使用: 0.2494
  推理时间: 0.5424秒

批次 62:
  奖励值: 26.6928
  收益率: 0.7016
  距离: 6.6816
  内存使用: -0.0954
  能量使用: 0.2282
  推理时间: 0.5093秒

批次 63:
  奖励值: 29.7097
  收益率: 0.7670
  距离: 8.1068
  内存使用: -0.0090
  能量使用: 0.2828
  推理时间: 0.6443秒

批次 64:
  奖励值: 24.9976
  收益率: 0.6581
  距离: 6.0424
  内存使用: -0.0678
  能量使用: 0.2301
  推理时间: 0.5122秒

批次 65:
  奖励值: 31.6950
  收益率: 0.7546
  距离: 10.2331
  内存使用: -0.0605
  能量使用: 0.2375
  推理时间: 0.6401秒

批次 66:
  奖励值: 29.7952
  收益率: 0.7342
  距离: 9.7478
  内存使用: -0.0202
  能量使用: 0.2819
  推理时间: 0.6233秒

批次 67:
  奖励值: 30.3997
  收益率: 0.7333
  距离: 7.1698
  内存使用: -0.0407
  能量使用: 0.2715
  推理时间: 0.5826秒

批次 68:
  奖励值: 26.7202
  收益率: 0.6808
  距离: 7.4089
  内存使用: -0.0699
  能量使用: 0.2206
  推理时间: 0.4949秒

批次 69:
  奖励值: 29.0376
  收益率: 0.7237
  距离: 7.7449
  内存使用: -0.0562
  能量使用: 0.2361
  推理时间: 0.5716秒

批次 70:
  奖励值: 30.2629
  收益率: 0.7160
  距离: 8.8797
  内存使用: -0.0615
  能量使用: 0.2561
  推理时间: 0.6056秒

批次 71:
  奖励值: 30.1856
  收益率: 0.7296
  距离: 7.4573
  内存使用: -0.0781
  能量使用: 0.2495
  推理时间: 0.5645秒

批次 72:
  奖励值: 27.1659
  收益率: 0.6828
  距离: 7.5000
  内存使用: -0.0795
  能量使用: 0.2476
  推理时间: 0.5422秒

批次 73:
  奖励值: 33.4747
  收益率: 0.7535
  距离: 8.5462
  内存使用: -0.0143
  能量使用: 0.3188
  推理时间: 0.6658秒

批次 74:
  奖励值: 32.1962
  收益率: 0.7684
  距离: 10.3469
  内存使用: -0.0333
  能量使用: 0.2940
  推理时间: 0.6442秒

批次 75:
  奖励值: 29.8184
  收益率: 0.7505
  距离: 8.7450
  内存使用: -0.0436
  能量使用: 0.2618
  推理时间: 0.6026秒

批次 76:
  奖励值: 26.9034
  收益率: 0.7090
  距离: 7.4137
  内存使用: -0.0771
  能量使用: 0.2566
  推理时间: 0.5472秒

批次 77:
  奖励值: 31.2877
  收益率: 0.7117
  距离: 6.8352
  内存使用: -0.0569
  能量使用: 0.2463
  推理时间: 0.5853秒

批次 78:
  奖励值: 27.1764
  收益率: 0.6982
  距离: 8.8934
  内存使用: -0.0905
  能量使用: 0.2442
  推理时间: 0.5911秒

批次 79:
  奖励值: 26.0421
  收益率: 0.6909
  距离: 6.7780
  内存使用: -0.0711
  能量使用: 0.2088
  推理时间: 0.5399秒

批次 80:
  奖励值: 29.7132
  收益率: 0.7689
  距离: 9.5361
  内存使用: -0.0291
  能量使用: 0.2666
  推理时间: 0.6401秒

批次 81:
  奖励值: 22.0108
  收益率: 0.6334
  距离: 6.6789
  内存使用: -0.0987
  能量使用: 0.1983
  推理时间: 0.4923秒

批次 82:
  奖励值: 33.1984
  收益率: 0.7611
  距离: 8.4797
  内存使用: 0.0116
  能量使用: 0.3120
  推理时间: 0.6877秒

批次 83:
  奖励值: 32.9597
  收益率: 0.7596
  距离: 8.9970
  内存使用: -0.0247
  能量使用: 0.3083
  推理时间: 0.6448秒

批次 84:
  奖励值: 28.1909
  收益率: 0.6653
  距离: 7.0895
  内存使用: -0.0693
  能量使用: 0.1980
  推理时间: 0.5353秒

批次 85:
  奖励值: 27.4967
  收益率: 0.6548
  距离: 7.5131
  内存使用: -0.1091
  能量使用: 0.2025
  推理时间: 0.5160秒

批次 86:
  奖励值: 27.4291
  收益率: 0.6991
  距离: 8.1524
  内存使用: -0.0495
  能量使用: 0.2390
  推理时间: 0.5834秒

批次 87:
  奖励值: 27.3710
  收益率: 0.6896
  距离: 7.2433
  内存使用: -0.0683
  能量使用: 0.2830
  推理时间: 0.5516秒

批次 88:
  奖励值: 29.9895
  收益率: 0.7050
  距离: 7.2674
  内存使用: -0.0501
  能量使用: 0.2037
  推理时间: 0.5839秒

批次 89:
  奖励值: 25.0395
  收益率: 0.6518
  距离: 8.2251
  内存使用: -0.0858
  能量使用: 0.1819
  推理时间: 0.4878秒

批次 90:
  奖励值: 27.0602
  收益率: 0.7087
  距离: 7.4796
  内存使用: -0.0539
  能量使用: 0.2539
  推理时间: 0.5624秒

批次 91:
  奖励值: 29.0176
  收益率: 0.7338
  距离: 9.5368
  内存使用: -0.0400
  能量使用: 0.2680
  推理时间: 0.6423秒

批次 92:
  奖励值: 28.9542
  收益率: 0.6571
  距离: 5.9404
  内存使用: -0.0686
  能量使用: 0.2198
  推理时间: 0.5131秒

批次 93:
  奖励值: 30.3897
  收益率: 0.6786
  距离: 7.0087
  内存使用: -0.0331
  能量使用: 0.2451
  推理时间: 0.5455秒

批次 94:
  奖励值: 27.1642
  收益率: 0.7040
  距离: 8.2704
  内存使用: -0.0455
  能量使用: 0.2467
  推理时间: 0.5828秒

批次 95:
  奖励值: 29.9849
  收益率: 0.7014
  距离: 8.4427
  内存使用: -0.0435
  能量使用: 0.2310
  推理时间: 0.5833秒

批次 96:
  奖励值: 27.9191
  收益率: 0.6853
  距离: 7.6601
  内存使用: -0.0903
  能量使用: 0.1921
  推理时间: 0.5128秒

批次 97:
  奖励值: 26.5337
  收益率: 0.6674
  距离: 7.7508
  内存使用: -0.0537
  能量使用: 0.2354
  推理时间: 0.5280秒

批次 98:
  奖励值: 31.2251
  收益率: 0.6960
  距离: 6.9310
  内存使用: -0.0615
  能量使用: 0.2803
  推理时间: 0.5752秒

批次 99:
  奖励值: 25.4301
  收益率: 0.6824
  距离: 6.9748
  内存使用: -0.0838
  能量使用: 0.2488
  推理时间: 0.5273秒

批次 100:
  奖励值: 31.2069
  收益率: 0.7398
  距离: 10.2004
  内存使用: -0.0683
  能量使用: 0.2542
  推理时间: 0.6205秒


==================== 总结 ====================
平均收益率: 0.7025
平均能量使用: 0.2470
平均推理时间: 0.5750秒
