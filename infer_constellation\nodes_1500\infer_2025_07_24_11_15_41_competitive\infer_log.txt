推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 137.1353
  收益率: 0.2331
  距离: 36.9162
  内存使用: 0.7702
  能量使用: 1.0429
  推理时间: 2.4618秒

批次 2:
  奖励值: 120.1798
  收益率: 0.2005
  距离: 28.7088
  内存使用: 0.6225
  能量使用: 0.9091
  推理时间: 2.1118秒

批次 3:
  奖励值: 120.3537
  收益率: 0.2063
  距离: 31.2125
  内存使用: 0.5667
  能量使用: 0.9133
  推理时间: 2.1273秒

批次 4:
  奖励值: 125.6407
  收益率: 0.2038
  距离: 27.0879
  内存使用: 0.6891
  能量使用: 0.9415
  推理时间: 2.1639秒

批次 5:
  奖励值: 120.3247
  收益率: 0.2011
  距离: 28.7594
  内存使用: 0.6155
  能量使用: 0.9756
  推理时间: 2.1088秒

批次 6:
  奖励值: 115.7244
  收益率: 0.1881
  距离: 27.3671
  内存使用: 0.9082
  能量使用: 0.8758
  推理时间: 2.0343秒

批次 7:
  奖励值: 110.7928
  收益率: 0.1841
  距离: 28.7235
  内存使用: 0.8801
  能量使用: 0.8109
  推理时间: 1.9599秒

批次 8:
  奖励值: 116.1802
  收益率: 0.1945
  距离: 29.3897
  内存使用: 0.9119
  能量使用: 0.9379
  推理时间: 2.0603秒

批次 9:
  奖励值: 121.2083
  收益率: 0.2000
  距离: 26.2270
  内存使用: 0.6561
  能量使用: 0.9003
  推理时间: 2.1220秒

批次 10:
  奖励值: 122.5547
  收益率: 0.2048
  距离: 27.2783
  内存使用: 0.6468
  能量使用: 0.9654
  推理时间: 2.1126秒

批次 11:
  奖励值: 117.4756
  收益率: 0.1969
  距离: 28.9920
  内存使用: 0.5899
  能量使用: 0.8669
  推理时间: 2.0346秒

批次 12:
  奖励值: 120.4183
  收益率: 0.1998
  距离: 30.3746
  内存使用: 0.6699
  能量使用: 0.9189
  推理时间: 2.1518秒

批次 13:
  奖励值: 122.4300
  收益率: 0.2069
  距离: 32.4344
  内存使用: 0.5868
  能量使用: 0.9187
  推理时间: 2.1590秒

批次 14:
  奖励值: 122.7330
  收益率: 0.2035
  距离: 29.3300
  内存使用: 0.9160
  能量使用: 0.8880
  推理时间: 2.0910秒

批次 15:
  奖励值: 117.3572
  收益率: 0.1960
  距离: 28.2471
  内存使用: 0.8906
  能量使用: 0.9478
  推理时间: 2.1762秒

批次 16:
  奖励值: 121.8858
  收益率: 0.2059
  距离: 30.4842
  内存使用: 0.6210
  能量使用: 0.9232
  推理时间: 2.1389秒

批次 17:
  奖励值: 128.1942
  收益率: 0.2123
  距离: 31.6813
  内存使用: 0.6207
  能量使用: 0.9096
  推理时间: 2.3282秒

批次 18:
  奖励值: 150.7505
  收益率: 0.2571
  距离: 35.6728
  内存使用: 0.8180
  能量使用: 1.0082
  推理时间: 2.5204秒

批次 19:
  奖励值: 125.7906
  收益率: 0.2098
  距离: 29.2360
  内存使用: 0.6332
  能量使用: 0.9495
  推理时间: 2.1590秒

批次 20:
  奖励值: 130.2923
  收益率: 0.2163
  距离: 29.1690
  内存使用: 0.6720
  能量使用: 0.9744
  推理时间: 2.2190秒

批次 21:
  奖励值: 113.0154
  收益率: 0.1912
  距离: 30.1831
  内存使用: 0.9054
  能量使用: 0.8807
  推理时间: 1.9931秒

批次 22:
  奖励值: 115.6577
  收益率: 0.1903
  距离: 29.8585
  内存使用: 0.9117
  能量使用: 0.9143
  推理时间: 1.9590秒

批次 23:
  奖励值: 128.6341
  收益率: 0.2121
  距离: 31.3365
  内存使用: 0.6513
  能量使用: 0.9438
  推理时间: 2.3125秒

批次 24:
  奖励值: 111.2098
  收益率: 0.1935
  距离: 31.5909
  内存使用: 0.9108
  能量使用: 0.8786
  推理时间: 2.0062秒

批次 25:
  奖励值: 110.7673
  收益率: 0.1829
  距离: 28.3581
  内存使用: 0.8779
  能量使用: 0.8294
  推理时间: 1.9994秒

批次 26:
  奖励值: 116.0958
  收益率: 0.1964
  距离: 32.1303
  内存使用: 0.8937
  能量使用: 0.9843
  推理时间: 2.1210秒

批次 27:
  奖励值: 121.8606
  收益率: 0.2044
  距离: 29.4882
  内存使用: 0.5949
  能量使用: 0.9956
  推理时间: 2.1825秒

批次 28:
  奖励值: 110.3768
  收益率: 0.1855
  距离: 28.4925
  内存使用: 0.8759
  能量使用: 0.8781
  推理时间: 1.9728秒

批次 29:
  奖励值: 129.1532
  收益率: 0.2234
  距离: 34.2923
  内存使用: 0.6338
  能量使用: 0.9675
  推理时间: 2.2892秒

批次 30:
  奖励值: 114.7742
  收益率: 0.1937
  距离: 25.8450
  内存使用: 0.9033
  能量使用: 0.9034
  推理时间: 2.0358秒

批次 31:
  奖励值: 110.8567
  收益率: 0.1870
  距离: 25.9540
  内存使用: 0.9092
  能量使用: 0.8234
  推理时间: 2.0323秒

批次 32:
  奖励值: 124.1748
  收益率: 0.2035
  距离: 29.8096
  内存使用: 0.6586
  能量使用: 0.9820
  推理时间: 2.2490秒

批次 33:
  奖励值: 111.5716
  收益率: 0.1899
  距离: 30.4456
  内存使用: 0.9116
  能量使用: 0.8996
  推理时间: 2.0494秒

批次 34:
  奖励值: 115.1899
  收益率: 0.1948
  距离: 30.4486
  内存使用: 0.8724
  能量使用: 0.8991
  推理时间: 2.0042秒

批次 35:
  奖励值: 99.2369
  收益率: 0.1670
  距离: 26.3058
  内存使用: 0.8312
  能量使用: 0.7893
  推理时间: 1.7906秒

批次 36:
  奖励值: 115.8477
  收益率: 0.1973
  距离: 31.4789
  内存使用: 0.8938
  能量使用: 0.9319
  推理时间: 2.0993秒

批次 37:
  奖励值: 109.8870
  收益率: 0.1805
  距离: 27.0792
  内存使用: 0.8677
  能量使用: 0.8790
  推理时间: 1.9718秒

批次 38:
  奖励值: 127.0011
  收益率: 0.2053
  距离: 25.5747
  内存使用: 0.6333
  能量使用: 0.9898
  推理时间: 2.1526秒

批次 39:
  奖励值: 115.3648
  收益率: 0.1933
  距离: 28.8986
  内存使用: 0.9076
  能量使用: 0.8643
  推理时间: 2.0526秒

批次 40:
  奖励值: 119.7226
  收益率: 0.1998
  距离: 30.4041
  内存使用: 0.6521
  能量使用: 0.8987
  推理时间: 2.1249秒

批次 41:
  奖励值: 118.1727
  收益率: 0.1956
  距离: 29.4113
  内存使用: 0.8889
  能量使用: 0.9544
  推理时间: 2.0211秒

批次 42:
  奖励值: 112.8700
  收益率: 0.1880
  距离: 27.8736
  内存使用: 0.6435
  能量使用: 0.9427
  推理时间: 1.9953秒

批次 43:
  奖励值: 112.5870
  收益率: 0.1915
  距离: 28.6592
  内存使用: 0.9120
  能量使用: 0.8547
  推理时间: 2.0426秒

批次 44:
  奖励值: 141.9244
  收益率: 0.2394
  距离: 34.0835
  内存使用: 0.7659
  能量使用: 1.1018
  推理时间: 2.4573秒

批次 45:
  奖励值: 129.3265
  收益率: 0.2189
  距离: 29.6356
  内存使用: 0.6834
  能量使用: 1.0279
  推理时间: 2.1983秒

批次 46:
  奖励值: 128.6755
  收益率: 0.2119
  距离: 32.5879
  内存使用: 0.6927
  能量使用: 0.9683
  推理时间: 2.2195秒

批次 47:
  奖励值: 117.7007
  收益率: 0.1996
  距离: 29.2477
  内存使用: 0.6127
  能量使用: 0.9381
  推理时间: 2.0773秒

批次 48:
  奖励值: 113.8078
  收益率: 0.1899
  距离: 27.6406
  内存使用: 0.9125
  能量使用: 0.8313
  推理时间: 1.9580秒

批次 49:
  奖励值: 110.1495
  收益率: 0.1810
  距离: 25.8738
  内存使用: 0.5804
  能量使用: 0.8468
  推理时间: 1.9256秒

批次 50:
  奖励值: 111.1721
  收益率: 0.1880
  距离: 27.0994
  内存使用: 0.9135
  能量使用: 0.8429
  推理时间: 1.9439秒

批次 51:
  奖励值: 109.6756
  收益率: 0.1882
  距离: 31.5907
  内存使用: 0.8355
  能量使用: 0.8226
  推理时间: 2.0954秒

批次 52:
  奖励值: 101.8406
  收益率: 0.1731
  距离: 23.6767
  内存使用: 0.8322
  能量使用: 0.8098
  推理时间: 1.9558秒

批次 53:
  奖励值: 122.8209
  收益率: 0.1977
  距离: 26.8977
  内存使用: 0.5655
  能量使用: 0.8948
  推理时间: 2.0569秒

批次 54:
  奖励值: 123.9306
  收益率: 0.2023
  距离: 26.3593
  内存使用: 0.6373
  能量使用: 0.9421
  推理时间: 2.0854秒

批次 55:
  奖励值: 127.5356
  收益率: 0.2087
  距离: 27.9118
  内存使用: 0.6430
  能量使用: 0.9738
  推理时间: 2.1025秒

批次 56:
  奖励值: 117.8045
  收益率: 0.2003
  距离: 30.7342
  内存使用: 0.9093
  能量使用: 0.9475
  推理时间: 2.2004秒

批次 57:
  奖励值: 122.1573
  收益率: 0.2044
  距离: 29.9416
  内存使用: 0.6387
  能量使用: 0.9050
  推理时间: 2.0777秒

批次 58:
  奖励值: 110.2352
  收益率: 0.1819
  距离: 25.5718
  内存使用: 0.8696
  能量使用: 0.8882
  推理时间: 1.8979秒

批次 59:
  奖励值: 115.6770
  收益率: 0.1923
  距离: 29.2606
  内存使用: 0.9136
  能量使用: 0.8717
  推理时间: 1.9864秒

批次 60:
  奖励值: 116.4534
  收益率: 0.1900
  距离: 26.0708
  内存使用: 0.5810
  能量使用: 0.8415
  推理时间: 1.9075秒

批次 61:
  奖励值: 138.7681
  收益率: 0.2346
  距离: 35.0348
  内存使用: 0.7490
  能量使用: 1.0800
  推理时间: 2.4061秒

批次 62:
  奖励值: 120.5801
  收益率: 0.2010
  距离: 26.7347
  内存使用: 0.5975
  能量使用: 0.9074
  推理时间: 2.0488秒

批次 63:
  奖励值: 128.0147
  收益率: 0.2144
  距离: 32.0772
  内存使用: 0.7118
  能量使用: 0.9816
  推理时间: 2.2683秒

批次 64:
  奖励值: 116.2068
  收益率: 0.1981
  距离: 30.7582
  内存使用: 0.8954
  能量使用: 0.9328
  推理时间: 2.1026秒

批次 65:
  奖励值: 135.7115
  收益率: 0.2232
  距离: 31.4619
  内存使用: 0.6882
  能量使用: 1.0268
  推理时间: 2.3358秒

批次 66:
  奖励值: 115.2494
  收益率: 0.1889
  距离: 29.4898
  内存使用: 0.9169
  能量使用: 0.8944
  推理时间: 2.0160秒

批次 67:
  奖励值: 133.0512
  收益率: 0.2235
  距离: 34.0007
  内存使用: 0.6983
  能量使用: 0.9985
  推理时间: 2.2396秒

批次 68:
  奖励值: 114.2098
  收益率: 0.1896
  距离: 28.9958
  内存使用: 0.9042
  能量使用: 0.8803
  推理时间: 2.0508秒

批次 69:
  奖励值: 141.2927
  收益率: 0.2386
  距离: 35.4484
  内存使用: 0.7209
  能量使用: 1.0452
  推理时间: 2.5059秒

批次 70:
  奖励值: 109.9684
  收益率: 0.1874
  距离: 30.1252
  内存使用: 0.8546
  能量使用: 0.8834
  推理时间: 1.9480秒

批次 71:
  奖励值: 113.9839
  收益率: 0.1910
  距离: 26.9243
  内存使用: 0.6046
  能量使用: 0.9033
  推理时间: 1.9683秒

批次 72:
  奖励值: 126.4576
  收益率: 0.2114
  距离: 33.4562
  内存使用: 0.6938
  能量使用: 0.9347
  推理时间: 2.2317秒

批次 73:
  奖励值: 115.7153
  收益率: 0.1938
  距离: 27.4467
  内存使用: 0.6347
  能量使用: 0.8881
  推理时间: 2.0349秒

批次 74:
  奖励值: 126.4339
  收益率: 0.2095
  距离: 29.0883
  内存使用: 0.7067
  能量使用: 0.9372
  推理时间: 2.1645秒

批次 75:
  奖励值: 116.7078
  收益率: 0.1969
  距离: 29.7749
  内存使用: 0.8946
  能量使用: 0.9148
  推理时间: 2.0883秒

批次 76:
  奖励值: 121.0574
  收益率: 0.1991
  距离: 28.3651
  内存使用: 0.6179
  能量使用: 0.9023
  推理时间: 2.1176秒

批次 77:
  奖励值: 110.3679
  收益率: 0.1866
  距离: 30.1915
  内存使用: 0.4825
  能量使用: 0.7902
  推理时间: 1.8988秒

批次 78:
  奖励值: 114.3472
  收益率: 0.1925
  距离: 30.1710
  内存使用: 0.5924
  能量使用: 0.9422
  推理时间: 1.9720秒

批次 79:
  奖励值: 124.5104
  收益率: 0.2109
  距离: 30.5529
  内存使用: 0.6856
  能量使用: 0.9408
  推理时间: 2.1825秒

批次 80:
  奖励值: 123.6364
  收益率: 0.2105
  距离: 29.9285
  内存使用: 0.6960
  能量使用: 0.9029
  推理时间: 2.2043秒

批次 81:
  奖励值: 113.5523
  收益率: 0.1925
  距离: 28.7576
  内存使用: 0.6202
  能量使用: 0.9176
  推理时间: 1.9782秒

批次 82:
  奖励值: 123.3034
  收益率: 0.2042
  距离: 34.1311
  内存使用: 0.6962
  能量使用: 0.9659
  推理时间: 2.1643秒

批次 83:
  奖励值: 123.0586
  收益率: 0.2008
  距离: 30.5539
  内存使用: 0.5684
  能量使用: 0.9606
  推理时间: 2.1201秒

批次 84:
  奖励值: 114.5521
  收益率: 0.1880
  距离: 26.0631
  内存使用: 0.6030
  能量使用: 0.8297
  推理时间: 1.9838秒

批次 85:
  奖励值: 108.0912
  收益率: 0.1787
  距离: 28.6471
  内存使用: 0.9054
  能量使用: 0.8650
  推理时间: 1.8359秒

批次 86:
  奖励值: 128.3713
  收益率: 0.2122
  距离: 32.1032
  内存使用: 0.7154
  能量使用: 1.0388
  推理时间: 2.2270秒

批次 87:
  奖励值: 113.5345
  收益率: 0.1856
  距离: 27.2424
  内存使用: 0.9059
  能量使用: 0.9826
  推理时间: 2.0501秒

批次 88:
  奖励值: 117.4424
  收益率: 0.1983
  距离: 26.3101
  内存使用: 0.9122
  能量使用: 0.8928
  推理时间: 2.0252秒

批次 89:
  奖励值: 142.7863
  收益率: 0.2365
  距离: 33.4735
  内存使用: 0.7882
  能量使用: 1.1655
  推理时间: 2.4618秒

批次 90:
  奖励值: 127.0821
  收益率: 0.2089
  距离: 31.3285
  内存使用: 0.6585
  能量使用: 1.0488
  推理时间: 2.1935秒

批次 91:
  奖励值: 110.9772
  收益率: 0.1890
  距离: 28.7898
  内存使用: 0.8280
  能量使用: 0.8278
  推理时间: 2.0264秒

批次 92:
  奖励值: 139.1034
  收益率: 0.2321
  距离: 35.1179
  内存使用: 0.7953
  能量使用: 1.0799
  推理时间: 2.4002秒

批次 93:
  奖励值: 124.5648
  收益率: 0.2085
  距离: 30.2077
  内存使用: 0.6426
  能量使用: 1.0199
  推理时间: 2.2180秒

批次 94:
  奖励值: 121.4124
  收益率: 0.2026
  距离: 31.4541
  内存使用: 0.6413
  能量使用: 0.8799
  推理时间: 2.1616秒

批次 95:
  奖励值: 118.4894
  收益率: 0.1990
  距离: 29.0980
  内存使用: 0.9115
  能量使用: 0.8117
  推理时间: 2.0567秒

批次 96:
  奖励值: 117.4409
  收益率: 0.2006
  距离: 34.2609
  内存使用: 0.5705
  能量使用: 0.8868
  推理时间: 2.0478秒

批次 97:
  奖励值: 127.0128
  收益率: 0.2115
  距离: 31.0558
  内存使用: 0.5975
  能量使用: 0.9212
  推理时间: 2.2032秒

批次 98:
  奖励值: 118.8243
  收益率: 0.2005
  距离: 32.4314
  内存使用: 0.6188
  能量使用: 0.9010
  推理时间: 2.1421秒

批次 99:
  奖励值: 128.2849
  收益率: 0.2189
  距离: 34.4679
  内存使用: 0.6928
  能量使用: 1.0696
  推理时间: 2.2820秒

批次 100:
  奖励值: 120.3337
  收益率: 0.2022
  距离: 30.4551
  内存使用: 0.9106
  能量使用: 0.9669
  推理时间: 2.1260秒


==================== 总结 ====================
平均收益率: 0.2013
平均能量使用: 0.9264
平均推理时间: 2.1133秒
