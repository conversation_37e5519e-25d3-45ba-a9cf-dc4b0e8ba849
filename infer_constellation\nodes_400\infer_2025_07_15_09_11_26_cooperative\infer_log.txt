推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 95.2543
  收益率: 0.5817
  距离: 25.9915
  内存使用: 0.4907
  能量使用: 0.8092
  推理时间: 1.9602秒

批次 2:
  奖励值: 84.4525
  收益率: 0.5380
  距离: 24.1118
  内存使用: 0.4217
  能量使用: 0.7615
  推理时间: 1.8003秒

批次 3:
  奖励值: 83.6838
  收益率: 0.5502
  距离: 22.0783
  内存使用: 0.4406
  能量使用: 0.7136
  推理时间: 1.7135秒

批次 4:
  奖励值: 83.7360
  收益率: 0.5353
  距离: 24.2859
  内存使用: 0.3836
  能量使用: 0.6585
  推理时间: 1.6554秒

批次 5:
  奖励值: 86.0375
  收益率: 0.5377
  距离: 21.9262
  内存使用: 0.4357
  能量使用: 0.7631
  推理时间: 1.7900秒

批次 6:
  奖励值: 82.8466
  收益率: 0.4982
  距离: 22.6382
  内存使用: 0.3616
  能量使用: 0.7070
  推理时间: 1.6040秒

批次 7:
  奖励值: 85.9400
  收益率: 0.5516
  距离: 23.7461
  内存使用: 0.4807
  能量使用: 0.6934
  推理时间: 1.7599秒

批次 8:
  奖励值: 94.0915
  收益率: 0.5820
  距离: 26.2500
  内存使用: 0.5164
  能量使用: 0.8505
  推理时间: 1.8645秒

批次 9:
  奖励值: 81.8649
  收益率: 0.5248
  距离: 23.5957
  内存使用: 0.4022
  能量使用: 0.6724
  推理时间: 1.6027秒

批次 10:
  奖励值: 87.9081
  收益率: 0.5588
  距离: 27.3688
  内存使用: 0.4929
  能量使用: 0.7221
  推理时间: 1.7911秒

批次 11:
  奖励值: 98.9745
  收益率: 0.6038
  距离: 29.2146
  内存使用: 0.5994
  能量使用: 0.8370
  推理时间: 1.9643秒

批次 12:
  奖励值: 85.0651
  收益率: 0.5423
  距离: 27.3002
  内存使用: 0.4025
  能量使用: 0.7671
  推理时间: 1.8412秒

批次 13:
  奖励值: 85.0754
  收益率: 0.5388
  距离: 24.3437
  内存使用: 0.4569
  能量使用: 0.6488
  推理时间: 1.7699秒

批次 14:
  奖励值: 91.2142
  收益率: 0.5723
  距离: 25.3311
  内存使用: 0.4709
  能量使用: 0.7695
  推理时间: 1.7972秒

批次 15:
  奖励值: 81.7272
  收益率: 0.5296
  距离: 24.9023
  内存使用: 0.7179
  能量使用: 0.7254
  推理时间: 1.7051秒

批次 16:
  奖励值: 88.5759
  收益率: 0.5905
  距离: 27.2548
  内存使用: 0.4117
  能量使用: 0.7817
  推理时间: 1.8677秒

批次 17:
  奖励值: 87.5265
  收益率: 0.5395
  距离: 23.4279
  内存使用: 0.4060
  能量使用: 0.7131
  推理时间: 1.7784秒

批次 18:
  奖励值: 83.9979
  收益率: 0.5575
  距离: 23.6775
  内存使用: 0.4278
  能量使用: 0.6989
  推理时间: 1.7214秒

批次 19:
  奖励值: 82.1121
  收益率: 0.5089
  距离: 23.7451
  内存使用: 0.3823
  能量使用: 0.6485
  推理时间: 1.5720秒

批次 20:
  奖励值: 82.5852
  收益率: 0.5465
  距离: 22.8955
  内存使用: 0.3910
  能量使用: 0.7431
  推理时间: 1.6422秒

批次 21:
  奖励值: 83.8821
  收益率: 0.5424
  距离: 24.2736
  内存使用: 0.4357
  能量使用: 0.7266
  推理时间: 1.7226秒

批次 22:
  奖励值: 90.0020
  收益率: 0.5593
  距离: 25.2112
  内存使用: 0.4532
  能量使用: 0.7317
  推理时间: 1.8759秒

批次 23:
  奖励值: 88.5682
  收益率: 0.5399
  距离: 23.1413
  内存使用: 0.3948
  能量使用: 0.7274
  推理时间: 1.7102秒

批次 24:
  奖励值: 75.3191
  收益率: 0.4683
  距离: 18.4197
  内存使用: 0.3254
  能量使用: 0.6166
  推理时间: 1.4283秒

批次 25:
  奖励值: 95.3873
  收益率: 0.5942
  距离: 23.7542
  内存使用: 0.4682
  能量使用: 0.8031
  推理时间: 1.9466秒

批次 26:
  奖励值: 94.3769
  收益率: 0.5930
  距离: 24.7690
  内存使用: 0.5001
  能量使用: 0.7771
  推理时间: 1.8973秒

批次 27:
  奖励值: 83.2513
  收益率: 0.5019
  距离: 20.2647
  内存使用: 0.3835
  能量使用: 0.6492
  推理时间: 1.6607秒

批次 28:
  奖励值: 85.6194
  收益率: 0.5589
  距离: 23.0996
  内存使用: 0.4597
  能量使用: 0.8018
  推理时间: 1.8077秒

批次 29:
  奖励值: 85.8039
  收益率: 0.5508
  距离: 24.6418
  内存使用: 0.4278
  能量使用: 0.7226
  推理时间: 1.6722秒

批次 30:
  奖励值: 86.3597
  收益率: 0.5246
  距离: 24.3184
  内存使用: 0.4082
  能量使用: 0.7540
  推理时间: 1.8245秒

批次 31:
  奖励值: 92.4037
  收益率: 0.5886
  距离: 25.8398
  内存使用: 0.5456
  能量使用: 0.8071
  推理时间: 1.8994秒

批次 32:
  奖励值: 86.7842
  收益率: 0.5306
  距离: 21.5430
  内存使用: 0.3910
  能量使用: 0.6977
  推理时间: 1.7087秒

批次 33:
  奖励值: 77.8637
  收益率: 0.4978
  距离: 21.6471
  内存使用: 0.3435
  能量使用: 0.6820
  推理时间: 1.5592秒

批次 34:
  奖励值: 87.4201
  收益率: 0.5470
  距离: 22.0204
  内存使用: 0.4858
  能量使用: 0.6536
  推理时间: 1.7135秒

批次 35:
  奖励值: 67.0115
  收益率: 0.4227
  距离: 19.8761
  内存使用: 0.3259
  能量使用: 0.5750
  推理时间: 1.3559秒

批次 36:
  奖励值: 83.7944
  收益率: 0.5227
  距离: 20.3544
  内存使用: 0.3713
  能量使用: 0.6856
  推理时间: 1.6638秒

批次 37:
  奖励值: 85.1359
  收益率: 0.5564
  距离: 22.9780
  内存使用: 0.4084
  能量使用: 0.7419
  推理时间: 1.7838秒

批次 38:
  奖励值: 79.7240
  收益率: 0.4986
  距离: 21.5096
  内存使用: 0.3769
  能量使用: 0.6655
  推理时间: 1.6139秒

批次 39:
  奖励值: 82.6838
  收益率: 0.5388
  距离: 25.0437
  内存使用: 0.6949
  能量使用: 0.6938
  推理时间: 2.0811秒

批次 40:
  奖励值: 80.5419
  收益率: 0.5260
  距离: 24.0371
  内存使用: 0.4029
  能量使用: 0.6763
  推理时间: 1.6618秒

批次 41:
  奖励值: 81.3539
  收益率: 0.5238
  距离: 21.4554
  内存使用: 0.3419
  能量使用: 0.6949
  推理时间: 1.6334秒

批次 42:
  奖励值: 91.5847
  收益率: 0.5553
  距离: 25.4465
  内存使用: 0.4563
  能量使用: 0.7278
  推理时间: 1.8399秒

批次 43:
  奖励值: 86.8905
  收益率: 0.5290
  距离: 23.6838
  内存使用: 0.4405
  能量使用: 0.6946
  推理时间: 1.8102秒

批次 44:
  奖励值: 90.0119
  收益率: 0.5746
  距离: 25.1105
  内存使用: 0.4578
  能量使用: 0.8080
  推理时间: 1.8329秒

批次 45:
  奖励值: 78.4768
  收益率: 0.4992
  距离: 20.9689
  内存使用: 0.3454
  能量使用: 0.7321
  推理时间: 1.5830秒

批次 46:
  奖励值: 91.0021
  收益率: 0.5721
  距离: 26.2291
  内存使用: 0.5107
  能量使用: 0.8561
  推理时间: 1.8524秒

批次 47:
  奖励值: 80.7874
  收益率: 0.5125
  距离: 21.7921
  内存使用: 0.3774
  能量使用: 0.6557
  推理时间: 1.8387秒

批次 48:
  奖励值: 92.4284
  收益率: 0.5755
  距离: 25.8450
  内存使用: 0.5033
  能量使用: 0.7949
  推理时间: 1.8495秒

批次 49:
  奖励值: 85.3042
  收益率: 0.5631
  距离: 26.2564
  内存使用: 0.4316
  能量使用: 0.7036
  推理时间: 1.7883秒

批次 50:
  奖励值: 95.5607
  收益率: 0.5784
  距离: 22.9952
  内存使用: 0.5283
  能量使用: 0.7551
  推理时间: 1.8411秒

批次 51:
  奖励值: 88.6625
  收益率: 0.5485
  距离: 20.6927
  内存使用: 0.4401
  能量使用: 0.7422
  推理时间: 1.7555秒

批次 52:
  奖励值: 81.3863
  收益率: 0.5300
  距离: 21.7743
  内存使用: 0.3734
  能量使用: 0.6798
  推理时间: 1.6559秒

批次 53:
  奖励值: 80.8460
  收益率: 0.5330
  距离: 24.8857
  内存使用: 0.3736
  能量使用: 0.6488
  推理时间: 1.6552秒

批次 54:
  奖励值: 87.3549
  收益率: 0.5457
  距离: 26.7858
  内存使用: 0.5100
  能量使用: 0.7944
  推理时间: 1.7817秒

批次 55:
  奖励值: 94.4871
  收益率: 0.5672
  距离: 24.8995
  内存使用: 0.5055
  能量使用: 0.8209
  推理时间: 2.1378秒

批次 56:
  奖励值: 80.3952
  收益率: 0.5196
  距离: 24.1272
  内存使用: 0.3690
  能量使用: 0.7133
  推理时间: 1.5677秒

批次 57:
  奖励值: 87.6621
  收益率: 0.5649
  距离: 28.8729
  内存使用: 0.4623
  能量使用: 0.7404
  推理时间: 1.7730秒

批次 58:
  奖励值: 84.0330
  收益率: 0.5532
  距离: 25.1856
  内存使用: 0.4470
  能量使用: 0.7015
  推理时间: 1.7372秒

批次 59:
  奖励值: 81.7778
  收益率: 0.5348
  距离: 25.9371
  内存使用: 0.4273
  能量使用: 0.6800
  推理时间: 1.6610秒

批次 60:
  奖励值: 82.6501
  收益率: 0.5246
  距离: 23.6056
  内存使用: 0.3627
  能量使用: 0.7316
  推理时间: 1.6552秒

批次 61:
  奖励值: 86.5953
  收益率: 0.5413
  距离: 25.0345
  内存使用: 0.4184
  能量使用: 0.7565
  推理时间: 1.9988秒

批次 62:
  奖励值: 87.8133
  收益率: 0.5374
  距离: 23.0190
  内存使用: 0.4584
  能量使用: 0.6960
  推理时间: 1.8937秒

批次 63:
  奖励值: 88.8087
  收益率: 0.5403
  距离: 24.9697
  内存使用: 0.4730
  能量使用: 0.7399
  推理时间: 2.1358秒

批次 64:
  奖励值: 81.8872
  收益率: 0.5339
  距离: 22.6073
  内存使用: 0.3832
  能量使用: 0.6352
  推理时间: 1.6565秒

批次 65:
  奖励值: 82.1059
  收益率: 0.5338
  距离: 25.9267
  内存使用: 0.3636
  能量使用: 0.6978
  推理时间: 1.7154秒

批次 66:
  奖励值: 94.2079
  收益率: 0.5730
  距离: 24.7031
  内存使用: 0.4954
  能量使用: 0.7857
  推理时间: 2.1359秒

批次 67:
  奖励值: 103.2939
  收益率: 0.6051
  距离: 24.1560
  内存使用: 0.4605
  能量使用: 0.7958
  推理时间: 1.9478秒

批次 68:
  奖励值: 91.4682
  收益率: 0.5567
  距离: 23.8325
  内存使用: 0.4716
  能量使用: 0.7594
  推理时间: 1.8519秒

批次 69:
  奖励值: 81.7315
  收益率: 0.5336
  距离: 22.5009
  内存使用: 0.4375
  能量使用: 0.7986
  推理时间: 1.7263秒

批次 70:
  奖励值: 89.4251
  收益率: 0.5714
  距离: 24.7235
  内存使用: 0.4745
  能量使用: 0.7519
  推理时间: 1.8752秒

批次 71:
  奖励值: 93.2790
  收益率: 0.5714
  距离: 22.7453
  内存使用: 0.5189
  能量使用: 0.7778
  推理时间: 1.8524秒

批次 72:
  奖励值: 88.6160
  收益率: 0.5248
  距离: 22.7796
  内存使用: 0.4216
  能量使用: 0.7670
  推理时间: 1.7146秒

批次 73:
  奖励值: 83.4724
  收益率: 0.5068
  距离: 19.4188
  内存使用: 0.3635
  能量使用: 0.6729
  推理时间: 1.6285秒

批次 74:
  奖励值: 81.4245
  收益率: 0.5336
  距离: 21.5497
  内存使用: 0.4128
  能量使用: 0.7885
  推理时间: 1.9334秒

批次 75:
  奖励值: 84.2758
  收益率: 0.5171
  距离: 23.7550
  内存使用: 0.4155
  能量使用: 0.6780
  推理时间: 1.7044秒

批次 76:
  奖励值: 84.2727
  收益率: 0.5237
  距离: 22.9419
  内存使用: 0.4224
  能量使用: 0.6903
  推理时间: 1.7298秒

批次 77:
  奖励值: 83.9630
  收益率: 0.5184
  距离: 22.1807
  内存使用: 0.3782
  能量使用: 0.7152
  推理时间: 1.8780秒

批次 78:
  奖励值: 84.3200
  收益率: 0.5306
  距离: 22.7436
  内存使用: 0.3555
  能量使用: 0.6608
  推理时间: 1.6834秒

批次 79:
  奖励值: 87.4874
  收益率: 0.5476
  距离: 27.3577
  内存使用: 0.4849
  能量使用: 0.7827
  推理时间: 1.8412秒

批次 80:
  奖励值: 87.0064
  收益率: 0.5478
  距离: 23.0010
  内存使用: 0.4870
  能量使用: 0.6992
  推理时间: 1.7945秒

批次 81:
  奖励值: 82.8314
  收益率: 0.5246
  距离: 24.9884
  内存使用: 0.4661
  能量使用: 0.6826
  推理时间: 1.7350秒

批次 82:
  奖励值: 79.9413
  收益率: 0.5081
  距离: 20.2948
  内存使用: 0.4074
  能量使用: 0.6152
  推理时间: 1.6412秒

批次 83:
  奖励值: 91.7842
  收益率: 0.5738
  距离: 25.2296
  内存使用: 0.4401
  能量使用: 0.7619
  推理时间: 1.8130秒

批次 84:
  奖励值: 84.7235
  收益率: 0.5451
  距离: 21.4210
  内存使用: 0.3891
  能量使用: 0.7429
  推理时间: 1.7139秒

批次 85:
  奖励值: 58.4641
  收益率: 0.3726
  距离: 17.7737
  内存使用: 0.2088
  能量使用: 0.4894
  推理时间: 1.2280秒

批次 86:
  奖励值: 82.2992
  收益率: 0.5320
  距离: 23.7080
  内存使用: 0.3643
  能量使用: 0.7294
  推理时间: 1.7527秒

批次 87:
  奖励值: 77.7471
  收益率: 0.4757
  距离: 17.9608
  内存使用: 0.3092
  能量使用: 0.6359
  推理时间: 1.5071秒

批次 88:
  奖励值: 89.8352
  收益率: 0.5649
  距离: 26.2993
  内存使用: 0.4533
  能量使用: 0.7771
  推理时间: 1.8960秒

批次 89:
  奖励值: 90.5962
  收益率: 0.5581
  距离: 24.0254
  内存使用: 0.4991
  能量使用: 0.6831
  推理时间: 1.7726秒

批次 90:
  奖励值: 81.5306
  收益率: 0.5169
  距离: 25.4045
  内存使用: 0.3958
  能量使用: 0.7386
  推理时间: 1.7040秒

批次 91:
  奖励值: 73.4658
  收益率: 0.4836
  距离: 23.1912
  内存使用: 0.3730
  能量使用: 0.7042
  推理时间: 1.7811秒

批次 92:
  奖励值: 82.8024
  收益率: 0.5319
  距离: 21.7155
  内存使用: 0.4002
  能量使用: 0.6795
  推理时间: 1.6037秒

批次 93:
  奖励值: 81.6519
  收益率: 0.5162
  距离: 23.9567
  内存使用: 0.2969
  能量使用: 0.6426
  推理时间: 1.6617秒

批次 94:
  奖励值: 95.3280
  收益率: 0.5759
  距离: 24.1857
  内存使用: 0.5156
  能量使用: 0.8427
  推理时间: 2.3037秒

批次 95:
  奖励值: 85.6936
  收益率: 0.5316
  距离: 24.1269
  内存使用: 0.3819
  能量使用: 0.7041
  推理时间: 1.7273秒

批次 96:
  奖励值: 81.6074
  收益率: 0.5251
  距离: 23.7916
  内存使用: 0.3832
  能量使用: 0.6105
  推理时间: 1.6077秒

批次 97:
  奖励值: 85.9539
  收益率: 0.5280
  距离: 23.3140
  内存使用: 0.4634
  能量使用: 0.7148
  推理时间: 1.7673秒

批次 98:
  奖励值: 91.4361
  收益率: 0.5812
  距离: 25.7446
  内存使用: 0.4443
  能量使用: 0.7389
  推理时间: 1.8554秒

批次 99:
  奖励值: 86.7114
  收益率: 0.5710
  距离: 25.5050
  内存使用: 0.5147
  能量使用: 0.7576
  推理时间: 1.8289秒

批次 100:
  奖励值: 93.9368
  收益率: 0.5838
  距离: 25.3819
  内存使用: 0.5252
  能量使用: 0.8193
  推理时间: 2.0352秒


==================== 总结 ====================
平均收益率: 0.5400
平均能量使用: 0.7227
平均推理时间: 1.7667秒
