推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 69.6471
  收益率: 0.5598
  距离: 18.1529
  内存使用: 0.2076
  能量使用: 0.5452
  推理时间: 1.3241秒

批次 2:
  奖励值: 61.8599
  收益率: 0.5206
  距离: 17.1231
  内存使用: 0.2319
  能量使用: 0.5252
  推理时间: 1.2272秒

批次 3:
  奖励值: 64.9350
  收益率: 0.5350
  距离: 16.1739
  内存使用: 0.2239
  能量使用: 0.5321
  推理时间: 1.2323秒

批次 4:
  奖励值: 64.7515
  收益率: 0.5350
  距离: 16.3072
  内存使用: 0.2239
  能量使用: 0.5660
  推理时间: 1.2303秒

批次 5:
  奖励值: 65.0318
  收益率: 0.5119
  距离: 19.5485
  内存使用: 0.2403
  能量使用: 0.5476
  推理时间: 1.2181秒

批次 6:
  奖励值: 72.0500
  收益率: 0.5761
  距离: 17.0651
  内存使用: 0.2407
  能量使用: 0.5943
  推理时间: 1.3392秒

批次 7:
  奖励值: 69.0884
  收益率: 0.5670
  距离: 19.9372
  内存使用: 0.2709
  能量使用: 0.6152
  推理时间: 1.3437秒

批次 8:
  奖励值: 64.3492
  收益率: 0.5451
  距离: 18.1571
  内存使用: 0.2412
  能量使用: 0.5033
  推理时间: 1.2291秒

批次 9:
  奖励值: 67.2902
  收益率: 0.5418
  距离: 16.4642
  内存使用: 0.2219
  能量使用: 0.5335
  推理时间: 1.9382秒

批次 10:
  奖励值: 58.1811
  收益率: 0.4911
  距离: 14.7832
  内存使用: 0.2649
  能量使用: 0.3896
  推理时间: 1.1005秒

批次 11:
  奖励值: 68.9382
  收益率: 0.5483
  距离: 16.8823
  内存使用: 0.2377
  能量使用: 0.6035
  推理时间: 1.2992秒

批次 12:
  奖励值: 64.2632
  收益率: 0.5259
  距离: 14.8793
  内存使用: 0.2460
  能量使用: 0.5484
  推理时间: 1.1724秒

批次 13:
  奖励值: 62.6232
  收益率: 0.5489
  距离: 18.8090
  内存使用: 0.2166
  能量使用: 0.5279
  推理时间: 1.2092秒

批次 14:
  奖励值: 64.6452
  收益率: 0.5317
  距离: 15.9848
  内存使用: 0.2452
  能量使用: 0.4796
  推理时间: 1.2500秒

批次 15:
  奖励值: 67.0476
  收益率: 0.5406
  距离: 16.8670
  内存使用: 0.3116
  能量使用: 0.5261
  推理时间: 1.2780秒

批次 16:
  奖励值: 64.6415
  收益率: 0.5389
  距离: 16.7737
  内存使用: 0.1889
  能量使用: 0.5544
  推理时间: 1.2544秒

批次 17:
  奖励值: 63.7068
  收益率: 0.5275
  距离: 15.8562
  内存使用: 0.2454
  能量使用: 0.4849
  推理时间: 1.1946秒

批次 18:
  奖励值: 64.9484
  收益率: 0.5726
  距离: 20.6611
  内存使用: 0.2413
  能量使用: 0.6009
  推理时间: 1.2946秒

批次 19:
  奖励值: 70.0935
  收益率: 0.5695
  距离: 16.3686
  内存使用: 0.2658
  能量使用: 0.5786
  推理时间: 1.3138秒

批次 20:
  奖励值: 55.3716
  收益率: 0.4927
  距离: 17.1773
  内存使用: 0.1889
  能量使用: 0.4907
  推理时间: 1.0880秒

批次 21:
  奖励值: 64.6108
  收益率: 0.5424
  距离: 19.5622
  内存使用: 0.2559
  能量使用: 0.5603
  推理时间: 1.2599秒

批次 22:
  奖励值: 58.9267
  收益率: 0.4967
  距离: 15.0798
  内存使用: 0.2036
  能量使用: 0.4631
  推理时间: 1.0967秒

批次 23:
  奖励值: 65.2936
  收益率: 0.5748
  距离: 21.5748
  内存使用: 0.2335
  能量使用: 0.6045
  推理时间: 1.4382秒

批次 24:
  奖励值: 65.6089
  收益率: 0.5258
  距离: 18.8027
  内存使用: 0.2307
  能量使用: 0.5488
  推理时间: 1.2492秒

批次 25:
  奖励值: 56.5541
  收益率: 0.4932
  距离: 16.3166
  内存使用: 0.2621
  能量使用: 0.5131
  推理时间: 1.2057秒

批次 26:
  奖励值: 59.2503
  收益率: 0.4945
  距离: 16.1736
  内存使用: 0.2796
  能量使用: 0.4462
  推理时间: 1.2369秒

批次 27:
  奖励值: 64.4680
  收益率: 0.5007
  距离: 13.6663
  内存使用: 0.2551
  能量使用: 0.5426
  推理时间: 1.2094秒

批次 28:
  奖励值: 65.2375
  收益率: 0.5228
  距离: 15.7776
  内存使用: 0.2147
  能量使用: 0.5327
  推理时间: 1.2650秒

批次 29:
  奖励值: 77.9720
  收益率: 0.5779
  距离: 19.7147
  内存使用: 0.3328
  能量使用: 0.6086
  推理时间: 1.5177秒

批次 30:
  奖励值: 69.6261
  收益率: 0.5647
  距离: 16.1372
  内存使用: 0.2880
  能量使用: 0.5224
  推理时间: 1.3210秒

批次 31:
  奖励值: 62.2464
  收益率: 0.5178
  距离: 16.9989
  内存使用: 0.1939
  能量使用: 0.5144
  推理时间: 1.2930秒

批次 32:
  奖励值: 71.2558
  收益率: 0.5648
  距离: 17.7968
  内存使用: 0.2930
  能量使用: 0.5986
  推理时间: 1.3907秒

批次 33:
  奖励值: 57.5325
  收益率: 0.4970
  距离: 15.7402
  内存使用: 0.1679
  能量使用: 0.4268
  推理时间: 1.1074秒

批次 34:
  奖励值: 63.4645
  收益率: 0.5569
  距离: 16.0796
  内存使用: 0.2543
  能量使用: 0.5370
  推理时间: 1.2021秒

批次 35:
  奖励值: 64.4314
  收益率: 0.5353
  距离: 17.6899
  内存使用: 0.2112
  能量使用: 0.4361
  推理时间: 1.1973秒

批次 36:
  奖励值: 64.3649
  收益率: 0.5350
  距离: 17.2313
  内存使用: 0.2663
  能量使用: 0.5726
  推理时间: 1.2340秒

批次 37:
  奖励值: 62.0238
  收益率: 0.5207
  距离: 18.7679
  内存使用: 0.2867
  能量使用: 0.4977
  推理时间: 1.2414秒

批次 38:
  奖励值: 66.4533
  收益率: 0.5501
  距离: 18.8607
  内存使用: 0.2888
  能量使用: 0.5439
  推理时间: 1.3382秒

批次 39:
  奖励值: 55.1102
  收益率: 0.4889
  距离: 16.7486
  内存使用: 0.1816
  能量使用: 0.4494
  推理时间: 1.1257秒

批次 40:
  奖励值: 70.4301
  收益率: 0.5636
  距离: 18.0457
  内存使用: 0.1953
  能量使用: 0.4987
  推理时间: 1.3211秒

批次 41:
  奖励值: 62.5037
  收益率: 0.5411
  距离: 17.6934
  内存使用: 0.1669
  能量使用: 0.5322
  推理时间: 1.2112秒

批次 42:
  奖励值: 66.6748
  收益率: 0.5560
  距离: 20.6548
  内存使用: 0.2659
  能量使用: 0.5298
  推理时间: 1.3307秒

批次 43:
  奖励值: 60.0642
  收益率: 0.4996
  距离: 18.0138
  内存使用: 0.1947
  能量使用: 0.4631
  推理时间: 1.1595秒

批次 44:
  奖励值: 65.2796
  收益率: 0.5543
  距离: 17.6761
  内存使用: 0.2093
  能量使用: 0.5554
  推理时间: 1.2692秒

批次 45:
  奖励值: 66.6198
  收益率: 0.5647
  距离: 17.7465
  内存使用: 0.2839
  能量使用: 0.5300
  推理时间: 1.3659秒

批次 46:
  奖励值: 67.5322
  收益率: 0.5592
  距离: 19.8037
  内存使用: 0.3007
  能量使用: 0.6312
  推理时间: 1.3861秒

批次 47:
  奖励值: 70.9326
  收益率: 0.5717
  距离: 19.0031
  内存使用: 0.3244
  能量使用: 0.6072
  推理时间: 1.3592秒

批次 48:
  奖励值: 61.2675
  收益率: 0.4982
  距离: 15.0735
  内存使用: 0.5152
  能量使用: 0.4763
  推理时间: 1.1729秒

批次 49:
  奖励值: 64.1051
  收益率: 0.5448
  距离: 19.4456
  内存使用: 0.2031
  能量使用: 0.5411
  推理时间: 1.2573秒

批次 50:
  奖励值: 65.4423
  收益率: 0.5482
  距离: 17.3698
  内存使用: 0.2437
  能量使用: 0.5018
  推理时间: 1.2528秒

批次 51:
  奖励值: 64.8887
  收益率: 0.5435
  距离: 18.2639
  内存使用: 0.2321
  能量使用: 0.5266
  推理时间: 1.2306秒

批次 52:
  奖励值: 63.9833
  收益率: 0.5361
  距离: 16.6372
  内存使用: 0.2935
  能量使用: 0.5027
  推理时间: 1.3935秒

批次 53:
  奖励值: 61.0469
  收益率: 0.5128
  距离: 17.6024
  内存使用: 0.2264
  能量使用: 0.4774
  推理时间: 1.1905秒

批次 54:
  奖励值: 57.9334
  收益率: 0.5179
  距离: 19.5910
  内存使用: 0.1875
  能量使用: 0.5546
  推理时间: 1.2385秒

批次 55:
  奖励值: 56.4353
  收益率: 0.4807
  距离: 16.1943
  内存使用: 0.1263
  能量使用: 0.4089
  推理时间: 1.0833秒

批次 56:
  奖励值: 60.2845
  收益率: 0.5237
  距离: 17.7470
  内存使用: 0.2178
  能量使用: 0.4662
  推理时间: 1.2427秒

批次 57:
  奖励值: 69.3396
  收益率: 0.5391
  距离: 16.3066
  内存使用: 0.2869
  能量使用: 0.5273
  推理时间: 1.2994秒

批次 58:
  奖励值: 72.5866
  收益率: 0.5912
  距离: 20.5037
  内存使用: 0.3220
  能量使用: 0.6011
  推理时间: 1.4431秒

批次 59:
  奖励值: 68.9518
  收益率: 0.5814
  距离: 21.1723
  内存使用: 0.3202
  能量使用: 0.6039
  推理时间: 1.3546秒

批次 60:
  奖励值: 59.8070
  收益率: 0.4996
  距离: 16.4497
  内存使用: 0.2432
  能量使用: 0.4931
  推理时间: 1.2796秒

批次 61:
  奖励值: 62.3933
  收益率: 0.5340
  距离: 14.2299
  内存使用: 0.2212
  能量使用: 0.5624
  推理时间: 1.2263秒

批次 62:
  奖励值: 62.1547
  收益率: 0.5170
  距离: 15.1333
  内存使用: 0.2288
  能量使用: 0.5286
  推理时间: 1.1307秒

批次 63:
  奖励值: 63.1589
  收益率: 0.5295
  距离: 14.7266
  内存使用: 0.2291
  能量使用: 0.5265
  推理时间: 1.2534秒

批次 64:
  奖励值: 64.7603
  收益率: 0.5363
  距离: 16.0872
  内存使用: 0.2584
  能量使用: 0.5339
  推理时间: 1.2300秒

批次 65:
  奖励值: 67.4972
  收益率: 0.5431
  距离: 16.7831
  内存使用: 0.2771
  能量使用: 0.5455
  推理时间: 1.2305秒

批次 66:
  奖励值: 62.5108
  收益率: 0.4951
  距离: 15.2884
  内存使用: 0.2134
  能量使用: 0.4766
  推理时间: 1.1550秒

批次 67:
  奖励值: 56.8547
  收益率: 0.4989
  距离: 18.1797
  内存使用: 0.2337
  能量使用: 0.4773
  推理时间: 1.1362秒

批次 68:
  奖励值: 66.0737
  收益率: 0.5288
  距离: 17.6358
  内存使用: 0.2658
  能量使用: 0.5511
  推理时间: 1.2731秒

批次 69:
  奖励值: 68.1073
  收益率: 0.5567
  距离: 18.1043
  内存使用: 0.3165
  能量使用: 0.5646
  推理时间: 1.3230秒

批次 70:
  奖励值: 64.9787
  收益率: 0.5434
  距离: 15.8357
  内存使用: 0.2655
  能量使用: 0.5413
  推理时间: 1.2329秒

批次 71:
  奖励值: 67.7444
  收益率: 0.5615
  距离: 17.0416
  内存使用: 0.3037
  能量使用: 0.5659
  推理时间: 1.2839秒

批次 72:
  奖励值: 64.1605
  收益率: 0.5239
  距离: 15.5539
  内存使用: 0.2387
  能量使用: 0.4865
  推理时间: 1.2756秒

批次 73:
  奖励值: 64.6133
  收益率: 0.5333
  距离: 15.8913
  内存使用: 0.1861
  能量使用: 0.4960
  推理时间: 1.1701秒

批次 74:
  奖励值: 60.6989
  收益率: 0.5102
  距离: 17.8533
  内存使用: 0.2133
  能量使用: 0.5356
  推理时间: 1.1372秒

批次 75:
  奖励值: 59.1439
  收益率: 0.5197
  距离: 15.8798
  内存使用: 0.1726
  能量使用: 0.4597
  推理时间: 1.1164秒

批次 76:
  奖励值: 65.0935
  收益率: 0.5295
  距离: 16.2300
  内存使用: 0.2588
  能量使用: 0.5242
  推理时间: 1.3454秒

批次 77:
  奖励值: 59.6404
  收益率: 0.4920
  距离: 15.6364
  内存使用: 0.2090
  能量使用: 0.4738
  推理时间: 1.0806秒

批次 78:
  奖励值: 73.0363
  收益率: 0.5917
  距离: 18.8142
  内存使用: 0.3498
  能量使用: 0.5633
  推理时间: 1.3785秒

批次 79:
  奖励值: 60.4862
  收益率: 0.5130
  距离: 15.8544
  内存使用: 0.2497
  能量使用: 0.5235
  推理时间: 1.1954秒

批次 80:
  奖励值: 70.6743
  收益率: 0.5678
  距离: 18.6264
  内存使用: 0.2707
  能量使用: 0.5543
  推理时间: 1.4046秒

批次 81:
  奖励值: 59.2481
  收益率: 0.5069
  距离: 13.8008
  内存使用: 0.1783
  能量使用: 0.5247
  推理时间: 1.1714秒

批次 82:
  奖励值: 62.8118
  收益率: 0.4993
  距离: 16.2647
  内存使用: 0.1943
  能量使用: 0.5174
  推理时间: 1.2424秒

批次 83:
  奖励值: 72.9024
  收益率: 0.5771
  距离: 18.0111
  内存使用: 0.2708
  能量使用: 0.5133
  推理时间: 1.3705秒

批次 84:
  奖励值: 63.9455
  收益率: 0.5367
  距离: 17.5815
  内存使用: 0.2240
  能量使用: 0.5035
  推理时间: 1.2628秒

批次 85:
  奖励值: 65.6429
  收益率: 0.5574
  距离: 19.3565
  内存使用: 0.2572
  能量使用: 0.5005
  推理时间: 1.2936秒

批次 86:
  奖励值: 62.6991
  收益率: 0.5438
  距离: 19.2073
  内存使用: 0.2689
  能量使用: 0.5256
  推理时间: 1.2415秒

批次 87:
  奖励值: 64.8755
  收益率: 0.5313
  距离: 17.0649
  内存使用: 0.2863
  能量使用: 0.4978
  推理时间: 1.2902秒

批次 88:
  奖励值: 66.8055
  收益率: 0.5492
  距离: 18.6195
  内存使用: 0.2539
  能量使用: 0.5156
  推理时间: 1.3148秒

批次 89:
  奖励值: 59.0209
  收益率: 0.4902
  距离: 16.9120
  内存使用: 0.1694
  能量使用: 0.4769
  推理时间: 1.0986秒

批次 90:
  奖励值: 72.3760
  收益率: 0.5811
  距离: 19.1578
  内存使用: 0.3383
  能量使用: 0.5521
  推理时间: 1.3940秒

批次 91:
  奖励值: 61.2732
  收益率: 0.5033
  距离: 14.7988
  内存使用: 0.1738
  能量使用: 0.4810
  推理时间: 1.1507秒

批次 92:
  奖励值: 60.7836
  收益率: 0.5177
  距离: 17.8845
  内存使用: 0.2158
  能量使用: 0.5324
  推理时间: 1.1920秒

批次 93:
  奖励值: 63.5342
  收益率: 0.5308
  距离: 16.9855
  内存使用: 0.2435
  能量使用: 0.5026
  推理时间: 1.2725秒

批次 94:
  奖励值: 63.2425
  收益率: 0.5315
  距离: 19.4012
  内存使用: 0.2207
  能量使用: 0.4930
  推理时间: 1.2715秒

批次 95:
  奖励值: 63.6998
  收益率: 0.5678
  距离: 19.1048
  内存使用: 0.2382
  能量使用: 0.6575
  推理时间: 1.3467秒

批次 96:
  奖励值: 67.5872
  收益率: 0.5433
  距离: 15.8880
  内存使用: 0.2272
  能量使用: 0.5104
  推理时间: 1.2571秒

批次 97:
  奖励值: 59.2991
  收益率: 0.5022
  距离: 18.5555
  内存使用: 0.1738
  能量使用: 0.4725
  推理时间: 1.1479秒

批次 98:
  奖励值: 69.5666
  收益率: 0.5768
  距离: 18.4608
  内存使用: 0.2693
  能量使用: 0.5366
  推理时间: 1.3382秒

批次 99:
  奖励值: 70.0616
  收益率: 0.5828
  距离: 16.7525
  内存使用: 0.1957
  能量使用: 0.5285
  推理时间: 1.3666秒

批次 100:
  奖励值: 65.1359
  收益率: 0.5389
  距离: 15.8096
  内存使用: 0.2316
  能量使用: 0.4870
  推理时间: 1.2632秒


==================== 总结 ====================
平均收益率: 0.5356
平均能量使用: 0.5251
平均推理时间: 1.2617秒
