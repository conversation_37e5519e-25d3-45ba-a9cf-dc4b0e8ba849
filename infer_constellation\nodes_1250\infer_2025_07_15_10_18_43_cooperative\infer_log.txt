推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 111.8528
  收益率: 0.2320
  距离: 30.7351
  内存使用: 0.6139
  能量使用: 0.9454
  推理时间: 2.0606秒

批次 2:
  奖励值: 104.1607
  收益率: 0.2086
  距离: 27.7054
  内存使用: 0.5077
  能量使用: 0.8654
  推理时间: 1.8485秒

批次 3:
  奖励值: 110.4627
  收益率: 0.2211
  距离: 28.3869
  内存使用: 0.6005
  能量使用: 0.8872
  推理时间: 1.9391秒

批次 4:
  奖励值: 107.7926
  收益率: 0.2147
  距离: 25.6554
  内存使用: 0.9064
  能量使用: 0.8530
  推理时间: 1.9371秒

批次 5:
  奖励值: 135.5747
  收益率: 0.2674
  距离: 33.2658
  内存使用: 0.7838
  能量使用: 1.0010
  推理时间: 2.3030秒

批次 6:
  奖励值: 125.3049
  收益率: 0.2501
  距离: 33.6209
  内存使用: 0.7142
  能量使用: 1.0551
  推理时间: 2.2559秒

批次 7:
  奖励值: 126.2105
  收益率: 0.2533
  距离: 31.6956
  内存使用: 0.6874
  能量使用: 0.9960
  推理时间: 2.2021秒

批次 8:
  奖励值: 127.9091
  收益率: 0.2549
  距离: 30.6110
  内存使用: 0.6869
  能量使用: 1.0837
  推理时间: 2.3431秒

批次 9:
  奖励值: 129.6774
  收益率: 0.2632
  距离: 37.1068
  内存使用: 0.7490
  能量使用: 0.9893
  推理时间: 2.3517秒

批次 10:
  奖励值: 125.1160
  收益率: 0.2500
  距离: 31.0372
  内存使用: 0.6900
  能量使用: 1.0407
  推理时间: 2.1550秒

批次 11:
  奖励值: 107.2724
  收益率: 0.2193
  距离: 29.9007
  内存使用: 0.9071
  能量使用: 0.8475
  推理时间: 1.9828秒

批次 12:
  奖励值: 112.2460
  收益率: 0.2237
  距离: 32.6449
  内存使用: 0.9132
  能量使用: 0.9497
  推理时间: 2.0264秒

批次 13:
  奖励值: 115.4978
  收益率: 0.2359
  距离: 31.1841
  内存使用: 0.6450
  能量使用: 0.9753
  推理时间: 2.0982秒

批次 14:
  奖励值: 126.9398
  收益率: 0.2545
  距离: 35.2630
  内存使用: 0.7420
  能量使用: 0.9154
  推理时间: 2.2607秒

批次 15:
  奖励值: 107.5703
  收益率: 0.2157
  距离: 26.9456
  内存使用: 0.9124
  能量使用: 0.8015
  推理时间: 1.9377秒

批次 16:
  奖励值: 116.9940
  收益率: 0.2368
  距离: 29.8472
  内存使用: 0.6472
  能量使用: 0.9176
  推理时间: 2.0782秒

批次 17:
  奖励值: 117.6542
  收益率: 0.2367
  距离: 26.8013
  内存使用: 0.5538
  能量使用: 0.9365
  推理时间: 2.0830秒

批次 18:
  奖励值: 108.5065
  收益率: 0.2164
  距离: 28.8906
  内存使用: 0.6638
  能量使用: 1.0325
  推理时间: 2.0113秒

批次 19:
  奖励值: 125.8043
  收益率: 0.2440
  距离: 32.0974
  内存使用: 0.6996
  能量使用: 0.9944
  推理时间: 2.2523秒

批次 20:
  奖励值: 119.9971
  收益率: 0.2431
  距离: 35.6318
  内存使用: 0.9075
  能量使用: 1.0664
  推理时间: 2.2438秒

批次 21:
  奖励值: 127.4287
  收益率: 0.2591
  距离: 35.4197
  内存使用: 0.7161
  能量使用: 1.0067
  推理时间: 2.2961秒

批次 22:
  奖励值: 124.6360
  收益率: 0.2447
  距离: 31.0848
  内存使用: 0.7245
  能量使用: 0.9187
  推理时间: 2.2185秒

批次 23:
  奖励值: 134.1772
  收益率: 0.2706
  距离: 34.0138
  内存使用: 0.7775
  能量使用: 1.0542
  推理时间: 2.4138秒

批次 24:
  奖励值: 102.3752
  收益率: 0.2063
  距离: 28.4920
  内存使用: 0.5372
  能量使用: 0.8204
  推理时间: 1.8117秒

批次 25:
  奖励值: 117.6009
  收益率: 0.2369
  距离: 29.9883
  内存使用: 0.6830
  能量使用: 0.9268
  推理时间: 2.1276秒

批次 26:
  奖励值: 123.1561
  收益率: 0.2499
  距离: 33.4301
  内存使用: 0.7006
  能量使用: 0.9571
  推理时间: 2.2086秒

批次 27:
  奖励值: 129.8371
  收益率: 0.2620
  距离: 33.7362
  内存使用: 0.7334
  能量使用: 1.0561
  推理时间: 2.2726秒

批次 28:
  奖励值: 119.0195
  收益率: 0.2324
  距离: 30.0952
  内存使用: 0.6204
  能量使用: 0.8453
  推理时间: 2.0944秒

批次 29:
  奖励值: 114.2848
  收益率: 0.2303
  距离: 30.6866
  内存使用: 0.8934
  能量使用: 0.8503
  推理时间: 2.0581秒

批次 30:
  奖励值: 127.9189
  收益率: 0.2536
  距离: 30.7891
  内存使用: 0.7324
  能量使用: 1.0406
  推理时间: 2.2969秒

批次 31:
  奖励值: 136.6181
  收益率: 0.2793
  距离: 34.4095
  内存使用: 0.6944
  能量使用: 1.0598
  推理时间: 2.4188秒

批次 32:
  奖励值: 112.5452
  收益率: 0.2276
  距离: 31.7030
  内存使用: 0.9000
  能量使用: 0.9066
  推理时间: 2.0446秒

批次 33:
  奖励值: 116.5484
  收益率: 0.2344
  距离: 31.9695
  内存使用: 0.9101
  能量使用: 1.0236
  推理时间: 2.1550秒

批次 34:
  奖励值: 124.8324
  收益率: 0.2550
  距离: 34.0034
  内存使用: 0.7108
  能量使用: 1.0210
  推理时间: 2.2681秒

批次 35:
  奖励值: 131.7753
  收益率: 0.2638
  距离: 33.2892
  内存使用: 0.7107
  能量使用: 1.0495
  推理时间: 2.3237秒

批次 36:
  奖励值: 132.5422
  收益率: 0.2705
  距离: 33.5108
  内存使用: 0.7500
  能量使用: 1.0547
  推理时间: 2.3998秒

批次 37:
  奖励值: 109.5960
  收益率: 0.2189
  距离: 26.7288
  内存使用: 0.5861
  能量使用: 0.8931
  推理时间: 1.9614秒

批次 38:
  奖励值: 130.4369
  收益率: 0.2594
  距离: 35.4332
  内存使用: 0.6800
  能量使用: 1.0129
  推理时间: 2.3430秒

批次 39:
  奖励值: 127.9026
  收益率: 0.2536
  距离: 29.6090
  内存使用: 0.6643
  能量使用: 0.9215
  推理时间: 2.1874秒

批次 40:
  奖励值: 113.3254
  收益率: 0.2275
  距离: 27.6947
  内存使用: 0.9112
  能量使用: 0.9432
  推理时间: 2.1020秒

批次 41:
  奖励值: 109.4985
  收益率: 0.2212
  距离: 28.4052
  内存使用: 0.9083
  能量使用: 0.8894
  推理时间: 2.0105秒

批次 42:
  奖励值: 115.0445
  收益率: 0.2286
  距离: 30.5221
  内存使用: 0.6221
  能量使用: 0.9668
  推理时间: 2.0199秒

批次 43:
  奖励值: 113.2903
  收益率: 0.2318
  距离: 32.2304
  内存使用: 0.9034
  能量使用: 0.8855
  推理时间: 2.0612秒

批次 44:
  奖励值: 135.7927
  收益率: 0.2722
  距离: 34.2002
  内存使用: 0.7451
  能量使用: 1.0692
  推理时间: 2.4161秒

批次 45:
  奖励值: 130.5782
  收益率: 0.2672
  距离: 35.0125
  内存使用: 0.7584
  能量使用: 1.0727
  推理时间: 2.3277秒

批次 46:
  奖励值: 122.9646
  收益率: 0.2478
  距离: 30.4210
  内存使用: 0.6739
  能量使用: 0.9759
  推理时间: 2.3071秒

批次 47:
  奖励值: 111.2480
  收益率: 0.2208
  距离: 28.3330
  内存使用: 0.9136
  能量使用: 0.8573
  推理时间: 1.9439秒

批次 48:
  奖励值: 120.6159
  收益率: 0.2453
  距离: 30.5933
  内存使用: 0.6626
  能量使用: 0.9124
  推理时间: 2.0875秒

批次 49:
  奖励值: 114.2987
  收益率: 0.2321
  距离: 30.5044
  内存使用: 0.9126
  能量使用: 0.9856
  推理时间: 2.1306秒

批次 50:
  奖励值: 119.0480
  收益率: 0.2413
  距离: 28.7816
  内存使用: 0.6365
  能量使用: 0.8859
  推理时间: 2.1368秒

批次 51:
  奖励值: 129.3655
  收益率: 0.2621
  距离: 34.9484
  内存使用: 0.6740
  能量使用: 1.0465
  推理时间: 2.3258秒

批次 52:
  奖励值: 112.2875
  收益率: 0.2269
  距离: 28.1020
  内存使用: 0.6192
  能量使用: 0.9038
  推理时间: 1.9669秒

批次 53:
  奖励值: 122.0307
  收益率: 0.2447
  距离: 31.8310
  内存使用: 0.7486
  能量使用: 0.9589
  推理时间: 2.1702秒

批次 54:
  奖励值: 121.9620
  收益率: 0.2530
  距离: 31.8934
  内存使用: 0.6542
  能量使用: 0.9284
  推理时间: 2.2016秒

批次 55:
  奖励值: 133.2362
  收益率: 0.2661
  距离: 33.2337
  内存使用: 0.7404
  能量使用: 0.9535
  推理时间: 2.2934秒

批次 56:
  奖励值: 119.4006
  收益率: 0.2384
  距离: 28.3969
  内存使用: 0.6678
  能量使用: 0.9341
  推理时间: 2.0664秒

批次 57:
  奖励值: 120.9405
  收益率: 0.2400
  距离: 30.5827
  内存使用: 0.6777
  能量使用: 1.0585
  推理时间: 2.0853秒

批次 58:
  奖励值: 118.7205
  收益率: 0.2444
  距离: 32.5325
  内存使用: 0.6381
  能量使用: 0.9884
  推理时间: 2.0947秒

批次 59:
  奖励值: 121.5686
  收益率: 0.2386
  距离: 32.2052
  内存使用: 0.6108
  能量使用: 1.0468
  推理时间: 2.1351秒

批次 60:
  奖励值: 106.0475
  收益率: 0.2186
  距离: 26.3540
  内存使用: 0.9127
  能量使用: 0.8384
  推理时间: 1.9023秒

批次 61:
  奖励值: 126.3118
  收益率: 0.2433
  距离: 28.8139
  内存使用: 0.6332
  能量使用: 0.9293
  推理时间: 2.1938秒

批次 62:
  奖励值: 123.7683
  收益率: 0.2482
  距离: 31.7759
  内存使用: 0.7013
  能量使用: 0.9861
  推理时间: 2.2273秒

批次 63:
  奖励值: 137.3446
  收益率: 0.2791
  距离: 38.0200
  内存使用: 0.7878
  能量使用: 1.1029
  推理时间: 2.4658秒

批次 64:
  奖励值: 125.8805
  收益率: 0.2483
  距离: 31.6839
  内存使用: 0.6422
  能量使用: 0.9130
  推理时间: 2.2142秒

批次 65:
  奖励值: 110.2717
  收益率: 0.2172
  距离: 25.6742
  内存使用: 0.5473
  能量使用: 0.8351
  推理时间: 1.9592秒

批次 66:
  奖励值: 112.3768
  收益率: 0.2313
  距离: 29.2212
  内存使用: 0.9054
  能量使用: 0.9196
  推理时间: 2.0278秒

批次 67:
  奖励值: 117.3890
  收益率: 0.2357
  距离: 32.0574
  内存使用: 0.6323
  能量使用: 0.9323
  推理时间: 2.0665秒

批次 68:
  奖励值: 127.6564
  收益率: 0.2621
  距离: 30.7638
  内存使用: 0.6828
  能量使用: 1.0407
  推理时间: 2.2561秒

批次 69:
  奖励值: 130.5350
  收益率: 0.2660
  距离: 35.6725
  内存使用: 0.6947
  能量使用: 0.9628
  推理时间: 2.3212秒

批次 70:
  奖励值: 124.4423
  收益率: 0.2447
  距离: 33.7347
  内存使用: 0.6122
  能量使用: 0.9685
  推理时间: 2.1790秒

批次 71:
  奖励值: 133.5109
  收益率: 0.2706
  距离: 33.8610
  内存使用: 0.7304
  能量使用: 0.9869
  推理时间: 2.4154秒

批次 72:
  奖励值: 125.4365
  收益率: 0.2512
  距离: 31.2324
  内存使用: 0.6868
  能量使用: 1.0078
  推理时间: 2.2623秒

批次 73:
  奖励值: 118.5010
  收益率: 0.2322
  距离: 26.8649
  内存使用: 0.6286
  能量使用: 0.9046
  推理时间: 2.0073秒

批次 74:
  奖励值: 142.1089
  收益率: 0.2878
  距离: 36.3473
  内存使用: 0.7452
  能量使用: 1.0896
  推理时间: 2.5063秒

批次 75:
  奖励值: 114.6794
  收益率: 0.2269
  距离: 28.0549
  内存使用: 0.6540
  能量使用: 0.9323
  推理时间: 2.0297秒

批次 76:
  奖励值: 125.2457
  收益率: 0.2509
  距离: 29.8906
  内存使用: 0.6777
  能量使用: 0.9404
  推理时间: 2.2523秒

批次 77:
  奖励值: 130.7531
  收益率: 0.2703
  距离: 34.9255
  内存使用: 0.7529
  能量使用: 1.0154
  推理时间: 2.2694秒

批次 78:
  奖励值: 127.0350
  收益率: 0.2560
  距离: 32.6118
  内存使用: 0.6894
  能量使用: 1.0286
  推理时间: 2.1910秒

批次 79:
  奖励值: 109.1222
  收益率: 0.2160
  距离: 27.0992
  内存使用: 0.6226
  能量使用: 0.8338
  推理时间: 1.8924秒

批次 80:
  奖励值: 125.2133
  收益率: 0.2555
  距离: 34.9517
  内存使用: 0.7679
  能量使用: 1.0538
  推理时间: 2.2445秒

批次 81:
  奖励值: 108.7805
  收益率: 0.2213
  距离: 27.0141
  内存使用: 0.9084
  能量使用: 0.9165
  推理时间: 1.9949秒

批次 82:
  奖励值: 134.0126
  收益率: 0.2606
  距离: 31.3916
  内存使用: 0.7057
  能量使用: 1.0775
  推理时间: 2.3282秒

批次 83:
  奖励值: 124.5785
  收益率: 0.2477
  距离: 29.4240
  内存使用: 0.6696
  能量使用: 0.9494
  推理时间: 2.1889秒

批次 84:
  奖励值: 140.0019
  收益率: 0.2874
  距离: 36.8715
  内存使用: 0.7547
  能量使用: 1.1701
  推理时间: 2.4375秒

批次 85:
  奖励值: 119.9073
  收益率: 0.2459
  距离: 32.6624
  内存使用: 0.5801
  能量使用: 0.9429
  推理时间: 2.1898秒

批次 86:
  奖励值: 138.9585
  收益率: 0.2814
  距离: 38.8204
  内存使用: 0.7651
  能量使用: 1.0974
  推理时间: 2.4310秒

批次 87:
  奖励值: 112.3868
  收益率: 0.2320
  距离: 31.9848
  内存使用: 0.6258
  能量使用: 1.0156
  推理时间: 2.0766秒

批次 88:
  奖励值: 126.3218
  收益率: 0.2607
  距离: 31.4967
  内存使用: 0.7968
  能量使用: 1.0629
  推理时间: 2.2731秒

批次 89:
  奖励值: 124.3423
  收益率: 0.2518
  距离: 36.0779
  内存使用: 0.6397
  能量使用: 0.9978
  推理时间: 2.2031秒

批次 90:
  奖励值: 135.9624
  收益率: 0.2712
  距离: 33.1856
  内存使用: 0.7505
  能量使用: 1.1391
  推理时间: 2.3736秒

批次 91:
  奖励值: 117.0152
  收益率: 0.2323
  距离: 32.0699
  内存使用: 0.6163
  能量使用: 0.8834
  推理时间: 2.1491秒

批次 92:
  奖励值: 106.6048
  收益率: 0.2163
  距离: 26.8969
  内存使用: 0.8920
  能量使用: 0.8015
  推理时间: 1.8522秒

批次 93:
  奖励值: 122.9596
  收益率: 0.2440
  距离: 28.4044
  内存使用: 0.7248
  能量使用: 0.9515
  推理时间: 2.1909秒

批次 94:
  奖励值: 130.7650
  收益率: 0.2640
  距离: 34.2471
  内存使用: 0.7446
  能量使用: 1.0782
  推理时间: 2.3346秒

批次 95:
  奖励值: 120.5100
  收益率: 0.2333
  距离: 29.7551
  内存使用: 0.6494
  能量使用: 0.9755
  推理时间: 2.0791秒

批次 96:
  奖励值: 110.7457
  收益率: 0.2205
  距离: 30.0801
  内存使用: 0.9080
  能量使用: 0.9206
  推理时间: 2.0269秒

批次 97:
  奖励值: 128.1191
  收益率: 0.2617
  距离: 33.6789
  内存使用: 0.6961
  能量使用: 0.9868
  推理时间: 2.2755秒

批次 98:
  奖励值: 121.0687
  收益率: 0.2381
  距离: 29.8610
  内存使用: 0.6442
  能量使用: 0.9573
  推理时间: 2.1565秒

批次 99:
  奖励值: 140.6803
  收益率: 0.2814
  距离: 35.3313
  内存使用: 0.7953
  能量使用: 1.1128
  推理时间: 2.4762秒

批次 100:
  奖励值: 125.5917
  收益率: 0.2523
  距离: 33.0881
  内存使用: 0.9122
  能量使用: 1.0163
  推理时间: 2.2224秒


==================== 总结 ====================
平均收益率: 0.2452
平均能量使用: 0.9711
平均推理时间: 2.1730秒
