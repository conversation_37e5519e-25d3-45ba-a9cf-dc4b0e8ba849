推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 76.1094
  收益率: 0.4674
  距离: 21.8174
  内存使用: 0.3437
  能量使用: 0.6202
  推理时间: 1.5181秒

批次 2:
  奖励值: 73.7397
  收益率: 0.4659
  距离: 19.8115
  内存使用: 0.3060
  能量使用: 0.6030
  推理时间: 1.4523秒

批次 3:
  奖励值: 67.4687
  收益率: 0.4457
  距离: 18.6549
  内存使用: 0.2762
  能量使用: 0.5315
  推理时间: 1.3016秒

批次 4:
  奖励值: 77.3596
  收益率: 0.4942
  距离: 22.3936
  内存使用: 0.3142
  能量使用: 0.5730
  推理时间: 1.5247秒

批次 5:
  奖励值: 73.1984
  收益率: 0.4617
  距离: 20.2905
  内存使用: 0.3063
  能量使用: 0.6064
  推理时间: 1.4310秒

批次 6:
  奖励值: 75.1685
  收益率: 0.4482
  距离: 19.1757
  内存使用: 0.2638
  能量使用: 0.6235
  推理时间: 1.3680秒

批次 7:
  奖励值: 75.6959
  收益率: 0.4933
  距离: 23.6343
  内存使用: 0.3920
  能量使用: 0.5820
  推理时间: 1.5544秒

批次 8:
  奖励值: 75.4120
  收益率: 0.4606
  距离: 19.0075
  内存使用: 0.3123
  能量使用: 0.6562
  推理时间: 1.4643秒

批次 9:
  奖励值: 77.2924
  收益率: 0.4875
  距离: 19.4385
  内存使用: 0.3590
  能量使用: 0.6176
  推理时间: 1.5543秒

批次 10:
  奖励值: 80.6001
  收益率: 0.5079
  距离: 23.5492
  内存使用: 0.4163
  能量使用: 0.6344
  推理时间: 1.6415秒

批次 11:
  奖励值: 78.3761
  收益率: 0.4733
  距离: 21.4657
  内存使用: 0.3692
  能量使用: 0.6129
  推理时间: 1.4696秒

批次 12:
  奖励值: 78.4437
  收益率: 0.4915
  距离: 22.1539
  内存使用: 0.3015
  能量使用: 0.6571
  推理时间: 1.5706秒

批次 13:
  奖励值: 71.6092
  收益率: 0.4518
  距离: 19.6149
  内存使用: 0.6153
  能量使用: 0.5513
  推理时间: 1.6142秒

批次 14:
  奖励值: 80.0285
  收益率: 0.5000
  距离: 21.5580
  内存使用: 0.3548
  能量使用: 0.6302
  推理时间: 1.5506秒

批次 15:
  奖励值: 66.3739
  收益率: 0.4258
  距离: 19.0374
  内存使用: 0.2512
  能量使用: 0.5637
  推理时间: 1.2576秒

批次 16:
  奖励值: 68.7628
  收益率: 0.4446
  距离: 16.4404
  内存使用: 0.2559
  能量使用: 0.5782
  推理时间: 1.3098秒

批次 17:
  奖励值: 79.0618
  收益率: 0.4978
  距离: 25.0814
  内存使用: 0.3482
  能量使用: 0.6468
  推理时间: 1.5895秒

批次 18:
  奖励值: 71.0373
  收益率: 0.4737
  距离: 20.8577
  内存使用: 0.3101
  能量使用: 0.5576
  推理时间: 1.5100秒

批次 19:
  奖励值: 70.6350
  收益率: 0.4373
  距离: 20.2714
  内存使用: 0.2958
  能量使用: 0.5629
  推理时间: 1.4003秒

批次 20:
  奖励值: 73.3431
  收益率: 0.4818
  距离: 19.2113
  内存使用: 0.2855
  能量使用: 0.6169
  推理时间: 1.4389秒

批次 21:
  奖励值: 73.7067
  收益率: 0.4711
  距离: 19.4261
  内存使用: 0.3328
  能量使用: 0.6277
  推理时间: 1.5039秒

批次 22:
  奖励值: 82.8028
  收益率: 0.5203
  距离: 25.3601
  内存使用: 0.3802
  能量使用: 0.6541
  推理时间: 1.5644秒

批次 23:
  奖励值: 79.7228
  收益率: 0.4810
  距离: 18.9754
  内存使用: 0.3352
  能量使用: 0.6437
  推理时间: 1.4617秒

批次 24:
  奖励值: 71.2788
  收益率: 0.4452
  距离: 18.1404
  内存使用: 0.3206
  能量使用: 0.5814
  推理时间: 1.4016秒

批次 25:
  奖励值: 75.3254
  收益率: 0.4802
  距离: 22.8514
  内存使用: 0.2781
  能量使用: 0.6197
  推理时间: 1.4753秒

批次 26:
  奖励值: 75.1970
  收益率: 0.4732
  距离: 20.1203
  内存使用: 0.3044
  能量使用: 0.5813
  推理时间: 1.4608秒

批次 27:
  奖励值: 83.1059
  收益率: 0.5035
  距离: 21.2138
  内存使用: 0.3352
  能量使用: 0.6391
  推理时间: 1.6173秒

批次 28:
  奖励值: 76.8588
  收益率: 0.5034
  距离: 21.4626
  内存使用: 0.3144
  能量使用: 0.7054
  推理时间: 1.6622秒

批次 29:
  奖励值: 68.4268
  收益率: 0.4375
  距离: 19.1356
  内存使用: 0.2574
  能量使用: 0.5535
  推理时间: 1.4883秒

批次 30:
  奖励值: 78.4050
  收益率: 0.4764
  距离: 22.2090
  内存使用: 0.3208
  能量使用: 0.6601
  推理时间: 1.6285秒

批次 31:
  奖励值: 82.4519
  收益率: 0.5147
  距离: 19.4087
  内存使用: 0.4243
  能量使用: 0.6632
  推理时间: 1.6944秒

批次 32:
  奖励值: 74.1837
  收益率: 0.4516
  距离: 17.7832
  内存使用: 0.2640
  能量使用: 0.5855
  推理时间: 1.5591秒

批次 33:
  奖励值: 70.0638
  收益率: 0.4470
  距离: 19.2675
  内存使用: 0.2179
  能量使用: 0.5871
  推理时间: 1.4967秒

批次 34:
  奖励值: 73.9857
  收益率: 0.4646
  距离: 19.3074
  内存使用: 0.3383
  能量使用: 0.5828
  推理时间: 1.6064秒

批次 35:
  奖励值: 74.8574
  收益率: 0.4735
  距离: 22.6469
  内存使用: 0.3949
  能量使用: 0.6432
  推理时间: 1.6788秒

批次 36:
  奖励值: 82.9007
  收益率: 0.5216
  距离: 21.7616
  内存使用: 0.3715
  能量使用: 0.6656
  推理时间: 1.6854秒

批次 37:
  奖励值: 77.5014
  收益率: 0.5089
  距离: 21.7813
  内存使用: 0.3543
  能量使用: 0.6616
  推理时间: 1.6789秒

批次 38:
  奖励值: 80.1728
  收益率: 0.5036
  距离: 22.4258
  内存使用: 0.3561
  能量使用: 0.6725
  推理时间: 1.7458秒

批次 39:
  奖励值: 75.9584
  收益率: 0.4798
  距离: 17.9428
  内存使用: 0.3197
  能量使用: 0.6208
  推理时间: 1.5476秒

批次 40:
  奖励值: 67.9858
  收益率: 0.4384
  距离: 18.3884
  内存使用: 0.2919
  能量使用: 0.5481
  推理时间: 1.4661秒

批次 41:
  奖励值: 72.4543
  收益率: 0.4712
  距离: 20.8109
  内存使用: 0.2492
  能量使用: 0.6313
  推理时间: 1.5589秒

批次 42:
  奖励值: 82.0216
  收益率: 0.4923
  距离: 20.9120
  内存使用: 0.3744
  能量使用: 0.6704
  推理时间: 1.6832秒

批次 43:
  奖励值: 79.7384
  收益率: 0.4848
  距离: 21.5131
  内存使用: 0.3792
  能量使用: 0.6309
  推理时间: 1.7385秒

批次 44:
  奖励值: 75.4007
  收益率: 0.4783
  距离: 19.7264
  内存使用: 0.6207
  能量使用: 0.6517
  推理时间: 1.6539秒

批次 45:
  奖励值: 74.4242
  收益率: 0.4690
  距离: 18.3898
  内存使用: 0.2909
  能量使用: 0.6709
  推理时间: 1.5481秒

批次 46:
  奖励值: 76.5666
  收益率: 0.4825
  距离: 22.5967
  内存使用: 0.3647
  能量使用: 0.7055
  推理时间: 1.6946秒

批次 47:
  奖励值: 75.3872
  收益率: 0.4775
  距离: 20.1152
  内存使用: 0.3119
  能量使用: 0.5984
  推理时间: 1.5682秒

批次 48:
  奖励值: 73.9807
  收益率: 0.4577
  距离: 19.7435
  内存使用: 0.3131
  能量使用: 0.5820
  推理时间: 1.6338秒

批次 49:
  奖励值: 72.5823
  收益率: 0.4693
  距离: 18.9584
  内存使用: 0.3203
  能量使用: 0.5567
  推理时间: 1.5482秒

批次 50:
  奖励值: 85.3759
  收益率: 0.5183
  距离: 21.1848
  内存使用: 0.4107
  能量使用: 0.6528
  推理时间: 1.7936秒

批次 51:
  奖励值: 77.4921
  收益率: 0.4780
  距离: 17.7366
  内存使用: 0.2840
  能量使用: 0.5953
  推理时间: 1.7220秒

批次 52:
  奖励值: 72.6667
  收益率: 0.4729
  距离: 19.3833
  内存使用: 0.2946
  能量使用: 0.5888
  推理时间: 1.6320秒

批次 53:
  奖励值: 70.7285
  收益率: 0.4591
  距离: 19.2745
  内存使用: 0.3031
  能量使用: 0.5654
  推理时间: 1.4652秒

批次 54:
  奖励值: 72.3691
  收益率: 0.4462
  距离: 20.0765
  内存使用: 0.3620
  能量使用: 0.6233
  推理时间: 1.4544秒

批次 55:
  奖励值: 79.7997
  收益率: 0.4718
  距离: 18.3507
  内存使用: 0.3886
  能量使用: 0.6612
  推理时间: 1.6250秒

批次 56:
  奖励值: 83.4355
  收益率: 0.5335
  距离: 23.0521
  内存使用: 0.3552
  能量使用: 0.7218
  推理时间: 1.6535秒

批次 57:
  奖励值: 76.5043
  收益率: 0.4879
  距离: 23.4322
  内存使用: 0.3234
  能量使用: 0.6359
  推理时间: 1.5764秒

批次 58:
  奖励值: 76.7396
  收益率: 0.4952
  距离: 19.5455
  内存使用: 0.3522
  能量使用: 0.6230
  推理时间: 1.4938秒

批次 59:
  奖励值: 74.1520
  收益率: 0.4770
  距离: 20.7898
  内存使用: 0.3196
  能量使用: 0.5865
  推理时间: 1.5369秒

批次 60:
  奖励值: 76.6916
  收益率: 0.4754
  距离: 17.8947
  内存使用: 0.2817
  能量使用: 0.6404
  推理时间: 1.5392秒

批次 61:
  奖励值: 82.2185
  收益率: 0.5098
  距离: 22.3353
  内存使用: 0.3830
  能量使用: 0.6447
  推理时间: 1.8374秒

批次 62:
  奖励值: 82.0954
  收益率: 0.5056
  距离: 22.7475
  内存使用: 0.4134
  能量使用: 0.6482
  推理时间: 1.7454秒

批次 63:
  奖励值: 84.4166
  收益率: 0.5154
  距离: 24.4648
  内存使用: 0.3881
  能量使用: 0.7139
  推理时间: 1.8926秒

批次 64:
  奖励值: 73.2016
  收益率: 0.4781
  距离: 20.5237
  内存使用: 0.2982
  能量使用: 0.5749
  推理时间: 1.5264秒

批次 65:
  奖励值: 66.5271
  收益率: 0.4271
  距离: 19.2036
  内存使用: 0.1899
  能量使用: 0.5523
  推理时间: 1.4705秒

批次 66:
  奖励值: 76.0500
  收益率: 0.4623
  距离: 19.9851
  内存使用: 0.3191
  能量使用: 0.5959
  推理时间: 1.5964秒

批次 67:
  奖励值: 86.7224
  收益率: 0.5107
  距离: 21.4164
  内存使用: 0.3103
  能量使用: 0.6284
  推理时间: 1.7217秒

批次 68:
  奖励值: 72.9268
  收益率: 0.4459
  距离: 19.9151
  内存使用: 0.2860
  能量使用: 0.5453
  推理时间: 1.4682秒

批次 69:
  奖励值: 73.5517
  收益率: 0.4778
  距离: 19.5295
  内存使用: 0.2844
  能量使用: 0.6828
  推理时间: 1.4996秒

批次 70:
  奖励值: 68.3876
  收益率: 0.4386
  距离: 19.6079
  内存使用: 0.2619
  能量使用: 0.5550
  推理时间: 1.4353秒

批次 71:
  奖励值: 82.7947
  收益率: 0.5084
  距离: 20.7950
  内存使用: 0.3676
  能量使用: 0.6480
  推理时间: 1.6746秒

批次 72:
  奖励值: 74.7205
  收益率: 0.4418
  距离: 19.0385
  内存使用: 0.2942
  能量使用: 0.6264
  推理时间: 1.3926秒

批次 73:
  奖励值: 79.4455
  收益率: 0.4813
  距离: 18.1875
  内存使用: 0.2996
  能量使用: 0.6219
  推理时间: 1.5582秒

批次 74:
  奖励值: 69.1229
  收益率: 0.4612
  距离: 21.2179
  内存使用: 0.2955
  能量使用: 0.6407
  推理时间: 1.5067秒

批次 75:
  奖励值: 80.5606
  收益率: 0.4914
  距离: 21.7178
  内存使用: 0.3507
  能量使用: 0.6103
  推理时间: 1.7237秒

批次 76:
  奖励值: 85.9605
  收益率: 0.5307
  距离: 22.1767
  内存使用: 0.4186
  能量使用: 0.6838
  推理时间: 1.7794秒

批次 77:
  奖励值: 79.8586
  收益率: 0.4897
  距离: 19.9387
  内存使用: 0.3058
  能量使用: 0.6383
  推理时间: 1.5913秒

批次 78:
  奖励值: 76.2270
  收益率: 0.4755
  距离: 19.0960
  内存使用: 0.2825
  能量使用: 0.5675
  推理时间: 1.6662秒

批次 79:
  奖励值: 71.4814
  收益率: 0.4384
  距离: 19.1169
  内存使用: 0.3210
  能量使用: 0.5995
  推理时间: 1.5864秒

批次 80:
  奖励值: 75.7105
  收益率: 0.4865
  距离: 23.6148
  内存使用: 0.3471
  能量使用: 0.6171
  推理时间: 1.5771秒

批次 81:
  奖励值: 78.2677
  收益率: 0.4891
  距离: 21.2452
  内存使用: 0.3976
  能量使用: 0.6219
  推理时间: 1.6453秒

批次 82:
  奖励值: 74.4971
  收益率: 0.4745
  距离: 19.3150
  内存使用: 0.3325
  能量使用: 0.5582
  推理时间: 1.5756秒

批次 83:
  奖励值: 72.1387
  收益率: 0.4448
  距离: 17.6940
  内存使用: 0.2785
  能量使用: 0.5502
  推理时间: 1.4775秒

批次 84:
  奖励值: 67.9417
  收益率: 0.4401
  距离: 18.2760
  内存使用: 0.2290
  能量使用: 0.6115
  推理时间: 1.5433秒

批次 85:
  奖励值: 88.9930
  收益率: 0.5526
  距离: 21.6792
  内存使用: 0.4338
  能量使用: 0.7010
  推理时间: 1.8584秒

批次 86:
  奖励值: 75.3736
  收益率: 0.4781
  距离: 18.4952
  内存使用: 0.3254
  能量使用: 0.6323
  推理时间: 1.6303秒

批次 87:
  奖励值: 79.0414
  收益率: 0.4845
  距离: 18.5598
  内存使用: 0.3280
  能量使用: 0.6293
  推理时间: 1.6259秒

批次 88:
  奖励值: 74.1829
  收益率: 0.4624
  距离: 20.3486
  内存使用: 0.2746
  能量使用: 0.6110
  推理时间: 1.5573秒

批次 89:
  奖励值: 74.2903
  收益率: 0.4521
  距离: 17.8187
  内存使用: 0.2616
  能量使用: 0.5392
  推理时间: 1.5321秒

批次 90:
  奖励值: 77.7477
  收益率: 0.4804
  距离: 19.7436
  内存使用: 0.3109
  能量使用: 0.6502
  推理时间: 1.6574秒

批次 91:
  奖励值: 73.9352
  收益率: 0.4847
  距离: 22.6630
  内存使用: 0.3802
  能量使用: 0.6865
  推理时间: 1.5943秒

批次 92:
  奖励值: 77.0901
  收益率: 0.4958
  距离: 20.4818
  内存使用: 0.3154
  能量使用: 0.6225
  推理时间: 1.5446秒

批次 93:
  奖励值: 81.1019
  收益率: 0.5096
  距离: 22.6285
  内存使用: 0.3282
  能量使用: 0.6395
  推理时间: 1.7769秒

批次 94:
  奖励值: 81.8069
  收益率: 0.4912
  距离: 19.7227
  内存使用: 0.3826
  能量使用: 0.6808
  推理时间: 1.7678秒

批次 95:
  奖励值: 73.8548
  收益率: 0.4543
  距离: 19.1139
  内存使用: 0.5823
  能量使用: 0.5941
  推理时间: 1.5314秒

批次 96:
  奖励值: 80.7535
  收益率: 0.5268
  距离: 26.1036
  内存使用: 0.3776
  能量使用: 0.6119
  推理时间: 1.7192秒

批次 97:
  奖励值: 73.6383
  收益率: 0.4425
  距离: 16.4508
  内存使用: 0.2850
  能量使用: 0.5876
  推理时间: 1.4616秒

批次 98:
  奖励值: 74.1809
  收益率: 0.4726
  距离: 21.3016
  内存使用: 0.3516
  能量使用: 0.5850
  推理时间: 1.5530秒

批次 99:
  奖励值: 74.8909
  收益率: 0.4980
  距离: 23.8118
  内存使用: 0.3746
  能量使用: 0.6318
  推理时间: 1.6644秒

批次 100:
  奖励值: 76.1899
  收益率: 0.4674
  距离: 18.4790
  内存使用: 0.3410
  能量使用: 0.6002
  推理时间: 1.5064秒


==================== 总结 ====================
平均收益率: 0.4778
平均能量使用: 0.6185
平均推理时间: 1.5754秒
