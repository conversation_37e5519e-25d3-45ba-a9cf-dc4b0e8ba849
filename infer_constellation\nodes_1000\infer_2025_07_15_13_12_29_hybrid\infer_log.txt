推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 112.0847
  收益率: 0.2901
  距离: 28.5654
  内存使用: 0.8982
  能量使用: 0.9669
  推理时间: 2.1021秒

批次 2:
  奖励值: 121.8057
  收益率: 0.3045
  距离: 31.6108
  内存使用: 0.6354
  能量使用: 0.9424
  推理时间: 2.1999秒

批次 3:
  奖励值: 127.5517
  收益率: 0.3158
  距离: 32.6362
  内存使用: 0.6788
  能量使用: 0.9816
  推理时间: 2.3078秒

批次 4:
  奖励值: 136.2810
  收益率: 0.3360
  距离: 32.6607
  内存使用: 0.7466
  能量使用: 1.0306
  推理时间: 2.5892秒

批次 5:
  奖励值: 108.0353
  收益率: 0.2737
  距离: 28.8905
  内存使用: 0.5999
  能量使用: 0.8390
  推理时间: 1.9701秒

批次 6:
  奖励值: 124.7414
  收益率: 0.3158
  距离: 32.8549
  内存使用: 0.7172
  能量使用: 0.9451
  推理时间: 2.2719秒

批次 7:
  奖励值: 111.0275
  收益率: 0.2880
  距离: 31.9719
  内存使用: 0.6252
  能量使用: 0.9479
  推理时间: 2.0723秒

批次 8:
  奖励值: 119.3714
  收益率: 0.3010
  距离: 29.5773
  内存使用: 0.7085
  能量使用: 0.9714
  推理时间: 2.1718秒

批次 9:
  奖励值: 119.5094
  收益率: 0.3041
  距离: 33.4194
  内存使用: 0.6429
  能量使用: 0.9189
  推理时间: 2.2479秒

批次 10:
  奖励值: 137.1584
  收益率: 0.3395
  距离: 34.3692
  内存使用: 0.6632
  能量使用: 1.0507
  推理时间: 2.5369秒

批次 11:
  奖励值: 126.2861
  收益率: 0.3086
  距离: 31.4481
  内存使用: 0.6816
  能量使用: 0.8981
  推理时间: 2.2225秒

批次 12:
  奖励值: 131.0779
  收益率: 0.3359
  距离: 35.1531
  内存使用: 0.6946
  能量使用: 0.9965
  推理时间: 2.3768秒

批次 13:
  奖励值: 117.0116
  收益率: 0.2999
  距离: 34.1581
  内存使用: 0.9114
  能量使用: 0.9073
  推理时间: 2.1595秒

批次 14:
  奖励值: 121.8178
  收益率: 0.3074
  距离: 29.7812
  内存使用: 0.7092
  能量使用: 0.8740
  推理时间: 2.2347秒

批次 15:
  奖励值: 124.0057
  收益率: 0.3172
  距离: 33.3179
  内存使用: 0.7126
  能量使用: 0.9582
  推理时间: 2.3159秒

批次 16:
  奖励值: 131.5664
  收益率: 0.3336
  距离: 35.2445
  内存使用: 0.6843
  能量使用: 0.9384
  推理时间: 2.3849秒

批次 17:
  奖励值: 132.2753
  收益率: 0.3370
  距离: 36.7142
  内存使用: 0.6719
  能量使用: 1.0633
  推理时间: 2.4211秒

批次 18:
  奖励值: 128.2767
  收益率: 0.3233
  距离: 29.3529
  内存使用: 0.6522
  能量使用: 1.0415
  推理时间: 2.2960秒

批次 19:
  奖励值: 125.5151
  收益率: 0.3104
  距离: 27.5344
  内存使用: 0.6661
  能量使用: 0.9693
  推理时间: 2.2895秒

批次 20:
  奖励值: 139.6445
  收益率: 0.3451
  距离: 34.5402
  内存使用: 0.7399
  能量使用: 1.0309
  推理时间: 2.5798秒

批次 21:
  奖励值: 125.1281
  收益率: 0.3047
  距离: 36.1316
  内存使用: 0.6252
  能量使用: 0.9869
  推理时间: 2.3158秒

批次 22:
  奖励值: 117.6445
  收益率: 0.2964
  距离: 32.8860
  内存使用: 0.9091
  能量使用: 0.9158
  推理时间: 2.1691秒

批次 23:
  奖励值: 111.0189
  收益率: 0.2801
  距离: 26.6831
  内存使用: 0.6233
  能量使用: 0.8557
  推理时间: 2.0304秒

批次 24:
  奖励值: 125.6502
  收益率: 0.3109
  距离: 29.9659
  内存使用: 0.7088
  能量使用: 1.0250
  推理时间: 2.2613秒

批次 25:
  奖励值: 125.4337
  收益率: 0.3023
  距离: 30.3313
  内存使用: 0.6149
  能量使用: 0.9864
  推理时间: 2.2455秒

批次 26:
  奖励值: 121.0533
  收益率: 0.3115
  距离: 35.1195
  内存使用: 0.6747
  能量使用: 0.8991
  推理时间: 2.1723秒

批次 27:
  奖励值: 130.6666
  收益率: 0.3324
  距离: 31.3771
  内存使用: 0.7169
  能量使用: 0.9726
  推理时间: 2.3509秒

批次 28:
  奖励值: 114.3755
  收益率: 0.2879
  距离: 30.5942
  内存使用: 0.5841
  能量使用: 0.8707
  推理时间: 2.0430秒

批次 29:
  奖励值: 133.2114
  收益率: 0.3324
  距离: 32.7496
  内存使用: 0.6927
  能量使用: 1.1349
  推理时间: 2.3933秒

批次 30:
  奖励值: 122.6318
  收益率: 0.2988
  距离: 31.4462
  内存使用: 0.7038
  能量使用: 0.9865
  推理时间: 2.1875秒

批次 31:
  奖励值: 141.3900
  收益率: 0.3555
  距离: 34.3692
  内存使用: 0.7565
  能量使用: 1.0944
  推理时间: 2.5558秒

批次 32:
  奖励值: 136.0421
  收益率: 0.3439
  距离: 37.8673
  内存使用: 0.8059
  能量使用: 1.0425
  推理时间: 2.5964秒

批次 33:
  奖励值: 141.4073
  收益率: 0.3542
  距离: 37.2954
  内存使用: 0.8010
  能量使用: 1.0889
  推理时间: 2.6315秒

批次 34:
  奖励值: 117.4697
  收益率: 0.2903
  距离: 28.2625
  内存使用: 0.7255
  能量使用: 0.8725
  推理时间: 2.1669秒

批次 35:
  奖励值: 125.1899
  收益率: 0.3144
  距离: 31.7589
  内存使用: 0.6608
  能量使用: 1.0003
  推理时间: 2.2544秒

批次 36:
  奖励值: 118.6712
  收益率: 0.2973
  距离: 28.8311
  内存使用: 0.8992
  能量使用: 0.9272
  推理时间: 2.1541秒

批次 37:
  奖励值: 114.2134
  收益率: 0.2910
  距离: 31.4726
  内存使用: 0.5860
  能量使用: 0.9146
  推理时间: 2.0874秒

批次 38:
  奖励值: 120.6154
  收益率: 0.3027
  距离: 28.0388
  内存使用: 0.6165
  能量使用: 0.9140
  推理时间: 2.2285秒

批次 39:
  奖励值: 117.0788
  收益率: 0.3060
  距离: 32.6268
  内存使用: 0.6337
  能量使用: 0.9819
  推理时间: 2.3133秒

批次 40:
  奖励值: 126.3402
  收益率: 0.3121
  距离: 28.6127
  内存使用: 0.6832
  能量使用: 1.0236
  推理时间: 2.2869秒

批次 41:
  奖励值: 119.5188
  收益率: 0.2972
  距离: 27.5843
  内存使用: 0.6405
  能量使用: 0.9375
  推理时间: 2.2285秒

批次 42:
  奖励值: 119.2314
  收益率: 0.2956
  距离: 30.1841
  内存使用: 0.6424
  能量使用: 0.9106
  推理时间: 2.1385秒

批次 43:
  奖励值: 118.0954
  收益率: 0.3026
  距离: 29.8198
  内存使用: 0.6779
  能量使用: 0.9114
  推理时间: 2.1342秒

批次 44:
  奖励值: 118.3594
  收益率: 0.3037
  距离: 31.9596
  内存使用: 0.6234
  能量使用: 0.8982
  推理时间: 2.1816秒

批次 45:
  奖励值: 125.5268
  收益率: 0.3133
  距离: 29.6212
  内存使用: 0.6122
  能量使用: 0.9129
  推理时间: 2.3014秒

批次 46:
  奖励值: 125.8861
  收益率: 0.3203
  距离: 32.5899
  内存使用: 0.6171
  能量使用: 1.0207
  推理时间: 2.3592秒

批次 47:
  奖励值: 113.3951
  收益率: 0.2827
  距离: 28.1308
  内存使用: 0.5978
  能量使用: 0.8812
  推理时间: 2.0541秒

批次 48:
  奖励值: 119.9260
  收益率: 0.3094
  距离: 29.1148
  内存使用: 0.6761
  能量使用: 0.9571
  推理时间: 2.1783秒

批次 49:
  奖励值: 110.0238
  收益率: 0.2728
  距离: 25.9483
  内存使用: 0.6120
  能量使用: 0.7921
  推理时间: 2.0889秒

批次 50:
  奖励值: 118.2651
  收益率: 0.2870
  距离: 27.3565
  内存使用: 0.6209
  能量使用: 0.8923
  推理时间: 2.1235秒

批次 51:
  奖励值: 138.3489
  收益率: 0.3445
  距离: 37.3461
  内存使用: 0.7553
  能量使用: 1.0007
  推理时间: 2.5214秒

批次 52:
  奖励值: 129.9010
  收益率: 0.3207
  距离: 31.3076
  内存使用: 0.7056
  能量使用: 0.9847
  推理时间: 2.5078秒

批次 53:
  奖励值: 127.5333
  收益率: 0.3314
  距离: 33.9534
  内存使用: 0.6479
  能量使用: 0.9319
  推理时间: 2.3634秒

批次 54:
  奖励值: 125.1398
  收益率: 0.3092
  距离: 28.8693
  内存使用: 0.6958
  能量使用: 0.9554
  推理时间: 2.3626秒

批次 55:
  奖励值: 112.9946
  收益率: 0.2824
  距离: 28.4986
  内存使用: 0.6101
  能量使用: 0.9021
  推理时间: 2.2446秒

批次 56:
  奖励值: 114.2221
  收益率: 0.2854
  距离: 29.8930
  内存使用: 0.9091
  能量使用: 0.9538
  推理时间: 2.2169秒

批次 57:
  奖励值: 114.7111
  收益率: 0.2998
  距离: 30.7612
  内存使用: 0.6656
  能量使用: 0.9511
  推理时间: 2.1558秒

批次 58:
  奖励值: 121.6766
  收益率: 0.3043
  距离: 31.0413
  内存使用: 0.6293
  能量使用: 0.9761
  推理时间: 2.2177秒

批次 59:
  奖励值: 119.9423
  收益率: 0.3031
  距离: 30.5555
  内存使用: 0.6635
  能量使用: 0.8964
  推理时间: 2.2793秒

批次 60:
  奖励值: 112.4783
  收益率: 0.2862
  距离: 28.8526
  内存使用: 0.9072
  能量使用: 0.8836
  推理时间: 2.1666秒

批次 61:
  奖励值: 130.0260
  收益率: 0.3307
  距离: 35.3452
  内存使用: 0.7744
  能量使用: 1.0284
  推理时间: 2.3950秒

批次 62:
  奖励值: 110.9979
  收益率: 0.2826
  距离: 31.1247
  内存使用: 0.6011
  能量使用: 0.8785
  推理时间: 2.0465秒

批次 63:
  奖励值: 114.0589
  收益率: 0.2812
  距离: 29.0277
  内存使用: 0.5829
  能量使用: 0.8716
  推理时间: 2.0393秒

批次 64:
  奖励值: 133.8022
  收益率: 0.3370
  距离: 33.4695
  内存使用: 0.6780
  能量使用: 1.0480
  推理时间: 2.4468秒

批次 65:
  奖励值: 117.9413
  收益率: 0.2921
  距离: 27.7392
  内存使用: 0.6377
  能量使用: 0.9404
  推理时间: 2.0972秒

批次 66:
  奖励值: 113.9570
  收益率: 0.2819
  距离: 25.1191
  内存使用: 0.9091
  能量使用: 0.8577
  推理时间: 2.0368秒

批次 67:
  奖励值: 138.2358
  收益率: 0.3464
  距离: 34.2065
  内存使用: 0.7549
  能量使用: 1.1669
  推理时间: 2.4986秒

批次 68:
  奖励值: 135.1628
  收益率: 0.3401
  距离: 33.2896
  内存使用: 0.7107
  能量使用: 1.0740
  推理时间: 2.4927秒

批次 69:
  奖励值: 107.7433
  收益率: 0.2694
  距离: 26.7215
  内存使用: 0.5950
  能量使用: 0.7968
  推理时间: 1.9413秒

批次 70:
  奖励值: 124.7767
  收益率: 0.3135
  距离: 31.9826
  内存使用: 0.6283
  能量使用: 1.0358
  推理时间: 2.3096秒

批次 71:
  奖励值: 116.8158
  收益率: 0.2889
  距离: 27.9049
  内存使用: 0.5882
  能量使用: 0.8752
  推理时间: 2.2645秒

批次 72:
  奖励值: 132.6656
  收益率: 0.3373
  距离: 35.8668
  内存使用: 0.7220
  能量使用: 1.0799
  推理时间: 2.4458秒

批次 73:
  奖励值: 131.4100
  收益率: 0.3341
  距离: 29.9011
  内存使用: 0.6481
  能量使用: 1.0307
  推理时间: 2.4303秒

批次 74:
  奖励值: 136.4213
  收益率: 0.3241
  距离: 28.4074
  内存使用: 0.7653
  能量使用: 0.9847
  推理时间: 2.4070秒

批次 75:
  奖励值: 116.2849
  收益率: 0.2807
  距离: 30.8513
  内存使用: 0.9075
  能量使用: 0.8714
  推理时间: 2.1363秒

批次 76:
  奖励值: 129.2486
  收益率: 0.3305
  距离: 34.8422
  内存使用: 0.7208
  能量使用: 0.9397
  推理时间: 2.4119秒

批次 77:
  奖励值: 110.5187
  收益率: 0.2722
  距离: 30.3353
  内存使用: 0.5610
  能量使用: 0.8663
  推理时间: 2.0809秒

批次 78:
  奖励值: 125.8767
  收益率: 0.3209
  距离: 33.8899
  内存使用: 0.7199
  能量使用: 1.0368
  推理时间: 2.4999秒

批次 79:
  奖励值: 121.7264
  收益率: 0.2968
  距离: 23.9711
  内存使用: 0.6183
  能量使用: 0.9577
  推理时间: 2.2550秒

批次 80:
  奖励值: 119.3450
  收益率: 0.2939
  距离: 28.3711
  内存使用: 0.6298
  能量使用: 0.9092
  推理时间: 2.1630秒

批次 81:
  奖励值: 133.9031
  收益率: 0.3308
  距离: 32.2947
  内存使用: 0.7113
  能量使用: 0.9877
  推理时间: 2.4864秒

批次 82:
  奖励值: 108.6271
  收益率: 0.2785
  距离: 29.0346
  内存使用: 0.5877
  能量使用: 0.9234
  推理时间: 2.0334秒

批次 83:
  奖励值: 118.1959
  收益率: 0.3002
  距离: 32.6385
  内存使用: 0.6320
  能量使用: 0.9375
  推理时间: 2.1212秒

批次 84:
  奖励值: 130.7768
  收益率: 0.3336
  距离: 33.6615
  内存使用: 0.7404
  能量使用: 1.0444
  推理时间: 2.3990秒

批次 85:
  奖励值: 112.7212
  收益率: 0.2867
  距离: 31.9066
  内存使用: 0.9151
  能量使用: 0.9358
  推理时间: 2.1511秒

批次 86:
  奖励值: 123.3283
  收益率: 0.3208
  距离: 31.7013
  内存使用: 0.6610
  能量使用: 0.9811
  推理时间: 2.3658秒

批次 87:
  奖励值: 128.2844
  收益率: 0.3281
  距离: 28.2940
  内存使用: 0.6816
  能量使用: 1.0554
  推理时间: 2.3828秒

批次 88:
  奖励值: 111.9292
  收益率: 0.2835
  距离: 27.5484
  内存使用: 0.5986
  能量使用: 0.7946
  推理时间: 2.1126秒

批次 89:
  奖励值: 120.0770
  收益率: 0.3003
  距离: 31.2745
  内存使用: 0.6698
  能量使用: 0.9016
  推理时间: 2.2614秒

批次 90:
  奖励值: 129.3780
  收益率: 0.3258
  距离: 34.9756
  内存使用: 0.7237
  能量使用: 1.1447
  推理时间: 2.4240秒

批次 91:
  奖励值: 129.6524
  收益率: 0.3290
  距离: 33.4136
  内存使用: 0.6785
  能量使用: 1.0031
  推理时间: 2.3355秒

批次 92:
  奖励值: 121.7680
  收益率: 0.3060
  距离: 30.4332
  内存使用: 0.6848
  能量使用: 0.9460
  推理时间: 2.2722秒

批次 93:
  奖励值: 129.2393
  收益率: 0.3195
  距离: 33.4011
  内存使用: 0.7624
  能量使用: 0.9580
  推理时间: 2.4596秒

批次 94:
  奖励值: 131.8030
  收益率: 0.3261
  距离: 33.9298
  内存使用: 0.6985
  能量使用: 0.9657
  推理时间: 2.4215秒

批次 95:
  奖励值: 110.8297
  收益率: 0.2793
  距离: 31.5790
  内存使用: 0.5142
  能量使用: 0.8472
  推理时间: 2.0629秒

批次 96:
  奖励值: 119.0710
  收益率: 0.2987
  距离: 29.1845
  内存使用: 0.6138
  能量使用: 0.8597
  推理时间: 2.1901秒

批次 97:
  奖励值: 131.5249
  收益率: 0.3250
  距离: 30.6640
  内存使用: 0.6740
  能量使用: 1.0122
  推理时间: 2.3872秒

批次 98:
  奖励值: 122.6954
  收益率: 0.3147
  距离: 31.9608
  内存使用: 0.6771
  能量使用: 0.9712
  推理时间: 2.2763秒

批次 99:
  奖励值: 124.3243
  收益率: 0.3117
  距离: 32.5416
  内存使用: 0.6317
  能量使用: 0.9780
  推理时间: 2.2834秒

批次 100:
  奖励值: 129.6070
  收益率: 0.3211
  距离: 25.1586
  内存使用: 0.6877
  能量使用: 0.9396
  推理时间: 2.3536秒


==================== 总结 ====================
平均收益率: 0.3094
平均能量使用: 0.9575
平均推理时间: 2.2720秒
