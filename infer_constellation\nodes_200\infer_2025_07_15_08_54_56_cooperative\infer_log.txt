推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_14_09_53_20

批次 1:
  奖励值: 47.5541
  收益率: 0.6107
  距离: 13.5517
  内存使用: 0.1593
  能量使用: 0.3809
  推理时间: 1.0562秒

批次 2:
  奖励值: 54.5185
  收益率: 0.6699
  距离: 14.5283
  内存使用: 0.1848
  能量使用: 0.4499
  推理时间: 1.0400秒

批次 3:
  奖励值: 52.0083
  收益率: 0.6779
  距离: 15.7417
  内存使用: 0.1867
  能量使用: 0.4551
  推理时间: 1.0424秒

批次 4:
  奖励值: 57.0515
  收益率: 0.6744
  距离: 12.8145
  内存使用: 0.2168
  能量使用: 0.4658
  推理时间: 1.1182秒

批次 5:
  奖励值: 51.1287
  收益率: 0.6697
  距离: 16.7444
  内存使用: 0.1868
  能量使用: 0.4114
  推理时间: 1.0800秒

批次 6:
  奖励值: 58.4936
  收益率: 0.6995
  距离: 17.3133
  内存使用: 0.1822
  能量使用: 0.5172
  推理时间: 1.1390秒

批次 7:
  奖励值: 60.4583
  收益率: 0.6997
  距离: 16.8149
  内存使用: 0.1670
  能量使用: 0.5015
  推理时间: 1.1177秒

批次 8:
  奖励值: 59.7605
  收益率: 0.7385
  距离: 18.1043
  内存使用: 0.2460
  能量使用: 0.5288
  推理时间: 1.2443秒

批次 9:
  奖励值: 50.9366
  收益率: 0.6298
  距离: 13.5555
  内存使用: 0.1423
  能量使用: 0.4290
  推理时间: 0.9723秒

批次 10:
  奖励值: 56.0243
  收益率: 0.6670
  距离: 16.6896
  内存使用: 0.2418
  能量使用: 0.4200
  推理时间: 1.0579秒

批次 11:
  奖励值: 49.8196
  收益率: 0.6284
  距离: 15.8342
  内存使用: 0.1223
  能量使用: 0.4044
  推理时间: 0.9445秒

批次 12:
  奖励值: 56.5616
  收益率: 0.6980
  距离: 13.7949
  内存使用: 0.1937
  能量使用: 0.4881
  推理时间: 1.0839秒

批次 13:
  奖励值: 55.2554
  收益率: 0.6955
  距离: 15.8024
  内存使用: 0.2027
  能量使用: 0.4842
  推理时间: 1.1100秒

批次 14:
  奖励值: 61.6292
  收益率: 0.7138
  距离: 15.3358
  内存使用: 0.1775
  能量使用: 0.4283
  推理时间: 1.1612秒

批次 15:
  奖励值: 56.2892
  收益率: 0.7107
  距离: 17.9225
  内存使用: 0.2194
  能量使用: 0.4797
  推理时间: 1.1398秒

批次 16:
  奖励值: 51.6312
  收益率: 0.6343
  距离: 15.1402
  内存使用: 0.1456
  能量使用: 0.4519
  推理时间: 0.9912秒

批次 17:
  奖励值: 56.5153
  收益率: 0.6743
  距离: 15.9081
  内存使用: 0.2034
  能量使用: 0.4579
  推理时间: 1.0576秒

批次 18:
  奖励值: 53.2399
  收益率: 0.6275
  距离: 13.4593
  内存使用: 0.1783
  能量使用: 0.4825
  推理时间: 1.0042秒

批次 19:
  奖励值: 56.1339
  收益率: 0.7102
  距离: 15.4391
  内存使用: 0.1668
  能量使用: 0.5263
  推理时间: 1.1229秒

批次 20:
  奖励值: 54.5540
  收益率: 0.6817
  距离: 17.0029
  内存使用: 0.1591
  能量使用: 0.5300
  推理时间: 1.0793秒

批次 21:
  奖励值: 60.2471
  收益率: 0.7076
  距离: 15.8080
  内存使用: 0.2285
  能量使用: 0.4693
  推理时间: 1.0897秒

批次 22:
  奖励值: 55.7484
  收益率: 0.6901
  距离: 15.6931
  内存使用: 0.2068
  能量使用: 0.4033
  推理时间: 1.0539秒

批次 23:
  奖励值: 52.3768
  收益率: 0.6512
  距离: 15.5999
  内存使用: 0.1831
  能量使用: 0.4634
  推理时间: 1.0607秒

批次 24:
  奖励值: 52.1534
  收益率: 0.6372
  距离: 14.9078
  内存使用: 0.1606
  能量使用: 0.4249
  推理时间: 1.0034秒

批次 25:
  奖励值: 52.2934
  收益率: 0.6549
  距离: 13.5673
  内存使用: 0.1726
  能量使用: 0.4732
  推理时间: 0.9799秒

批次 26:
  奖励值: 56.9828
  收益率: 0.7157
  距离: 18.7094
  内存使用: 0.2154
  能量使用: 0.5095
  推理时间: 1.2277秒

批次 27:
  奖励值: 52.9123
  收益率: 0.6670
  距离: 16.0720
  内存使用: 0.2129
  能量使用: 0.4904
  推理时间: 1.1333秒

批次 28:
  奖励值: 47.6486
  收益率: 0.6150
  距离: 13.6901
  内存使用: 0.1390
  能量使用: 0.4737
  推理时间: 1.0765秒

批次 29:
  奖励值: 55.6845
  收益率: 0.6599
  距离: 17.6513
  内存使用: 0.1365
  能量使用: 0.4431
  推理时间: 1.1390秒

批次 30:
  奖励值: 54.8421
  收益率: 0.6911
  距离: 14.1103
  内存使用: 0.1831
  能量使用: 0.4224
  推理时间: 1.0729秒

批次 31:
  奖励值: 54.6328
  收益率: 0.6916
  距离: 15.6619
  内存使用: 0.1561
  能量使用: 0.5165
  推理时间: 1.2111秒

批次 32:
  奖励值: 55.9808
  收益率: 0.7220
  距离: 17.7474
  内存使用: 0.1922
  能量使用: 0.4987
  推理时间: 1.1620秒

批次 33:
  奖励值: 54.2195
  收益率: 0.6831
  距离: 15.5143
  内存使用: 0.1742
  能量使用: 0.4726
  推理时间: 1.0982秒

批次 34:
  奖励值: 55.4759
  收益率: 0.6783
  距离: 12.4815
  内存使用: 0.1734
  能量使用: 0.3933
  推理时间: 1.1617秒

批次 35:
  奖励值: 52.9308
  收益率: 0.6619
  距离: 13.6921
  内存使用: 0.1311
  能量使用: 0.5152
  推理时间: 1.0753秒

批次 36:
  奖励值: 52.8080
  收益率: 0.6855
  距离: 14.9262
  内存使用: 0.1524
  能量使用: 0.5054
  推理时间: 1.0982秒

批次 37:
  奖励值: 56.2140
  收益率: 0.7250
  距离: 18.6988
  内存使用: 0.1518
  能量使用: 0.5213
  推理时间: 1.2183秒

批次 38:
  奖励值: 48.9939
  收益率: 0.6175
  距离: 14.4338
  内存使用: 0.1230
  能量使用: 0.4555
  推理时间: 1.0259秒

批次 39:
  奖励值: 50.0401
  收益率: 0.6457
  距离: 16.2569
  内存使用: 0.1970
  能量使用: 0.4660
  推理时间: 1.1462秒

批次 40:
  奖励值: 49.3467
  收益率: 0.6335
  距离: 14.1137
  内存使用: 0.1280
  能量使用: 0.4649
  推理时间: 1.0325秒

批次 41:
  奖励值: 51.4391
  收益率: 0.6697
  距离: 14.5610
  内存使用: 0.0947
  能量使用: 0.4661
  推理时间: 1.1708秒

批次 42:
  奖励值: 55.9509
  收益率: 0.6886
  距离: 14.8153
  内存使用: 0.2156
  能量使用: 0.4674
  推理时间: 1.1624秒

批次 43:
  奖励值: 56.8765
  收益率: 0.6966
  距离: 14.2280
  内存使用: 0.1466
  能量使用: 0.4723
  推理时间: 1.1219秒

批次 44:
  奖励值: 51.3665
  收益率: 0.6520
  距离: 15.5139
  内存使用: 0.1522
  能量使用: 0.4010
  推理时间: 1.0618秒

批次 45:
  奖励值: 51.4528
  收益率: 0.6561
  距离: 16.2874
  内存使用: 0.1550
  能量使用: 0.4520
  推理时间: 1.1125秒

批次 46:
  奖励值: 55.6841
  收益率: 0.6941
  距离: 17.7695
  内存使用: 0.1538
  能量使用: 0.5087
  推理时间: 1.1849秒

批次 47:
  奖励值: 46.4876
  收益率: 0.5905
  距离: 12.8823
  内存使用: 0.1003
  能量使用: 0.4422
  推理时间: 1.0044秒

批次 48:
  奖励值: 50.3906
  收益率: 0.6494
  距离: 13.7735
  内存使用: 0.1239
  能量使用: 0.5215
  推理时间: 1.1396秒

批次 49:
  奖励值: 50.2258
  收益率: 0.6425
  距离: 14.3279
  内存使用: 0.1367
  能量使用: 0.4838
  推理时间: 1.0529秒

批次 50:
  奖励值: 51.9012
  收益率: 0.6536
  距离: 15.1554
  内存使用: 0.1726
  能量使用: 0.4698
  推理时间: 1.1533秒

批次 51:
  奖励值: 54.3126
  收益率: 0.6742
  距离: 16.1340
  内存使用: 0.1759
  能量使用: 0.4649
  推理时间: 1.1639秒

批次 52:
  奖励值: 56.5087
  收益率: 0.6979
  距离: 17.4632
  内存使用: 0.2341
  能量使用: 0.4853
  推理时间: 1.1309秒

批次 53:
  奖励值: 63.0464
  收益率: 0.7194
  距离: 17.7041
  内存使用: 0.2427
  能量使用: 0.5603
  推理时间: 1.2280秒

批次 54:
  奖励值: 50.2764
  收益率: 0.6487
  距离: 16.9615
  内存使用: 0.1748
  能量使用: 0.5110
  推理时间: 1.1389秒

批次 55:
  奖励值: 54.5765
  收益率: 0.7006
  距离: 15.6139
  内存使用: 0.1969
  能量使用: 0.4363
  推理时间: 1.1337秒

批次 56:
  奖励值: 57.6785
  收益率: 0.6753
  距离: 14.9706
  内存使用: 0.1831
  能量使用: 0.4893
  推理时间: 1.1322秒

批次 57:
  奖励值: 53.0088
  收益率: 0.6495
  距离: 12.0022
  内存使用: 0.1373
  能量使用: 0.4429
  推理时间: 1.2285秒

批次 58:
  奖励值: 53.4995
  收益率: 0.6602
  距离: 14.3587
  内存使用: 0.1590
  能量使用: 0.4833
  推理时间: 1.0882秒

批次 59:
  奖励值: 54.0724
  收益率: 0.6652
  距离: 12.0966
  内存使用: 0.1159
  能量使用: 0.4426
  推理时间: 1.1187秒

批次 60:
  奖励值: 54.3475
  收益率: 0.6804
  距离: 15.8852
  内存使用: 0.1487
  能量使用: 0.4711
  推理时间: 1.1100秒

批次 61:
  奖励值: 53.2297
  收益率: 0.6606
  距离: 15.5075
  内存使用: 0.1532
  能量使用: 0.4800
  推理时间: 1.1956秒

批次 62:
  奖励值: 52.2597
  收益率: 0.6465
  距离: 14.4646
  内存使用: 0.1776
  能量使用: 0.4384
  推理时间: 1.0902秒

批次 63:
  奖励值: 50.5132
  收益率: 0.6667
  距离: 13.9485
  内存使用: 0.1641
  能量使用: 0.4610
  推理时间: 1.0366秒

批次 64:
  奖励值: 55.1973
  收益率: 0.6924
  距离: 16.2727
  内存使用: 0.2323
  能量使用: 0.5004
  推理时间: 1.2128秒

批次 65:
  奖励值: 58.2003
  收益率: 0.7138
  距离: 19.0805
  内存使用: 0.2261
  能量使用: 0.4928
  推理时间: 1.2408秒

批次 66:
  奖励值: 56.3214
  收益率: 0.6926
  距离: 15.0837
  内存使用: 0.2033
  能量使用: 0.4702
  推理时间: 1.1058秒

批次 67:
  奖励值: 51.8746
  收益率: 0.6601
  距离: 16.4743
  内存使用: 0.1415
  能量使用: 0.4349
  推理时间: 1.1012秒

批次 68:
  奖励值: 53.4405
  收益率: 0.6773
  距离: 15.8655
  内存使用: 0.1609
  能量使用: 0.4925
  推理时间: 1.1107秒

批次 69:
  奖励值: 37.2112
  收益率: 0.4653
  距离: 11.0248
  内存使用: 0.0221
  能量使用: 0.3307
  推理时间: 0.8147秒

批次 70:
  奖励值: 54.1257
  收益率: 0.6714
  距离: 14.9153
  内存使用: 0.1719
  能量使用: 0.4614
  推理时间: 1.1963秒

批次 71:
  奖励值: 54.3872
  收益率: 0.6827
  距离: 15.7871
  内存使用: 0.1429
  能量使用: 0.4956
  推理时间: 1.1240秒

批次 72:
  奖励值: 53.1688
  收益率: 0.6982
  距离: 15.5664
  内存使用: 0.1950
  能量使用: 0.5010
  推理时间: 1.2600秒

批次 73:
  奖励值: 55.9785
  收益率: 0.7120
  距离: 18.2963
  内存使用: 0.2072
  能量使用: 0.5395
  推理时间: 1.2572秒

批次 74:
  奖励值: 53.2144
  收益率: 0.6645
  距离: 14.9219
  内存使用: 0.1618
  能量使用: 0.4876
  推理时间: 1.1219秒

批次 75:
  奖励值: 55.3598
  收益率: 0.6888
  距离: 17.3672
  内存使用: 0.2168
  能量使用: 0.4965
  推理时间: 1.2164秒

批次 76:
  奖励值: 51.0432
  收益率: 0.6408
  距离: 14.7709
  内存使用: 0.1224
  能量使用: 0.4204
  推理时间: 1.0831秒

批次 77:
  奖励值: 51.0519
  收益率: 0.6593
  距离: 16.8521
  内存使用: 0.1615
  能量使用: 0.4825
  推理时间: 1.1498秒

批次 78:
  奖励值: 50.6308
  收益率: 0.6480
  距离: 13.7623
  内存使用: 0.1109
  能量使用: 0.4653
  推理时间: 1.0484秒

批次 79:
  奖励值: 51.0133
  收益率: 0.6533
  距离: 14.5358
  内存使用: 0.1664
  能量使用: 0.4712
  推理时间: 1.0556秒

批次 80:
  奖励值: 53.6078
  收益率: 0.6411
  距离: 15.7350
  内存使用: 0.1657
  能量使用: 0.4838
  推理时间: 1.1071秒

批次 81:
  奖励值: 51.8066
  收益率: 0.6754
  距离: 14.1000
  内存使用: 0.4627
  能量使用: 0.4240
  推理时间: 1.1732秒

批次 82:
  奖励值: 55.3130
  收益率: 0.6842
  距离: 16.5360
  内存使用: 0.1815
  能量使用: 0.4567
  推理时间: 1.1353秒

批次 83:
  奖励值: 54.3526
  收益率: 0.6553
  距离: 13.8278
  内存使用: 0.1736
  能量使用: 0.4934
  推理时间: 1.0942秒

批次 84:
  奖励值: 54.9116
  收益率: 0.7044
  距离: 16.3018
  内存使用: 0.1698
  能量使用: 0.5052
  推理时间: 1.2306秒

批次 85:
  奖励值: 54.3026
  收益率: 0.6876
  距离: 13.1562
  内存使用: 0.1454
  能量使用: 0.4932
  推理时间: 1.2948秒

批次 86:
  奖励值: 46.6068
  收益率: 0.6560
  距离: 16.3379
  内存使用: 0.2174
  能量使用: 0.4311
  推理时间: 1.0869秒

批次 87:
  奖励值: 53.1311
  收益率: 0.6599
  距离: 16.5339
  内存使用: 0.1502
  能量使用: 0.4536
  推理时间: 1.1778秒

批次 88:
  奖励值: 56.8014
  收益率: 0.6895
  距离: 14.4655
  内存使用: 0.2019
  能量使用: 0.5298
  推理时间: 1.2562秒

批次 89:
  奖励值: 55.5744
  收益率: 0.6891
  距离: 14.8519
  内存使用: 0.2083
  能量使用: 0.5910
  推理时间: 1.4417秒

批次 90:
  奖励值: 55.3122
  收益率: 0.6642
  距离: 15.3557
  内存使用: 0.1614
  能量使用: 0.4585
  推理时间: 1.3245秒

批次 91:
  奖励值: 50.1913
  收益率: 0.6458
  距离: 14.6633
  内存使用: 0.1129
  能量使用: 0.4411
  推理时间: 1.1406秒

批次 92:
  奖励值: 55.3722
  收益率: 0.7058
  距离: 17.2084
  内存使用: 0.1823
  能量使用: 0.4649
  推理时间: 1.2088秒

批次 93:
  奖励值: 55.5285
  收益率: 0.7097
  距离: 15.7997
  内存使用: 0.2285
  能量使用: 0.5149
  推理时间: 1.3190秒

批次 94:
  奖励值: 56.6848
  收益率: 0.7058
  距离: 15.9911
  内存使用: 0.1881
  能量使用: 0.4512
  推理时间: 1.3791秒

批次 95:
  奖励值: 56.8009
  收益率: 0.6843
  距离: 14.9404
  内存使用: 0.1728
  能量使用: 0.4851
  推理时间: 1.2775秒

批次 96:
  奖励值: 55.4032
  收益率: 0.6906
  距离: 15.6920
  内存使用: 0.2034
  能量使用: 0.4983
  推理时间: 1.1791秒

批次 97:
  奖励值: 57.5939
  收益率: 0.7026
  距离: 18.2511
  内存使用: 0.2261
  能量使用: 0.5351
  推理时间: 1.2632秒

批次 98:
  奖励值: 46.7153
  收益率: 0.5946
  距离: 12.4111
  内存使用: 0.0415
  能量使用: 0.3169
  推理时间: 0.9231秒

批次 99:
  奖励值: 56.5751
  收益率: 0.6618
  距离: 14.2498
  内存使用: 0.1440
  能量使用: 0.4561
  推理时间: 1.1645秒

批次 100:
  奖励值: 51.0919
  收益率: 0.6629
  距离: 14.4627
  内存使用: 0.1157
  能量使用: 0.4379
  推理时间: 1.0954秒


==================== 总结 ====================
平均收益率: 0.6707
平均能量使用: 0.4699
平均推理时间: 1.1296秒
