推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 111.7157
  收益率: 0.2871
  距离: 27.0826
  内存使用: 0.5826
  能量使用: 0.9033
  推理时间: 1.9238秒

批次 2:
  奖励值: 102.1779
  收益率: 0.2539
  距离: 25.2349
  内存使用: 0.4627
  能量使用: 0.7465
  推理时间: 1.8022秒

批次 3:
  奖励值: 113.2182
  收益率: 0.2815
  距离: 30.1059
  内存使用: 0.5689
  能量使用: 0.8887
  推理时间: 2.0620秒

批次 4:
  奖励值: 115.1682
  收益率: 0.2845
  距离: 28.0654
  内存使用: 0.6974
  能量使用: 0.9008
  推理时间: 2.0553秒

批次 5:
  奖励值: 106.1660
  收益率: 0.2708
  距离: 29.8230
  内存使用: 0.8519
  能量使用: 0.7932
  推理时间: 1.9039秒

批次 6:
  奖励值: 118.6832
  收益率: 0.2995
  距离: 30.4601
  内存使用: 0.6403
  能量使用: 0.9332
  推理时间: 2.0565秒

批次 7:
  奖励值: 100.3204
  收益率: 0.2569
  距离: 26.0736
  内存使用: 0.5682
  能量使用: 0.7175
  推理时间: 1.8431秒

批次 8:
  奖励值: 107.4925
  收益率: 0.2742
  距离: 29.5792
  内存使用: 0.5951
  能量使用: 0.8407
  推理时间: 1.9107秒

批次 9:
  奖励值: 123.1294
  收益率: 0.3101
  距离: 31.5412
  内存使用: 0.6670
  能量使用: 0.9330
  推理时间: 2.1672秒

批次 10:
  奖励值: 125.7615
  收益率: 0.3093
  距离: 29.7289
  内存使用: 0.5980
  能量使用: 0.9501
  推理时间: 2.1424秒

批次 11:
  奖励值: 116.3851
  收益率: 0.2854
  距离: 29.9731
  内存使用: 0.5567
  能量使用: 0.8999
  推理时间: 2.0598秒

批次 12:
  奖励值: 115.7945
  收益率: 0.2932
  距离: 27.9620
  内存使用: 0.5873
  能量使用: 0.8617
  推理时间: 2.0130秒

批次 13:
  奖励值: 117.5901
  收益率: 0.2939
  距离: 27.9257
  内存使用: 0.6306
  能量使用: 0.8635
  推理时间: 2.0540秒

批次 14:
  奖励值: 104.1806
  收益率: 0.2625
  距离: 25.1105
  内存使用: 0.5438
  能量使用: 0.7845
  推理时间: 1.9146秒

批次 15:
  奖励值: 107.7398
  收益率: 0.2735
  距离: 27.1323
  内存使用: 0.5973
  能量使用: 0.7909
  推理时间: 1.9236秒

批次 16:
  奖励值: 117.8791
  收益率: 0.2989
  距离: 31.5812
  内存使用: 0.6056
  能量使用: 0.8549
  推理时间: 2.0494秒

批次 17:
  奖励值: 99.1787
  收益率: 0.2536
  距离: 28.0432
  内存使用: 0.7652
  能量使用: 0.8342
  推理时间: 1.8369秒

批次 18:
  奖励值: 107.0134
  收益率: 0.2711
  距离: 25.5090
  内存使用: 0.8360
  能量使用: 0.8281
  推理时间: 1.8587秒

批次 19:
  奖励值: 112.4870
  收益率: 0.2813
  距离: 27.5439
  内存使用: 0.6424
  能量使用: 0.8396
  推理时间: 1.9546秒

批次 20:
  奖励值: 117.3330
  收益率: 0.2889
  距离: 28.1794
  内存使用: 0.5010
  能量使用: 0.8537
  推理时间: 2.0256秒

批次 21:
  奖励值: 118.2656
  收益率: 0.2837
  距离: 30.1299
  内存使用: 0.5937
  能量使用: 0.9451
  推理时间: 2.1059秒

批次 22:
  奖励值: 109.7060
  收益率: 0.2738
  距离: 28.5901
  内存使用: 0.5774
  能量使用: 0.8205
  推理时间: 3.1454秒

批次 23:
  奖励值: 100.4280
  收益率: 0.2546
  距离: 25.3090
  内存使用: 0.4877
  能量使用: 0.7472
  推理时间: 1.7785秒

批次 24:
  奖励值: 106.8584
  收益率: 0.2692
  距离: 29.5395
  内存使用: 0.8904
  能量使用: 0.8534
  推理时间: 1.9679秒

批次 25:
  奖励值: 123.4053
  收益率: 0.2985
  距离: 30.8795
  内存使用: 0.5870
  能量使用: 0.9229
  推理时间: 2.1762秒

批次 26:
  奖励值: 112.4225
  收益率: 0.2834
  距离: 27.2594
  内存使用: 0.5807
  能量使用: 0.9148
  推理时间: 1.9974秒

批次 27:
  奖励值: 110.9893
  收益率: 0.2872
  距离: 31.0060
  内存使用: 0.5924
  能量使用: 0.8231
  推理时间: 2.0185秒

批次 28:
  奖励值: 113.3858
  收益率: 0.2842
  距离: 29.2371
  内存使用: 0.5571
  能量使用: 0.8341
  推理时间: 1.9703秒

批次 29:
  奖励值: 110.9792
  收益率: 0.2769
  距离: 27.0557
  内存使用: 0.8655
  能量使用: 0.9203
  推理时间: 2.0268秒

批次 30:
  奖励值: 112.1440
  收益率: 0.2739
  距离: 29.4222
  内存使用: 0.6080
  能量使用: 0.8818
  推理时间: 2.0080秒

批次 31:
  奖励值: 114.1712
  收益率: 0.2857
  距离: 26.5923
  内存使用: 0.5635
  能量使用: 0.9017
  推理时间: 2.0005秒

批次 32:
  奖励值: 133.4240
  收益率: 0.3316
  距离: 32.0754
  内存使用: 0.6821
  能量使用: 0.9944
  推理时间: 2.3205秒

批次 33:
  奖励值: 119.9534
  收益率: 0.2981
  距离: 29.6228
  内存使用: 0.6055
  能量使用: 0.8649
  推理时间: 2.1379秒

批次 34:
  奖励值: 123.7886
  收益率: 0.3085
  距离: 32.1349
  内存使用: 0.7103
  能量使用: 0.9777
  推理时间: 2.2679秒

批次 35:
  奖励值: 114.9688
  收益率: 0.2887
  距离: 29.1298
  内存使用: 0.5854
  能量使用: 0.9471
  推理时间: 2.0556秒

批次 36:
  奖励值: 126.9395
  收益率: 0.3182
  距离: 31.2181
  内存使用: 0.7365
  能量使用: 0.9664
  推理时间: 2.3805秒

批次 37:
  奖励值: 103.9716
  收益率: 0.2600
  距离: 23.9736
  内存使用: 0.8324
  能量使用: 0.8508
  推理时间: 2.0737秒

批次 38:
  奖励值: 109.7016
  收益率: 0.2770
  距离: 26.9764
  内存使用: 0.5806
  能量使用: 0.8397
  推理时间: 1.9478秒

批次 39:
  奖励值: 111.4852
  收益率: 0.2860
  距离: 26.0856
  内存使用: 0.8603
  能量使用: 0.8837
  推理时间: 1.9801秒

批次 40:
  奖励值: 110.4063
  收益率: 0.2724
  距离: 24.7178
  内存使用: 0.5443
  能量使用: 0.9252
  推理时间: 2.0260秒

批次 41:
  奖励值: 109.0723
  收益率: 0.2702
  距离: 23.8866
  内存使用: 0.8826
  能量使用: 0.8860
  推理时间: 1.9398秒

批次 42:
  奖励值: 126.3178
  收益率: 0.3107
  距离: 29.7163
  内存使用: 0.6378
  能量使用: 1.0102
  推理时间: 2.2504秒

批次 43:
  奖励值: 115.4985
  收益率: 0.2963
  距离: 29.4639
  内存使用: 0.6558
  能量使用: 0.8832
  推理时间: 2.0901秒

批次 44:
  奖励值: 106.9462
  收益率: 0.2747
  距离: 29.1473
  内存使用: 0.5311
  能量使用: 0.8291
  推理时间: 1.9068秒

批次 45:
  奖励值: 123.7182
  收益率: 0.3064
  距离: 27.0557
  内存使用: 0.5402
  能量使用: 0.9676
  推理时间: 2.2069秒

批次 46:
  奖励值: 114.9501
  收益率: 0.2912
  距离: 28.6504
  内存使用: 0.5237
  能量使用: 0.9257
  推理时间: 2.0036秒

批次 47:
  奖励值: 111.0219
  收益率: 0.2770
  距离: 27.6933
  内存使用: 0.5517
  能量使用: 0.9112
  推理时间: 1.9701秒

批次 48:
  奖励值: 110.3008
  收益率: 0.2895
  距离: 31.1236
  内存使用: 0.6144
  能量使用: 0.8603
  推理时间: 1.9938秒

批次 49:
  奖励值: 105.2816
  收益率: 0.2625
  距离: 25.7673
  内存使用: 0.8844
  能量使用: 0.7850
  推理时间: 2.0135秒

批次 50:
  奖励值: 131.2496
  收益率: 0.3225
  距离: 33.7998
  内存使用: 0.7724
  能量使用: 1.1285
  推理时间: 2.3879秒

批次 51:
  奖励值: 124.3869
  收益率: 0.3111
  距离: 34.7895
  内存使用: 0.6694
  能量使用: 0.9674
  推理时间: 2.2715秒

批次 52:
  奖励值: 121.1919
  收益率: 0.3003
  距离: 30.1830
  内存使用: 0.6464
  能量使用: 0.9867
  推理时间: 2.0970秒

批次 53:
  奖励值: 119.7257
  收益率: 0.3091
  距离: 30.0531
  内存使用: 0.6264
  能量使用: 0.8690
  推理时间: 2.0607秒

批次 54:
  奖励值: 109.2910
  收益率: 0.2739
  距离: 28.4137
  内存使用: 0.9108
  能量使用: 0.8933
  推理时间: 1.9464秒

批次 55:
  奖励值: 102.0398
  收益率: 0.2584
  距离: 28.4788
  内存使用: 0.8379
  能量使用: 0.8038
  推理时间: 1.8312秒

批次 56:
  奖励值: 108.5079
  收益率: 0.2669
  距离: 24.6558
  内存使用: 0.8456
  能量使用: 0.8827
  推理时间: 1.9393秒

批次 57:
  奖励值: 109.7802
  收益率: 0.2849
  距离: 27.7887
  内存使用: 0.6117
  能量使用: 0.8392
  推理时间: 2.0307秒

批次 58:
  奖励值: 117.1106
  收益率: 0.2935
  距离: 30.1962
  内存使用: 0.8739
  能量使用: 0.9087
  推理时间: 2.0439秒

批次 59:
  奖励值: 108.5982
  收益率: 0.2781
  距离: 30.9811
  内存使用: 0.5735
  能量使用: 0.8491
  推理时间: 1.9288秒

批次 60:
  奖励值: 97.4067
  收益率: 0.2497
  距离: 26.9525
  内存使用: 0.4636
  能量使用: 0.7704
  推理时间: 1.8039秒

批次 61:
  奖励值: 98.8224
  收益率: 0.2466
  距离: 22.4214
  内存使用: 0.8018
  能量使用: 0.7320
  推理时间: 1.6860秒

批次 62:
  奖励值: 127.6863
  收益率: 0.3235
  距离: 34.3399
  内存使用: 0.7189
  能量使用: 0.9684
  推理时间: 2.2934秒

批次 63:
  奖励值: 111.2839
  收益率: 0.2753
  距离: 29.1908
  内存使用: 0.5770
  能量使用: 0.8645
  推理时间: 1.9168秒

批次 64:
  奖励值: 112.8523
  收益率: 0.2885
  距离: 32.0053
  内存使用: 0.5951
  能量使用: 0.8950
  推理时间: 2.0973秒

批次 65:
  奖励值: 107.8548
  收益率: 0.2703
  距离: 28.3305
  内存使用: 0.5072
  能量使用: 0.8528
  推理时间: 1.8777秒

批次 66:
  奖励值: 109.7148
  收益率: 0.2759
  距离: 28.4492
  内存使用: 0.6627
  能量使用: 0.8584
  推理时间: 2.0140秒

批次 67:
  奖励值: 118.9151
  收益率: 0.2950
  距离: 26.7932
  内存使用: 0.5700
  能量使用: 1.0067
  推理时间: 2.1936秒

批次 68:
  奖励值: 101.7541
  收益率: 0.2615
  距离: 29.9291
  内存使用: 0.5100
  能量使用: 0.8526
  推理时间: 1.9114秒

批次 69:
  奖励值: 108.0372
  收益率: 0.2705
  距离: 27.0811
  内存使用: 0.5966
  能量使用: 0.8480
  推理时间: 1.9280秒

批次 70:
  奖励值: 110.6367
  收益率: 0.2767
  距离: 27.2226
  内存使用: 0.5643
  能量使用: 0.9395
  推理时间: 1.9386秒

批次 71:
  奖励值: 120.5953
  收益率: 0.2992
  距离: 29.6846
  内存使用: 0.6103
  能量使用: 0.9145
  推理时间: 2.1127秒

批次 72:
  奖励值: 121.3974
  收益率: 0.3070
  距离: 31.2904
  内存使用: 0.6877
  能量使用: 1.0270
  推理时间: 2.1642秒

批次 73:
  奖励值: 111.6483
  收益率: 0.2901
  距离: 30.8072
  内存使用: 0.5534
  能量使用: 0.9429
  推理时间: 2.0139秒

批次 74:
  奖励值: 125.8955
  收益率: 0.3035
  距离: 30.3688
  内存使用: 0.6764
  能量使用: 0.9637
  推理时间: 2.2313秒

批次 75:
  奖励值: 118.1681
  收益率: 0.2866
  距离: 32.9281
  内存使用: 0.6533
  能量使用: 0.8824
  推理时间: 2.2023秒

批次 76:
  奖励值: 116.2736
  收益率: 0.2955
  距离: 29.8684
  内存使用: 0.5512
  能量使用: 0.8331
  推理时间: 2.0650秒

批次 77:
  奖励值: 107.1633
  收益率: 0.2632
  距离: 28.4275
  内存使用: 0.8481
  能量使用: 0.7977
  推理时间: 1.9741秒

批次 78:
  奖励值: 103.5635
  收益率: 0.2649
  距离: 28.6590
  内存使用: 0.5308
  能量使用: 0.8832
  推理时间: 1.8602秒

批次 79:
  奖励值: 101.0079
  收益率: 0.2554
  距离: 28.0037
  内存使用: 0.5210
  能量使用: 0.8594
  推理时间: 1.8598秒

批次 80:
  奖励值: 113.0947
  收益率: 0.2812
  距离: 29.3801
  内存使用: 0.5395
  能量使用: 0.8910
  推理时间: 2.0549秒

批次 81:
  奖励值: 123.0249
  收益率: 0.3084
  距离: 33.7395
  内存使用: 0.6099
  能量使用: 1.0007
  推理时间: 2.1920秒

批次 82:
  奖励值: 102.2073
  收益率: 0.2569
  距离: 22.8463
  内存使用: 0.4765
  能量使用: 0.8624
  推理时间: 1.7730秒

批次 83:
  奖励值: 113.1900
  收益率: 0.2844
  距离: 28.6135
  内存使用: 0.5726
  能量使用: 0.8338
  推理时间: 1.9770秒

批次 84:
  奖励值: 108.3286
  收益率: 0.2754
  距离: 27.1852
  内存使用: 0.5425
  能量使用: 0.8151
  推理时间: 1.9004秒

批次 85:
  奖励值: 110.0137
  收益率: 0.2767
  距离: 28.7353
  内存使用: 0.6129
  能量使用: 0.8243
  推理时间: 2.0123秒

批次 86:
  奖励值: 107.9139
  收益率: 0.2816
  距离: 28.5603
  内存使用: 0.5329
  能量使用: 0.8788
  推理时间: 2.1355秒

批次 87:
  奖励值: 116.5383
  收益率: 0.3007
  距离: 27.9493
  内存使用: 0.6216
  能量使用: 0.9526
  推理时间: 2.1587秒

批次 88:
  奖励值: 107.6920
  收益率: 0.2739
  距离: 27.4588
  内存使用: 0.5254
  能量使用: 0.8318
  推理时间: 1.9095秒

批次 89:
  奖励值: 107.0078
  收益率: 0.2686
  距离: 28.8569
  内存使用: 0.5462
  能量使用: 0.7813
  推理时间: 1.8454秒

批次 90:
  奖励值: 126.9958
  收益率: 0.3161
  距离: 30.9634
  内存使用: 0.7339
  能量使用: 1.1110
  推理时间: 2.3312秒

批次 91:
  奖励值: 113.0083
  收益率: 0.2886
  距离: 30.7391
  内存使用: 0.5418
  能量使用: 0.9025
  推理时间: 2.0494秒

批次 92:
  奖励值: 110.6450
  收益率: 0.2769
  距离: 26.6586
  内存使用: 0.6006
  能量使用: 0.8507
  推理时间: 1.9755秒

批次 93:
  奖励值: 131.5460
  收益率: 0.3234
  距离: 32.3691
  内存使用: 0.7099
  能量使用: 1.0291
  推理时间: 2.3871秒

批次 94:
  奖励值: 112.1941
  收益率: 0.2767
  距离: 27.9885
  内存使用: 0.5871
  能量使用: 0.8361
  推理时间: 2.0543秒

批次 95:
  奖励值: 105.4709
  收益率: 0.2637
  距离: 28.1464
  内存使用: 0.5138
  能量使用: 0.7980
  推理时间: 1.8338秒

批次 96:
  奖励值: 105.7334
  收益率: 0.2673
  距离: 27.5394
  内存使用: 0.8034
  能量使用: 0.7905
  推理时间: 1.9711秒

批次 97:
  奖励值: 120.9465
  收益率: 0.3019
  距离: 30.9333
  内存使用: 0.6233
  能量使用: 0.9502
  推理时间: 2.1563秒

批次 98:
  奖励值: 103.3369
  收益率: 0.2662
  距离: 27.9098
  内存使用: 0.5599
  能量使用: 0.8565
  推理时间: 1.7755秒

批次 99:
  奖励值: 101.3904
  收益率: 0.2561
  距离: 27.9878
  内存使用: 0.8110
  能量使用: 0.8204
  推理时间: 2.0286秒

批次 100:
  奖励值: 114.5241
  收益率: 0.2910
  距离: 28.6159
  内存使用: 0.5937
  能量使用: 0.9422
  推理时间: 2.0248秒


==================== 总结 ====================
平均收益率: 0.2835
平均能量使用: 0.8827
平均推理时间: 2.0355秒
