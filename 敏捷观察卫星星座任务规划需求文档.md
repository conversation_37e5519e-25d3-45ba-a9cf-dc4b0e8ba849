# 敏捷观察卫星星座任务规划系统需求文档

## 1. 项目概述

### 1.1 项目背景
敏捷观察卫星星座任务规划系统是一个基于深度强化学习的智能任务调度系统，旨在优化多颗敏捷观察卫星的协同任务执行，最大化整个星座的观测收益。

### 1.2 项目目标
- 实现多卫星协同任务规划与调度
- 在满足物理约束条件下最大化系统收益
- 支持大规模任务场景的高效处理
- 提供灵活的星座工作模式配置

## 2. 物理模型约束验证需求

### 2.1 能量约束系统 ✅
**当前实现状态：已支持**

- **能量消耗模型**：
  - 任务执行能量消耗：`POWER_CONSUME = 0.01`
  - 卫星机动能量消耗：`moving_power_cost = moving_time * MOVING_CONSUME_POWER_RATE`
  - 其中 `MOVING_CONSUME_POWER_RATE = 0.01`

- **充电功能**：
  - 在轨充电速率：`ORBITAL_CHARGING_RATE = 0.005`
  - 充电计算：`power_gained = idle_time * ORBITAL_CHARGING_RATE`
  - 能量平衡：`total_power_change = power_gained - moving_power_cost - task_power_consume`

- **能量约束检查**：
  - 初始能量：`args.power_total = 5.0`
  - 动态能量更新：通过 `update_dynamic` 函数实时更新每颗卫星的剩余能量
  - 能量下界约束：`torch.clamp(power_surplus, 0.0, self.power_total)`

### 2.2 时间窗口约束 ✅
**当前实现状态：已支持**

- **时间窗口定义**：
  - 任务开始时间：`StaticData[:, 0, :]`
  - 任务结束时间：`StaticData[:, 2, :]`
  - 任务执行时长：`StaticData[:, 3, :]`，范围 `[0.015, 0.030]`

- **时间窗口检查**：
  - 动态时间窗口标记：`DynamicData[:, 0, :, :]` 
  - 姿态调整稳定时间：`STABILIZATION_TIME = 0.005`
  - 时间窗口掩码：通过 `update_mask` 函数确保任务在有效时间窗口内执行

### 2.3 内存约束系统 ✅
**当前实现状态：已支持**

- **内存消耗模型**：
  - 任务内存消耗：`StaticData[:, 5, :]`，基础消耗 `MEMORY_CONSUME = 0.01`
  - 初始内存容量：`args.memory_total = 0.3`

- **内存约束检查**：
  - 动态内存更新：`memory_surplus = torch.sub(dynamic[:, 2, :, sat_idx], memory_consume)`
  - 内存下界约束：`torch.clamp(memory_surplus, 0.0, self.memory_total)`
  - 内存不足时任务不可执行

## 3. 性能指标需求

### 3.1 主要性能指标 ✅
**当前实现状态：已支持**

#### 3.1.1 Reward（奖励）
- **计算公式**：
  ```python
  reward = REWARD_PROPORTION * total_revenue + 
           DISTANCE_PROPORTION * total_distance + 
           MEMORY_PROPORTION * total_memory + 
           POWER_PROPORTION * total_power
  ```
- **权重配置**：
  - `REWARD_PROPORTION = 1`（收益权重）
  - `DISTANCE_PROPORTION = -0.5`（距离惩罚）
  - `POWER_PROPORTION = -0.5`（能量惩罚）
  - `MEMORY_PROPORTION = -0.5`（内存惩罚）

#### 3.1.2 Revenue Rate（收益率）
- **计算公式**：
  ```python
  revenue_rate = final_total_revenue / (all_possible_revenue + 1e-10)
  ```
- **说明**：已完成任务的总收益与所有任务可能收益的比值

## 4. 代码结构优化需求

### 4.1 现有代码结构分析

当前代码结构已经具备良好的模块化设计，包含以下主要模块：

1. **核心模型模块**：
   - [gpn.py](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/gpn.py) - GPN网络实现
   - [pn.py](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/pn.py) - Pointer网络实现
   - [indrnn/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/indrnn/) - IndRNN网络实现
   - [attention/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/attention/) - 注意力机制实现

2. **任务规划模块**：
   - [single_smp/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/single_smp/) - 单星任务规划实现
   - [constellation_smp/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/constellation_smp/) - 星座任务规划实现

3. **训练与推理模块**：
   - [train.py](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/train.py) - 单星训练主程序
   - [train_constellation.py](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/train_constellation.py) - 星座训练主程序
   - [infer/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/infer/) - 推理模块

4. **验证模块**：
   - [validation/](file:///c%3A/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9%20%282%29/0801single_smp/validation/) - 验证模块

### 4.2 优化建议

#### 4.2.1 引入Transformer架构提升性能
为提升大规模任务场景下的性能，建议引入Transformer架构：

1. **实现Transformer编码器**：
   - 替代现有的LSTM/IndRNN编码器
   - 利用自注意力机制更好地捕捉任务间依赖关系
   - 支持并行计算，提高大规模任务处理效率

2. **优化注意力机制**：
   - 使用多头自注意力机制替代现有的加性注意力
   - 实现更高效的特征提取和融合

3. **改进解码器**：
   - 使用Transformer解码器替代现有的RNN解码器
   - 支持更长序列的处理

#### 4.2.2 代码结构优化

1. **统一模型接口**：
   - 定义统一的模型基类，规范各模型的接口
   - 实现插件化架构，便于添加新模型

2. **模块解耦**：
   - 进一步分离模型定义、训练逻辑和推理逻辑
   - 提高代码复用性和可维护性

3. **配置管理**：
   - 引入配置文件管理超参数
   - 实现命令行参数和配置文件的统一管理

4. **性能监控**：
   - 添加训练过程中的性能监控
   - 实现可视化工具，便于分析模型性能

### 4.3 实施计划

#### 阶段一：Transformer架构设计与实现
- 实现基于Transformer的编码器-解码器架构
- 集成多头自注意力机制
- 验证在小规模任务上的可行性

#### 阶段二：性能优化与测试
- 在大规模任务场景下测试Transformer性能
- 与现有模型进行性能对比
- 根据测试结果优化模型结构和参数

#### 阶段三：代码结构重构
- 重构现有代码结构，提高模块化程度
- 统一模型接口，实现插件化架构
- 完善配置管理和性能监控功能

#### 阶段四：文档完善与部署
- 完善项目文档和使用说明
- 编写详细的API文档
- 准备部署方案和使用指南

## 5. 预期收益

通过以上优化，预期能够获得以下收益：

1. **性能提升**：
   - 大规模任务处理能力显著提升
   - 训练和推理速度加快
   - 模型收敛更快

2. **功能增强**：
   - 更好的任务间依赖关系建模
   - 更强的泛化能力
   - 更高的任务规划质量

3. **代码质量改善**：
   - 更清晰的代码结构
   - 更好的可维护性和可扩展性
   - 更完善的文档和测试