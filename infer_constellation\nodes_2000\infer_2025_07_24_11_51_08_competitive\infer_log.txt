推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 142.4893
  收益率: 0.1785
  距离: 32.6188
  内存使用: 0.7552
  能量使用: 1.0474
  推理时间: 2.4380秒

批次 2:
  奖励值: 138.2166
  收益率: 0.1734
  距离: 32.7665
  内存使用: 0.7417
  能量使用: 1.0587
  推理时间: 2.4278秒

批次 3:
  奖励值: 120.4637
  收益率: 0.1517
  距离: 31.4261
  内存使用: 0.6148
  能量使用: 0.8317
  推理时间: 2.1047秒

批次 4:
  奖励值: 109.8420
  收益率: 0.1359
  距离: 27.5859
  内存使用: 0.9054
  能量使用: 0.8246
  推理时间: 1.9503秒

批次 5:
  奖励值: 112.0295
  收益率: 0.1422
  距离: 26.4767
  内存使用: 0.9102
  能量使用: 0.9541
  推理时间: 1.9832秒

批次 6:
  奖励值: 114.8520
  收益率: 0.1430
  距离: 27.9504
  内存使用: 0.8494
  能量使用: 0.8962
  推理时间: 2.0228秒

批次 7:
  奖励值: 115.2722
  收益率: 0.1434
  距离: 28.4498
  内存使用: 0.9156
  能量使用: 0.8900
  推理时间: 2.1144秒

批次 8:
  奖励值: 117.1550
  收益率: 0.1467
  距离: 29.5533
  内存使用: 0.9079
  能量使用: 0.8288
  推理时间: 2.0721秒

批次 9:
  奖励值: 137.0242
  收益率: 0.1652
  距离: 26.3295
  内存使用: 0.6898
  能量使用: 0.9323
  推理时间: 2.3103秒

批次 10:
  奖励值: 115.1908
  收益率: 0.1445
  距离: 27.8053
  内存使用: 0.9067
  能量使用: 0.9064
  推理时间: 2.0587秒

批次 11:
  奖励值: 135.0307
  收益率: 0.1707
  距离: 30.6175
  内存使用: 0.7260
  能量使用: 0.9951
  推理时间: 2.3408秒

批次 12:
  奖励值: 122.2576
  收益率: 0.1529
  距离: 28.5362
  内存使用: 0.6527
  能量使用: 0.8957
  推理时间: 2.0617秒

批次 13:
  奖励值: 136.0051
  收益率: 0.1692
  距离: 32.1024
  内存使用: 0.6865
  能量使用: 1.0008
  推理时间: 2.3683秒

批次 14:
  奖励值: 119.2106
  收益率: 0.1489
  距离: 27.1468
  内存使用: 0.9062
  能量使用: 0.9258
  推理时间: 2.1139秒

批次 15:
  奖励值: 124.2084
  收益率: 0.1539
  距离: 28.4362
  内存使用: 0.6451
  能量使用: 0.9018
  推理时间: 2.0813秒

批次 16:
  奖励值: 124.9168
  收益率: 0.1564
  距离: 33.1121
  内存使用: 0.6934
  能量使用: 0.9608
  推理时间: 2.2514秒

批次 17:
  奖励值: 130.8429
  收益率: 0.1616
  距离: 28.4648
  内存使用: 0.6704
  能量使用: 0.9790
  推理时间: 2.2433秒

批次 18:
  奖励值: 121.0901
  收益率: 0.1520
  距离: 29.7475
  内存使用: 0.9018
  能量使用: 0.9706
  推理时间: 2.1599秒

批次 19:
  奖励值: 131.5653
  收益率: 0.1672
  距离: 34.7991
  内存使用: 0.6937
  能量使用: 0.9766
  推理时间: 2.3591秒

批次 20:
  奖励值: 124.4261
  收益率: 0.1596
  距离: 33.6806
  内存使用: 0.8500
  能量使用: 1.0170
  推理时间: 2.2219秒

批次 21:
  奖励值: 125.2308
  收益率: 0.1580
  距离: 28.3029
  内存使用: 0.6346
  能量使用: 1.0009
  推理时间: 2.1695秒

批次 22:
  奖励值: 142.0428
  收益率: 0.1808
  距离: 34.0595
  内存使用: 0.7634
  能量使用: 1.0913
  推理时间: 2.4294秒

批次 23:
  奖励值: 124.6256
  收益率: 0.1551
  距离: 31.2316
  内存使用: 0.7078
  能量使用: 1.0094
  推理时间: 2.2066秒

批次 24:
  奖励值: 121.5052
  收益率: 0.1514
  距离: 30.4150
  内存使用: 0.6590
  能量使用: 0.9156
  推理时间: 2.0948秒

批次 25:
  奖励值: 120.9119
  收益率: 0.1491
  距离: 25.8474
  内存使用: 0.6351
  能量使用: 0.8936
  推理时间: 2.0974秒

批次 26:
  奖励值: 128.7215
  收益率: 0.1624
  距离: 28.9938
  内存使用: 0.6751
  能量使用: 0.8880
  推理时间: 2.2307秒

批次 27:
  奖励值: 113.5862
  收益率: 0.1427
  距离: 29.2686
  内存使用: 0.8571
  能量使用: 0.9020
  推理时间: 2.0333秒

批次 28:
  奖励值: 115.4643
  收益率: 0.1440
  距离: 31.1327
  内存使用: 0.9014
  能量使用: 0.8373
  推理时间: 2.0618秒

批次 29:
  奖励值: 142.7194
  收益率: 0.1783
  距离: 32.3314
  内存使用: 0.7366
  能量使用: 1.0931
  推理时间: 2.4332秒

批次 30:
  奖励值: 111.8017
  收益率: 0.1401
  距离: 28.4404
  内存使用: 0.9048
  能量使用: 0.8515
  推理时间: 1.9730秒

批次 31:
  奖励值: 134.8338
  收益率: 0.1691
  距离: 31.8195
  内存使用: 0.6880
  能量使用: 1.0249
  推理时间: 2.3148秒

批次 32:
  奖励值: 117.4304
  收益率: 0.1476
  距离: 30.2438
  内存使用: 0.9064
  能量使用: 0.9889
  推理时间: 2.1198秒

批次 33:
  奖励值: 121.9002
  收益率: 0.1538
  距离: 31.6298
  内存使用: 0.8935
  能量使用: 0.8762
  推理时间: 2.1441秒

批次 34:
  奖励值: 119.5317
  收益率: 0.1483
  距离: 28.3236
  内存使用: 0.9056
  能量使用: 0.9074
  推理时间: 2.1485秒

批次 35:
  奖励值: 134.2513
  收益率: 0.1645
  距离: 32.0008
  内存使用: 0.7211
  能量使用: 0.9754
  推理时间: 2.3647秒

批次 36:
  奖励值: 134.4531
  收益率: 0.1646
  距离: 29.1877
  内存使用: 0.6989
  能量使用: 1.0070
  推理时间: 2.2590秒

批次 37:
  奖励值: 128.8977
  收益率: 0.1632
  距离: 30.9579
  内存使用: 0.6368
  能量使用: 1.0099
  推理时间: 2.2274秒

批次 38:
  奖励值: 137.5261
  收益率: 0.1756
  距离: 33.2863
  内存使用: 0.8270
  能量使用: 1.0345
  推理时间: 2.4222秒

批次 39:
  奖励值: 118.6785
  收益率: 0.1503
  距离: 30.1893
  内存使用: 0.5773
  能量使用: 0.8762
  推理时间: 2.0896秒

批次 40:
  奖励值: 112.1860
  收益率: 0.1385
  距离: 29.3753
  内存使用: 0.6185
  能量使用: 0.8341
  推理时间: 2.0237秒

批次 41:
  奖励值: 134.9758
  收益率: 0.1675
  距离: 32.1181
  内存使用: 0.7040
  能量使用: 1.0262
  推理时间: 2.3208秒

批次 42:
  奖励值: 128.0869
  收益率: 0.1598
  距离: 31.9514
  内存使用: 0.7230
  能量使用: 0.9517
  推理时间: 2.2941秒

批次 43:
  奖励值: 116.2645
  收益率: 0.1440
  距离: 28.0635
  内存使用: 0.9104
  能量使用: 0.8971
  推理时间: 2.0250秒

批次 44:
  奖励值: 116.8194
  收益率: 0.1470
  距离: 30.1515
  内存使用: 0.9128
  能量使用: 0.8969
  推理时间: 2.1469秒

批次 45:
  奖励值: 116.2693
  收益率: 0.1459
  距离: 29.7877
  内存使用: 0.8962
  能量使用: 0.9774
  推理时间: 2.1013秒

批次 46:
  奖励值: 109.3753
  收益率: 0.1425
  距离: 29.5060
  内存使用: 0.9093
  能量使用: 0.8341
  推理时间: 1.9385秒

批次 47:
  奖励值: 128.3701
  收益率: 0.1581
  距离: 29.6338
  内存使用: 0.6384
  能量使用: 0.9876
  推理时间: 2.2383秒

批次 48:
  奖励值: 135.5293
  收益率: 0.1673
  距离: 30.3352
  内存使用: 0.7682
  能量使用: 1.0380
  推理时间: 2.3378秒

批次 49:
  奖励值: 126.0210
  收益率: 0.1609
  距离: 33.3212
  内存使用: 0.6286
  能量使用: 1.0081
  推理时间: 2.2385秒

批次 50:
  奖励值: 139.5669
  收益率: 0.1747
  距离: 35.7398
  内存使用: 0.7407
  能量使用: 0.9856
  推理时间: 2.4961秒

批次 51:
  奖励值: 119.3055
  收益率: 0.1537
  距离: 33.3519
  内存使用: 0.9121
  能量使用: 0.9250
  推理时间: 2.1633秒

批次 52:
  奖励值: 132.3753
  收益率: 0.1620
  距离: 29.9188
  内存使用: 0.7220
  能量使用: 1.0085
  推理时间: 2.2747秒

批次 53:
  奖励值: 123.8126
  收益率: 0.1558
  距离: 32.1334
  内存使用: 0.9096
  能量使用: 0.9318
  推理时间: 2.2244秒

批次 54:
  奖励值: 125.6496
  收益率: 0.1573
  距离: 28.4803
  内存使用: 0.5903
  能量使用: 1.0302
  推理时间: 2.1994秒

批次 55:
  奖励值: 115.2262
  收益率: 0.1456
  距离: 29.3692
  内存使用: 0.9097
  能量使用: 0.8686
  推理时间: 2.0615秒

批次 56:
  奖励值: 112.1126
  收益率: 0.1406
  距离: 26.8894
  内存使用: 0.9118
  能量使用: 0.7736
  推理时间: 1.9739秒

批次 57:
  奖励值: 121.4977
  收益率: 0.1521
  距离: 30.1548
  内存使用: 0.9104
  能量使用: 0.9394
  推理时间: 2.1517秒

批次 58:
  奖励值: 116.8082
  收益率: 0.1455
  距离: 27.3918
  内存使用: 0.9072
  能量使用: 0.8845
  推理时间: 2.0659秒

批次 59:
  奖励值: 125.5187
  收益率: 0.1587
  距离: 31.8995
  内存使用: 0.9143
  能量使用: 0.9487
  推理时间: 2.2403秒

批次 60:
  奖励值: 126.7063
  收益率: 0.1611
  距离: 30.0364
  内存使用: 0.6149
  能量使用: 0.9359
  推理时间: 2.2059秒

批次 61:
  奖励值: 121.3909
  收益率: 0.1511
  距离: 29.8993
  内存使用: 0.6374
  能量使用: 0.8816
  推理时间: 2.0772秒

批次 62:
  奖励值: 127.0176
  收益率: 0.1576
  距离: 31.7066
  内存使用: 0.6919
  能量使用: 0.9663
  推理时间: 2.1956秒

批次 63:
  奖励值: 126.9083
  收益率: 0.1588
  距离: 31.2659
  内存使用: 0.9056
  能量使用: 1.0118
  推理时间: 2.2949秒

批次 64:
  奖励值: 128.7651
  收益率: 0.1628
  距离: 32.3748
  内存使用: 0.6997
  能量使用: 0.9953
  推理时间: 2.3196秒

批次 65:
  奖励值: 141.9687
  收益率: 0.1710
  距离: 31.7737
  内存使用: 0.7835
  能量使用: 1.1055
  推理时间: 2.4452秒

批次 66:
  奖励值: 111.5707
  收益率: 0.1416
  距离: 27.8901
  内存使用: 0.9140
  能量使用: 0.8545
  推理时间: 1.9169秒

批次 67:
  奖励值: 132.3013
  收益率: 0.1672
  距离: 35.2085
  内存使用: 0.7649
  能量使用: 1.0239
  推理时间: 2.3059秒

批次 68:
  奖励值: 124.0449
  收益率: 0.1534
  距离: 27.6177
  内存使用: 0.9043
  能量使用: 0.9882
  推理时间: 2.1864秒

批次 69:
  奖励值: 131.3858
  收益率: 0.1674
  距离: 34.9660
  内存使用: 0.7133
  能量使用: 0.9490
  推理时间: 2.3644秒

批次 70:
  奖励值: 112.9517
  收益率: 0.1398
  距离: 27.9321
  内存使用: 0.9099
  能量使用: 0.8546
  推理时间: 1.9954秒

批次 71:
  奖励值: 114.2016
  收益率: 0.1415
  距离: 26.5984
  内存使用: 0.9087
  能量使用: 0.8896
  推理时间: 1.9631秒

批次 72:
  奖励值: 129.1564
  收益率: 0.1589
  距离: 30.1941
  内存使用: 0.6588
  能量使用: 1.0343
  推理时间: 2.2390秒

批次 73:
  奖励值: 123.6648
  收益率: 0.1544
  距离: 30.6348
  内存使用: 0.8948
  能量使用: 0.9407
  推理时间: 2.2059秒

批次 74:
  奖励值: 118.8293
  收益率: 0.1475
  距离: 28.8877
  内存使用: 0.9205
  能量使用: 0.9332
  推理时间: 2.1207秒

批次 75:
  奖励值: 113.2971
  收益率: 0.1421
  距离: 28.8779
  内存使用: 0.9051
  能量使用: 0.8229
  推理时间: 1.9907秒

批次 76:
  奖励值: 137.3042
  收益率: 0.1712
  距离: 32.2305
  内存使用: 0.7086
  能量使用: 1.0523
  推理时间: 2.3733秒

批次 77:
  奖励值: 111.4242
  收益率: 0.1403
  距离: 28.0130
  内存使用: 0.9204
  能量使用: 0.8181
  推理时间: 1.9956秒

批次 78:
  奖励值: 117.6221
  收益率: 0.1504
  距离: 30.2425
  内存使用: 0.6580
  能量使用: 0.8553
  推理时间: 2.0733秒

批次 79:
  奖励值: 125.3585
  收益率: 0.1607
  距离: 31.7308
  内存使用: 0.6449
  能量使用: 0.9072
  推理时间: 2.1947秒

批次 80:
  奖励值: 122.8611
  收益率: 0.1549
  距离: 31.8840
  内存使用: 0.8865
  能量使用: 0.9073
  推理时间: 2.2036秒

批次 81:
  奖励值: 140.6040
  收益率: 0.1760
  距离: 33.2045
  内存使用: 0.8070
  能量使用: 0.9805
  推理时间: 2.4116秒

批次 82:
  奖励值: 137.0090
  收益率: 0.1731
  距离: 35.9903
  内存使用: 0.7069
  能量使用: 1.0849
  推理时间: 2.3702秒

批次 83:
  奖励值: 122.4698
  收益率: 0.1528
  距离: 30.0459
  内存使用: 0.6647
  能量使用: 0.9498
  推理时间: 2.1360秒

批次 84:
  奖励值: 119.5589
  收益率: 0.1488
  距离: 26.9122
  内存使用: 0.9152
  能量使用: 0.8548
  推理时间: 2.1390秒

批次 85:
  奖励值: 139.7615
  收益率: 0.1729
  距离: 29.0934
  内存使用: 0.7552
  能量使用: 1.0282
  推理时间: 2.4346秒

批次 86:
  奖励值: 118.2721
  收益率: 0.1452
  距离: 27.3712
  内存使用: 0.6593
  能量使用: 0.8252
  推理时间: 2.0389秒

批次 87:
  奖励值: 116.0275
  收益率: 0.1484
  距离: 31.9594
  内存使用: 0.8842
  能量使用: 0.9014
  推理时间: 2.0458秒

批次 88:
  奖励值: 127.5150
  收益率: 0.1544
  距离: 27.0599
  内存使用: 0.9146
  能量使用: 0.9954
  推理时间: 2.1914秒

批次 89:
  奖励值: 107.2851
  收益率: 0.1337
  距离: 30.2392
  内存使用: 0.9096
  能量使用: 0.8810
  推理时间: 1.9639秒

批次 90:
  奖励值: 118.2677
  收益率: 0.1460
  距离: 25.8054
  内存使用: 0.9076
  能量使用: 0.9517
  推理时间: 2.0610秒

批次 91:
  奖励值: 125.4507
  收益率: 0.1552
  距离: 29.1987
  内存使用: 0.6279
  能量使用: 1.0719
  推理时间: 2.2881秒

批次 92:
  奖励值: 133.0441
  收益率: 0.1629
  距离: 31.8285
  内存使用: 0.7221
  能量使用: 0.9611
  推理时间: 2.4788秒

批次 93:
  奖励值: 139.7260
  收益率: 0.1719
  距离: 32.7199
  内存使用: 0.7848
  能量使用: 1.0434
  推理时间: 2.5447秒

批次 94:
  奖励值: 119.8131
  收益率: 0.1529
  距离: 31.1118
  内存使用: 0.9117
  能量使用: 0.9503
  推理时间: 2.2308秒

批次 95:
  奖励值: 123.2127
  收益率: 0.1566
  距离: 32.2292
  内存使用: 0.5976
  能量使用: 0.9477
  推理时间: 2.2128秒

批次 96:
  奖励值: 114.6470
  收益率: 0.1440
  距离: 27.0752
  内存使用: 0.9041
  能量使用: 0.9267
  推理时间: 4.2492秒

批次 97:
  奖励值: 116.0312
  收益率: 0.1445
  距离: 25.9274
  内存使用: 0.9127
  能量使用: 0.8975
  推理时间: 1.9480秒

批次 98:
  奖励值: 156.0717
  收益率: 0.1940
  距离: 37.8478
  内存使用: 0.8265
  能量使用: 1.1824
  推理时间: 2.6478秒

批次 99:
  奖励值: 134.1378
  收益率: 0.1671
  距离: 32.2125
  内存使用: 0.6873
  能量使用: 1.0246
  推理时间: 2.3036秒

批次 100:
  奖励值: 114.1648
  收益率: 0.1444
  距离: 29.0935
  内存使用: 0.9063
  能量使用: 0.8705
  推理时间: 2.0249秒


==================== 总结 ====================
平均收益率: 0.1559
平均能量使用: 0.9478
平均推理时间: 2.2141秒
