推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 110.8498
  收益率: 0.2295
  距离: 30.0648
  内存使用: 0.5062
  能量使用: 0.9293
  推理时间: 1.9614秒

批次 2:
  奖励值: 123.2497
  收益率: 0.2460
  距离: 31.9118
  内存使用: 0.5806
  能量使用: 1.0081
  推理时间: 2.2012秒

批次 3:
  奖励值: 124.2499
  收益率: 0.2479
  距离: 31.1036
  内存使用: 0.6587
  能量使用: 0.9379
  推理时间: 2.1516秒

批次 4:
  奖励值: 119.3540
  收益率: 0.2388
  距离: 29.9618
  内存使用: 0.6143
  能量使用: 0.9160
  推理时间: 2.1469秒

批次 5:
  奖励值: 112.1760
  收益率: 0.2227
  距离: 28.9109
  内存使用: 0.9136
  能量使用: 0.8234
  推理时间: 2.0479秒

批次 6:
  奖励值: 118.9834
  收益率: 0.2336
  距离: 27.6842
  内存使用: 0.6351
  能量使用: 0.9138
  推理时间: 2.0936秒

批次 7:
  奖励值: 109.3775
  收益率: 0.2219
  距离: 30.1726
  内存使用: 0.5877
  能量使用: 0.8847
  推理时间: 1.9479秒

批次 8:
  奖励值: 128.1034
  收益率: 0.2574
  距离: 33.1750
  内存使用: 0.6814
  能量使用: 0.9368
  推理时间: 2.2915秒

批次 9:
  奖励值: 119.8449
  收益率: 0.2388
  距离: 29.1254
  内存使用: 0.8708
  能量使用: 0.9139
  推理时间: 2.1223秒

批次 10:
  奖励值: 126.0294
  收益率: 0.2560
  距离: 35.9808
  内存使用: 0.6848
  能量使用: 1.0757
  推理时间: 2.1799秒

批次 11:
  奖励值: 113.8243
  收益率: 0.2287
  距离: 27.6848
  内存使用: 0.5686
  能量使用: 0.8978
  推理时间: 1.9788秒

批次 12:
  奖励值: 126.1757
  收益率: 0.2496
  距离: 34.9362
  内存使用: 0.7047
  能量使用: 1.0076
  推理时间: 2.2250秒

批次 13:
  奖励值: 105.3468
  收益率: 0.2130
  距离: 26.1718
  内存使用: 0.5162
  能量使用: 0.8185
  推理时间: 1.8235秒

批次 14:
  奖励值: 122.7305
  收益率: 0.2462
  距离: 34.2640
  内存使用: 0.6999
  能量使用: 0.9750
  推理时间: 2.1507秒

批次 15:
  奖励值: 101.1183
  收益率: 0.2016
  距离: 23.9615
  内存使用: 0.8149
  能量使用: 0.7869
  推理时间: 1.8117秒

批次 16:
  奖励值: 116.5419
  收益率: 0.2350
  距离: 28.8574
  内存使用: 0.5867
  能量使用: 0.8720
  推理时间: 2.0260秒

批次 17:
  奖励值: 125.6326
  收益率: 0.2540
  距离: 29.9518
  内存使用: 0.6501
  能量使用: 0.9328
  推理时间: 2.1961秒

批次 18:
  奖励值: 110.3520
  收益率: 0.2187
  距离: 27.6160
  内存使用: 0.9070
  能量使用: 0.9729
  推理时间: 1.9218秒

批次 19:
  奖励值: 141.0348
  收益率: 0.2734
  距离: 35.8103
  内存使用: 0.7897
  能量使用: 1.1303
  推理时间: 2.3901秒

批次 20:
  奖励值: 115.6610
  收益率: 0.2296
  距离: 29.4515
  内存使用: 0.4776
  能量使用: 0.9488
  推理时间: 2.0093秒

批次 21:
  奖励值: 114.2194
  收益率: 0.2277
  距离: 26.6617
  内存使用: 0.5411
  能量使用: 0.9583
  推理时间: 1.9362秒

批次 22:
  奖励值: 111.8097
  收益率: 0.2211
  距离: 29.4673
  内存使用: 0.8891
  能量使用: 0.8242
  推理时间: 2.0247秒

批次 23:
  奖励值: 121.7673
  收益率: 0.2488
  距离: 34.4792
  内存使用: 0.6130
  能量使用: 0.9733
  推理时间: 2.1664秒

批次 24:
  奖励值: 116.6100
  收益率: 0.2344
  距离: 31.8056
  内存使用: 0.6272
  能量使用: 0.9472
  推理时间: 2.0630秒

批次 25:
  奖励值: 116.8947
  收益率: 0.2344
  距离: 28.7353
  内存使用: 0.6461
  能量使用: 0.8292
  推理时间: 2.0227秒

批次 26:
  奖励值: 129.7198
  收益率: 0.2615
  距离: 33.2061
  内存使用: 0.8045
  能量使用: 0.9498
  推理时间: 2.2379秒

批次 27:
  奖励值: 105.7504
  收益率: 0.2142
  距离: 28.5415
  内存使用: 0.5393
  能量使用: 0.8184
  推理时间: 1.8571秒

批次 28:
  奖励值: 110.2341
  收益率: 0.2141
  距离: 26.5688
  内存使用: 0.6056
  能量使用: 0.7573
  推理时间: 1.8820秒

批次 29:
  奖励值: 124.0697
  收益率: 0.2495
  距离: 33.1124
  内存使用: 0.6288
  能量使用: 0.9193
  推理时间: 2.1382秒

批次 30:
  奖励值: 104.5732
  收益率: 0.2091
  距离: 26.9075
  内存使用: 0.8309
  能量使用: 0.9151
  推理时间: 1.8684秒

批次 31:
  奖励值: 123.3499
  收益率: 0.2495
  距离: 28.2524
  内存使用: 0.5238
  能量使用: 0.9240
  推理时间: 2.1552秒

批次 32:
  奖励值: 106.8032
  收益率: 0.2132
  距离: 26.7472
  内存使用: 0.9014
  能量使用: 0.9449
  推理时间: 1.9100秒

批次 33:
  奖励值: 117.0284
  收益率: 0.2331
  距离: 30.0983
  内存使用: 0.5498
  能量使用: 0.8950
  推理时间: 2.0444秒

批次 34:
  奖励值: 118.9855
  收益率: 0.2407
  距离: 29.5932
  内存使用: 0.9045
  能量使用: 0.9312
  推理时间: 2.1136秒

批次 35:
  奖励值: 129.2483
  收益率: 0.2576
  距离: 31.4242
  内存使用: 0.7035
  能量使用: 0.9757
  推理时间: 2.2133秒

批次 36:
  奖励值: 135.9986
  收益率: 0.2797
  距离: 36.7918
  内存使用: 0.7573
  能量使用: 1.0537
  推理时间: 2.3973秒

批次 37:
  奖励值: 116.6894
  收益率: 0.2319
  距离: 27.0901
  内存使用: 0.6598
  能量使用: 0.8712
  推理时间: 2.0239秒

批次 38:
  奖励值: 115.3930
  收益率: 0.2275
  距离: 29.1617
  内存使用: 0.5433
  能量使用: 0.9091
  推理时间: 1.9813秒

批次 39:
  奖励值: 119.0332
  收益率: 0.2349
  距离: 26.2504
  内存使用: 0.6461
  能量使用: 0.8370
  推理时间: 2.0690秒

批次 40:
  奖励值: 103.6549
  收益率: 0.2103
  距离: 27.7826
  内存使用: 0.9032
  能量使用: 0.8044
  推理时间: 1.8439秒

批次 41:
  奖励值: 113.6691
  收益率: 0.2263
  距离: 26.3273
  内存使用: 0.5114
  能量使用: 0.8230
  推理时间: 2.0018秒

批次 42:
  奖励值: 116.5838
  收益率: 0.2283
  距离: 27.1341
  内存使用: 0.6255
  能量使用: 0.8729
  推理时间: 1.9953秒

批次 43:
  奖励值: 116.5393
  收益率: 0.2386
  距离: 33.2952
  内存使用: 0.9130
  能量使用: 0.9132
  推理时间: 2.0697秒

批次 44:
  奖励值: 135.5490
  收益率: 0.2710
  距离: 33.4097
  内存使用: 0.6989
  能量使用: 0.9933
  推理时间: 2.3218秒

批次 45:
  奖励值: 105.0308
  收益率: 0.2168
  距离: 30.0734
  内存使用: 0.8382
  能量使用: 0.8268
  推理时间: 1.8719秒

批次 46:
  奖励值: 121.5059
  收益率: 0.2451
  距离: 30.1195
  内存使用: 0.9041
  能量使用: 0.9646
  推理时间: 2.6011秒

批次 47:
  奖励值: 101.0864
  收益率: 0.1999
  距离: 24.8064
  内存使用: 0.8435
  能量使用: 0.7773
  推理时间: 1.7793秒

批次 48:
  奖励值: 117.8847
  收益率: 0.2378
  距离: 27.6959
  内存使用: 0.6289
  能量使用: 0.9058
  推理时间: 2.0395秒

批次 49:
  奖励值: 118.3298
  收益率: 0.2401
  距离: 31.7998
  内存使用: 0.5840
  能量使用: 0.9566
  推理时间: 2.0653秒

批次 50:
  奖励值: 115.9208
  收益率: 0.2354
  距离: 28.5345
  内存使用: 0.5625
  能量使用: 0.8613
  推理时间: 2.0106秒

批次 51:
  奖励值: 109.8096
  收益率: 0.2217
  距离: 28.4336
  内存使用: 0.8792
  能量使用: 0.8679
  推理时间: 1.9483秒

批次 52:
  奖励值: 113.2349
  收益率: 0.2284
  距离: 27.8420
  内存使用: 0.6149
  能量使用: 0.8734
  推理时间: 1.9602秒

批次 53:
  奖励值: 118.0266
  收益率: 0.2341
  距离: 27.9656
  内存使用: 0.6550
  能量使用: 0.9262
  推理时间: 2.0574秒

批次 54:
  奖励值: 118.5142
  收益率: 0.2464
  距离: 31.6464
  内存使用: 0.5804
  能量使用: 0.9448
  推理时间: 2.0565秒

批次 55:
  奖励值: 125.6384
  收益率: 0.2502
  距离: 30.4834
  内存使用: 0.6696
  能量使用: 0.9701
  推理时间: 2.2049秒

批次 56:
  奖励值: 114.1165
  收益率: 0.2320
  距离: 31.9331
  内存使用: 0.6205
  能量使用: 0.8133
  推理时间: 1.9978秒

批次 57:
  奖励值: 114.3356
  收益率: 0.2278
  距离: 30.1422
  内存使用: 0.5398
  能量使用: 0.8468
  推理时间: 1.9698秒

批次 58:
  奖励值: 115.7854
  收益率: 0.2342
  距离: 27.1103
  内存使用: 0.5786
  能量使用: 0.9404
  推理时间: 2.0078秒

批次 59:
  奖励值: 125.3639
  收益率: 0.2447
  距离: 31.5810
  内存使用: 0.6583
  能量使用: 1.0327
  推理时间: 2.1856秒

批次 60:
  奖励值: 113.6030
  收益率: 0.2313
  距离: 25.5973
  内存使用: 0.5612
  能量使用: 0.8354
  推理时间: 1.9515秒

批次 61:
  奖励值: 111.6925
  收益率: 0.2199
  距离: 30.6523
  内存使用: 0.8933
  能量使用: 0.8694
  推理时间: 1.9953秒

批次 62:
  奖励值: 132.8183
  收益率: 0.2624
  距离: 29.6487
  内存使用: 0.7435
  能量使用: 0.9712
  推理时间: 2.2480秒

批次 63:
  奖励值: 131.0195
  收益率: 0.2607
  距离: 30.0309
  内存使用: 0.7292
  能量使用: 1.0008
  推理时间: 2.2432秒

批次 64:
  奖励值: 135.7023
  收益率: 0.2698
  距离: 36.5434
  内存使用: 0.6649
  能量使用: 0.9870
  推理时间: 2.3558秒

批次 65:
  奖励值: 121.7540
  收益率: 0.2406
  距离: 29.1689
  内存使用: 0.5984
  能量使用: 0.9248
  推理时间: 2.1104秒

批次 66:
  奖励值: 108.2913
  收益率: 0.2248
  距离: 30.3112
  内存使用: 0.8606
  能量使用: 0.8456
  推理时间: 1.9526秒

批次 67:
  奖励值: 117.6656
  收益率: 0.2343
  距离: 29.9723
  内存使用: 0.6407
  能量使用: 0.8558
  推理时间: 2.1213秒

批次 68:
  奖励值: 119.4201
  收益率: 0.2455
  距离: 29.1527
  内存使用: 0.7056
  能量使用: 0.9015
  推理时间: 2.1142秒

批次 69:
  奖励值: 123.3584
  收益率: 0.2497
  距离: 31.9042
  内存使用: 0.6533
  能量使用: 0.9257
  推理时间: 2.1967秒

批次 70:
  奖励值: 113.0550
  收益率: 0.2231
  距离: 31.3900
  内存使用: 0.8653
  能量使用: 0.8346
  推理时间: 2.0270秒

批次 71:
  奖励值: 128.8887
  收益率: 0.2651
  距离: 36.9048
  内存使用: 0.6391
  能量使用: 1.0785
  推理时间: 2.3250秒

批次 72:
  奖励值: 131.1548
  收益率: 0.2625
  距离: 32.6152
  内存使用: 0.6775
  能量使用: 0.9977
  推理时间: 2.2670秒

批次 73:
  奖励值: 112.6883
  收益率: 0.2250
  距离: 30.4341
  内存使用: 0.6156
  能量使用: 0.7737
  推理时间: 2.1431秒

批次 74:
  奖励值: 110.2205
  收益率: 0.2212
  距离: 25.9620
  内存使用: 0.5601
  能量使用: 0.8369
  推理时间: 2.0140秒

批次 75:
  奖励值: 112.2023
  收益率: 0.2241
  距离: 29.5652
  内存使用: 0.9117
  能量使用: 0.9184
  推理时间: 1.9953秒

批次 76:
  奖励值: 123.1158
  收益率: 0.2486
  距离: 31.5551
  内存使用: 0.6857
  能量使用: 0.9274
  推理时间: 2.1641秒

批次 77:
  奖励值: 113.1235
  收益率: 0.2338
  距离: 30.2500
  内存使用: 0.5740
  能量使用: 0.9289
  推理时间: 2.0846秒

批次 78:
  奖励值: 120.7485
  收益率: 0.2398
  距离: 27.0852
  内存使用: 0.6799
  能量使用: 0.9378
  推理时间: 2.0823秒

批次 79:
  奖励值: 117.9237
  收益率: 0.2330
  距离: 28.8477
  内存使用: 0.6024
  能量使用: 0.9025
  推理时间: 2.1252秒

批次 80:
  奖励值: 127.6503
  收益率: 0.2576
  距离: 32.6341
  内存使用: 0.7066
  能量使用: 0.9587
  推理时间: 2.2101秒

批次 81:
  奖励值: 113.3586
  收益率: 0.2314
  距离: 29.0840
  内存使用: 0.9151
  能量使用: 0.8837
  推理时间: 2.0001秒

批次 82:
  奖励值: 123.9527
  收益率: 0.2407
  距离: 28.6844
  内存使用: 0.6463
  能量使用: 0.9638
  推理时间: 2.1505秒

批次 83:
  奖励值: 114.3069
  收益率: 0.2256
  距离: 25.2228
  内存使用: 0.5591
  能量使用: 0.8044
  推理时间: 1.9766秒

批次 84:
  奖励值: 119.2962
  收益率: 0.2455
  距离: 31.9849
  内存使用: 0.9030
  能量使用: 0.9196
  推理时间: 2.1846秒

批次 85:
  奖励值: 113.9921
  收益率: 0.2304
  距离: 27.4191
  内存使用: 0.5306
  能量使用: 0.8661
  推理时间: 1.9735秒

批次 86:
  奖励值: 125.1896
  收益率: 0.2508
  距离: 32.0172
  内存使用: 0.6684
  能量使用: 0.9351
  推理时间: 2.1024秒

批次 87:
  奖励值: 116.2902
  收益率: 0.2373
  距离: 30.2555
  内存使用: 0.5817
  能量使用: 0.9824
  推理时间: 2.0579秒

批次 88:
  奖励值: 122.4489
  收益率: 0.2534
  距离: 31.4641
  内存使用: 0.6544
  能量使用: 0.9838
  推理时间: 2.3987秒

批次 89:
  奖励值: 122.6320
  收益率: 0.2446
  距离: 31.4724
  内存使用: 0.5734
  能量使用: 0.8902
  推理时间: 2.1942秒

批次 90:
  奖励值: 107.5193
  收益率: 0.2169
  距离: 28.7718
  内存使用: 0.8772
  能量使用: 0.9123
  推理时间: 1.9015秒

批次 91:
  奖励值: 112.3943
  收益率: 0.2198
  距离: 26.6632
  内存使用: 0.9023
  能量使用: 0.8457
  推理时间: 1.9672秒

批次 92:
  奖励值: 119.2216
  收益率: 0.2414
  距离: 29.8205
  内存使用: 0.6208
  能量使用: 0.9156
  推理时间: 2.0467秒

批次 93:
  奖励值: 126.2545
  收益率: 0.2500
  距离: 28.6089
  内存使用: 0.7174
  能量使用: 0.9648
  推理时间: 2.1340秒

批次 94:
  奖励值: 106.0839
  收益率: 0.2116
  距离: 24.8054
  内存使用: 0.8403
  能量使用: 0.7864
  推理时间: 1.8656秒

批次 95:
  奖励值: 122.3929
  收益率: 0.2386
  距离: 32.2380
  内存使用: 0.6423
  能量使用: 0.9337
  推理时间: 2.0888秒

批次 96:
  奖励值: 121.8701
  收益率: 0.2432
  距离: 34.0321
  内存使用: 0.7051
  能量使用: 0.9225
  推理时间: 2.1453秒

批次 97:
  奖励值: 116.0450
  收益率: 0.2369
  距离: 30.4242
  内存使用: 0.6177
  能量使用: 0.8680
  推理时间: 2.1307秒

批次 98:
  奖励值: 110.1180
  收益率: 0.2155
  距离: 25.6236
  内存使用: 0.9100
  能量使用: 0.8303
  推理时间: 1.9192秒

批次 99:
  奖励值: 136.3150
  收益率: 0.2759
  距离: 37.8941
  内存使用: 0.7855
  能量使用: 1.0904
  推理时间: 2.4650秒

批次 100:
  奖励值: 141.4836
  收益率: 0.2814
  距离: 34.3335
  内存使用: 0.7635
  能量使用: 1.1357
  推理时间: 2.5129秒


==================== 总结 ====================
平均收益率: 0.2376
平均能量使用: 0.9148
平均推理时间: 2.0871秒
