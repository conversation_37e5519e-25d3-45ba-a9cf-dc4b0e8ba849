推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_23_17_04_44

批次 1:
  奖励值: 108.7820
  收益率: 0.3585
  距离: 25.0580
  内存使用: 0.5554
  能量使用: 0.8226
  推理时间: 2.3136秒

批次 2:
  奖励值: 106.6276
  收益率: 0.3565
  距离: 27.7727
  内存使用: 0.5481
  能量使用: 0.8239
  推理时间: 2.0634秒

批次 3:
  奖励值: 107.2269
  收益率: 0.3616
  距离: 27.3051
  内存使用: 0.5839
  能量使用: 0.8571
  推理时间: 2.3393秒

批次 4:
  奖励值: 93.3375
  收益率: 0.3180
  距离: 23.9817
  内存使用: 0.6836
  能量使用: 0.6597
  推理时间: 2.0907秒

批次 5:
  奖励值: 105.1756
  收益率: 0.3556
  距离: 23.2678
  内存使用: 0.5280
  能量使用: 0.8531
  推理时间: 2.1661秒

批次 6:
  奖励值: 110.2304
  收益率: 0.3806
  距离: 30.2369
  内存使用: 0.6099
  能量使用: 0.8924
  推理时间: 2.3110秒

批次 7:
  奖励值: 93.6375
  收益率: 0.3173
  距离: 26.2731
  内存使用: 0.4988
  能量使用: 0.7531
  推理时间: 1.9923秒

批次 8:
  奖励值: 104.7579
  收益率: 0.3486
  距离: 26.3204
  内存使用: 0.5206
  能量使用: 0.8431
  推理时间: 2.1256秒

批次 9:
  奖励值: 95.5420
  收益率: 0.3324
  距离: 22.6798
  内存使用: 0.4167
  能量使用: 0.8194
  推理时间: 1.8963秒

批次 10:
  奖励值: 97.8747
  收益率: 0.3203
  距离: 25.7397
  内存使用: 0.5047
  能量使用: 0.8062
  推理时间: 2.0936秒

批次 11:
  奖励值: 98.4571
  收益率: 0.3382
  距离: 23.4615
  内存使用: 0.5263
  能量使用: 0.6980
  推理时间: 2.0378秒

批次 12:
  奖励值: 104.2711
  收益率: 0.3385
  距离: 21.9405
  内存使用: 0.5376
  能量使用: 0.7797
  推理时间: 1.8673秒

批次 13:
  奖励值: 113.8386
  收益率: 0.3710
  距离: 27.8840
  内存使用: 0.5895
  能量使用: 0.8492
  推理时间: 2.2297秒

批次 14:
  奖励值: 112.0042
  收益率: 0.3877
  距离: 31.2581
  内存使用: 0.5997
  能量使用: 0.9339
  推理时间: 2.1840秒

批次 15:
  奖励值: 95.8525
  收益率: 0.3228
  距离: 25.9215
  内存使用: 0.4103
  能量使用: 0.7631
  推理时间: 1.7500秒

批次 16:
  奖励值: 104.6408
  收益率: 0.3440
  距离: 26.3855
  内存使用: 0.5095
  能量使用: 0.8234
  推理时间: 2.0543秒

批次 17:
  奖励值: 93.2007
  收益率: 0.3001
  距离: 23.3434
  内存使用: 0.7914
  能量使用: 0.6638
  推理时间: 1.8200秒

批次 18:
  奖励值: 98.1272
  收益率: 0.3295
  距离: 23.8168
  内存使用: 0.7416
  能量使用: 0.7872
  推理时间: 1.8480秒

批次 19:
  奖励值: 98.0667
  收益率: 0.3192
  距离: 25.2086
  内存使用: 0.4966
  能量使用: 0.7613
  推理时间: 1.8558秒

批次 20:
  奖励值: 106.9141
  收益率: 0.3594
  距离: 29.2918
  内存使用: 0.5492
  能量使用: 0.9308
  推理时间: 2.0620秒

批次 21:
  奖励值: 111.8460
  收益率: 0.3632
  距离: 29.0654
  内存使用: 0.5101
  能量使用: 0.9325
  推理时间: 2.1776秒

批次 22:
  奖励值: 93.1180
  收益率: 0.3148
  距离: 25.8457
  内存使用: 0.7475
  能量使用: 0.7707
  推理时间: 1.8707秒

批次 23:
  奖励值: 101.7125
  收益率: 0.3354
  距离: 23.8818
  内存使用: 0.5244
  能量使用: 0.7688
  推理时间: 1.9727秒

批次 24:
  奖励值: 97.7727
  收益率: 0.3294
  距离: 25.3945
  内存使用: 0.4979
  能量使用: 0.7622
  推理时间: 1.8608秒

批次 25:
  奖励值: 112.8714
  收益率: 0.3720
  距离: 32.9163
  内存使用: 0.6344
  能量使用: 0.9066
  推理时间: 2.1774秒

批次 26:
  奖励值: 103.9663
  收益率: 0.3528
  距离: 27.4163
  内存使用: 0.5270
  能量使用: 0.9241
  推理时间: 1.9704秒

批次 27:
  奖励值: 109.2778
  收益率: 0.3535
  距离: 27.8684
  内存使用: 0.5073
  能量使用: 0.8686
  推理时间: 2.0819秒

批次 28:
  奖励值: 108.9539
  收益率: 0.3552
  距离: 25.5408
  内存使用: 0.5482
  能量使用: 0.8031
  推理时间: 2.0513秒

批次 29:
  奖励值: 94.0185
  收益率: 0.3189
  距离: 26.9529
  内存使用: 0.4941
  能量使用: 0.7160
  推理时间: 1.8054秒

批次 30:
  奖励值: 113.6240
  收益率: 0.3676
  距离: 30.2353
  内存使用: 0.5854
  能量使用: 0.9314
  推理时间: 2.1677秒

批次 31:
  奖励值: 93.5404
  收益率: 0.3092
  距离: 25.3430
  内存使用: 0.7648
  能量使用: 0.8115
  推理时间: 1.8561秒

批次 32:
  奖励值: 104.7025
  收益率: 0.3406
  距离: 27.1494
  内存使用: 0.4595
  能量使用: 0.7860
  推理时间: 1.9379秒

批次 33:
  奖励值: 114.7435
  收益率: 0.3805
  距离: 28.0924
  内存使用: 0.5742
  能量使用: 0.8463
  推理时间: 2.3280秒

批次 34:
  奖励值: 102.1585
  收益率: 0.3489
  距离: 25.1898
  内存使用: 0.8709
  能量使用: 0.8222
  推理时间: 2.0176秒

批次 35:
  奖励值: 112.4006
  收益率: 0.3697
  距离: 25.3382
  内存使用: 0.5620
  能量使用: 0.8987
  推理时间: 2.1475秒

批次 36:
  奖励值: 109.6246
  收益率: 0.3671
  距离: 26.9117
  内存使用: 0.5230
  能量使用: 0.9161
  推理时间: 2.0613秒

批次 37:
  奖励值: 105.8295
  收益率: 0.3573
  距离: 26.2931
  内存使用: 0.5890
  能量使用: 0.8588
  推理时间: 2.0479秒

批次 38:
  奖励值: 104.3002
  收益率: 0.3488
  距离: 29.2343
  内存使用: 0.5006
  能量使用: 0.8647
  推理时间: 2.0184秒

批次 39:
  奖励值: 110.4930
  收益率: 0.3621
  距离: 24.4139
  内存使用: 0.5466
  能量使用: 0.8536
  推理时间: 2.0907秒

批次 40:
  奖励值: 100.5872
  收益率: 0.3330
  距离: 22.8646
  内存使用: 0.4594
  能量使用: 0.7015
  推理时间: 1.9135秒

批次 41:
  奖励值: 110.1643
  收益率: 0.3754
  距离: 29.4000
  内存使用: 0.6039
  能量使用: 0.8676
  推理时间: 2.0725秒

批次 42:
  奖励值: 113.9840
  收益率: 0.3610
  距离: 27.9323
  内存使用: 0.6242
  能量使用: 0.8756
  推理时间: 2.1617秒

批次 43:
  奖励值: 97.9618
  收益率: 0.3241
  距离: 23.9956
  内存使用: 0.7600
  能量使用: 0.7208
  推理时间: 1.8663秒

批次 44:
  奖励值: 111.5127
  收益率: 0.3663
  距离: 28.3039
  内存使用: 0.5758
  能量使用: 0.8948
  推理时间: 2.2058秒

批次 45:
  奖励值: 107.2508
  收益率: 0.3645
  距离: 28.2938
  内存使用: 0.5025
  能量使用: 0.9020
  推理时间: 2.0476秒

批次 46:
  奖励值: 101.2635
  收益率: 0.3344
  距离: 27.1724
  内存使用: 0.5701
  能量使用: 0.7306
  推理时间: 1.9004秒

批次 47:
  奖励值: 89.1773
  收益率: 0.2967
  距离: 23.9245
  内存使用: 0.4456
  能量使用: 0.6752
  推理时间: 1.7106秒

批次 48:
  奖励值: 101.2773
  收益率: 0.3391
  距离: 24.6055
  内存使用: 0.4604
  能量使用: 0.7796
  推理时间: 2.3035秒

批次 49:
  奖励值: 96.4024
  收益率: 0.3214
  距离: 25.4124
  内存使用: 0.4820
  能量使用: 0.7007
  推理时间: 1.9160秒

批次 50:
  奖励值: 85.9261
  收益率: 0.2798
  距离: 23.0619
  内存使用: 0.3932
  能量使用: 0.6927
  推理时间: 1.7073秒

批次 51:
  奖励值: 96.4022
  收益率: 0.3341
  距离: 23.1589
  内存使用: 0.4105
  能量使用: 0.8263
  推理时间: 1.8872秒

批次 52:
  奖励值: 102.6956
  收益率: 0.3375
  距离: 25.4594
  内存使用: 0.4805
  能量使用: 0.8688
  推理时间: 1.8833秒

批次 53:
  奖励值: 104.4646
  收益率: 0.3544
  距离: 27.9635
  内存使用: 0.4594
  能量使用: 0.8478
  推理时间: 2.1129秒

批次 54:
  奖励值: 98.2332
  收益率: 0.3314
  距离: 24.8469
  内存使用: 0.5171
  能量使用: 0.7695
  推理时间: 1.8455秒

批次 55:
  奖励值: 102.0677
  收益率: 0.3522
  距离: 27.9596
  内存使用: 0.4903
  能量使用: 0.8148
  推理时间: 2.0696秒

批次 56:
  奖励值: 90.3991
  收益率: 0.3042
  距离: 22.1268
  内存使用: 0.4065
  能量使用: 0.6685
  推理时间: 1.7815秒

批次 57:
  奖励值: 108.0779
  收益率: 0.3545
  距离: 25.3718
  内存使用: 0.5104
  能量使用: 0.7621
  推理时间: 2.0658秒

批次 58:
  奖励值: 112.2996
  收益率: 0.3698
  距离: 30.8298
  内存使用: 0.6325
  能量使用: 0.9383
  推理时间: 2.2686秒

批次 59:
  奖励值: 100.2843
  收益率: 0.3297
  距离: 24.8822
  内存使用: 0.5626
  能量使用: 0.7866
  推理时间: 2.0313秒

批次 60:
  奖励值: 103.1231
  收益率: 0.3577
  距离: 26.0935
  内存使用: 0.4437
  能量使用: 0.8167
  推理时间: 2.0092秒

批次 61:
  奖励值: 105.1571
  收益率: 0.3505
  距离: 26.7139
  内存使用: 0.5297
  能量使用: 0.8422
  推理时间: 2.0133秒

批次 62:
  奖励值: 101.4621
  收益率: 0.3365
  距离: 22.5432
  内存使用: 0.5015
  能量使用: 0.8311
  推理时间: 1.8698秒

批次 63:
  奖励值: 99.2058
  收益率: 0.3433
  距离: 29.2475
  内存使用: 0.4797
  能量使用: 0.8612
  推理时间: 2.0198秒

批次 64:
  奖励值: 102.1582
  收益率: 0.3394
  距离: 25.9967
  内存使用: 0.4846
  能量使用: 0.8023
  推理时间: 2.0074秒

批次 65:
  奖励值: 104.6506
  收益率: 0.3654
  距离: 29.5565
  内存使用: 0.5024
  能量使用: 0.8399
  推理时间: 2.0700秒

批次 66:
  奖励值: 97.1754
  收益率: 0.3377
  距离: 30.6087
  内存使用: 0.4786
  能量使用: 0.7619
  推理时间: 1.9894秒

批次 67:
  奖励值: 104.7071
  收益率: 0.3573
  距离: 27.9025
  内存使用: 0.4556
  能量使用: 0.8278
  推理时间: 2.0466秒

批次 68:
  奖励值: 108.6153
  收益率: 0.3589
  距离: 27.8164
  内存使用: 0.5330
  能量使用: 0.8200
  推理时间: 2.0420秒

批次 69:
  奖励值: 116.0120
  收益率: 0.3761
  距离: 26.6181
  内存使用: 0.6078
  能量使用: 0.9501
  推理时间: 2.1838秒

批次 70:
  奖励值: 105.0013
  收益率: 0.3567
  距离: 29.3392
  内存使用: 0.5691
  能量使用: 0.8891
  推理时间: 1.9929秒

批次 71:
  奖励值: 108.6575
  收益率: 0.3632
  距离: 25.7321
  内存使用: 0.4903
  能量使用: 0.8626
  推理时间: 2.0876秒

批次 72:
  奖励值: 110.3757
  收益率: 0.3614
  距离: 27.5306
  内存使用: 0.5207
  能量使用: 0.7972
  推理时间: 2.1232秒

批次 73:
  奖励值: 103.3658
  收益率: 0.3442
  距离: 26.7977
  内存使用: 0.5102
  能量使用: 0.7604
  推理时间: 2.1910秒

批次 74:
  奖励值: 106.1223
  收益率: 0.3591
  距离: 27.1987
  内存使用: 0.5635
  能量使用: 0.7933
  推理时间: 2.2080秒

批次 75:
  奖励值: 99.7620
  收益率: 0.3420
  距离: 27.7093
  内存使用: 0.5318
  能量使用: 0.8348
  推理时间: 2.0508秒

批次 76:
  奖励值: 113.9002
  收益率: 0.3808
  距离: 28.1393
  内存使用: 0.6039
  能量使用: 0.8563
  推理时间: 2.1076秒

批次 77:
  奖励值: 102.2699
  收益率: 0.3318
  距离: 24.7679
  内存使用: 0.4521
  能量使用: 0.8402
  推理时间: 1.8129秒

批次 78:
  奖励值: 117.5203
  收益率: 0.3856
  距离: 31.5736
  内存使用: 0.5876
  能量使用: 0.9982
  推理时间: 2.0736秒

批次 79:
  奖励值: 109.5673
  收益率: 0.3654
  距离: 32.9387
  内存使用: 0.5912
  能量使用: 0.9355
  推理时间: 1.9876秒

批次 80:
  奖励值: 91.4216
  收益率: 0.3109
  距离: 25.1683
  内存使用: 0.4536
  能量使用: 0.7348
  推理时间: 1.5971秒

批次 81:
  奖励值: 103.0449
  收益率: 0.3558
  距离: 26.2041
  内存使用: 0.5184
  能量使用: 0.7876
  推理时间: 1.8725秒

批次 82:
  奖励值: 94.5271
  收益率: 0.3155
  距离: 21.5448
  内存使用: 0.4861
  能量使用: 0.7149
  推理时间: 1.6771秒

批次 83:
  奖励值: 101.6115
  收益率: 0.3473
  距离: 24.8224
  内存使用: 0.5347
  能量使用: 0.8198
  推理时间: 1.8409秒

批次 84:
  奖励值: 95.5311
  收益率: 0.3168
  距离: 26.4058
  内存使用: 0.5493
  能量使用: 0.7827
  推理时间: 1.7481秒

批次 85:
  奖励值: 101.4922
  收益率: 0.3440
  距离: 26.5437
  内存使用: 0.5467
  能量使用: 0.7252
  推理时间: 1.8235秒

批次 86:
  奖励值: 85.3804
  收益率: 0.2854
  距离: 22.1983
  内存使用: 0.3550
  能量使用: 0.6859
  推理时间: 1.5091秒

批次 87:
  奖励值: 113.6402
  收益率: 0.3811
  距离: 30.7254
  内存使用: 0.6379
  能量使用: 0.9564
  推理时间: 2.0449秒

批次 88:
  奖励值: 104.3584
  收益率: 0.3462
  距离: 26.4054
  内存使用: 0.4855
  能量使用: 0.7922
  推理时间: 1.8787秒

批次 89:
  奖励值: 112.2671
  收益率: 0.3798
  距离: 28.3650
  内存使用: 0.6007
  能量使用: 0.9001
  推理时间: 2.0325秒

批次 90:
  奖励值: 107.4869
  收益率: 0.3579
  距离: 25.6007
  内存使用: 0.5912
  能量使用: 0.8341
  推理时间: 1.8696秒

批次 91:
  奖励值: 91.6799
  收益率: 0.3011
  距离: 23.9486
  内存使用: 0.4439
  能量使用: 0.6477
  推理时间: 1.5885秒

批次 92:
  奖励值: 101.5511
  收益率: 0.3287
  距离: 27.4430
  内存使用: 0.8451
  能量使用: 0.8096
  推理时间: 1.8924秒

批次 93:
  奖励值: 102.9255
  收益率: 0.3310
  距离: 25.8551
  内存使用: 0.5074
  能量使用: 0.7866
  推理时间: 1.8392秒

批次 94:
  奖励值: 105.4125
  收益率: 0.3649
  距离: 24.6099
  内存使用: 0.5369
  能量使用: 0.8283
  推理时间: 1.8811秒

批次 95:
  奖励值: 109.5856
  收益率: 0.3763
  距离: 30.7619
  内存使用: 0.6187
  能量使用: 0.8482
  推理时间: 1.9417秒

批次 96:
  奖励值: 105.5833
  收益率: 0.3588
  距离: 28.2479
  内存使用: 0.4935
  能量使用: 0.8920
  推理时间: 1.8501秒

批次 97:
  奖励值: 108.5523
  收益率: 0.3666
  距离: 25.6065
  内存使用: 0.5169
  能量使用: 0.7719
  推理时间: 1.9004秒

批次 98:
  奖励值: 109.3617
  收益率: 0.3687
  距离: 29.9752
  内存使用: 0.6396
  能量使用: 0.8617
  推理时间: 2.0139秒

批次 99:
  奖励值: 99.5366
  收益率: 0.3227
  距离: 24.6166
  内存使用: 0.5481
  能量使用: 0.7620
  推理时间: 1.7574秒

批次 100:
  奖励值: 116.2612
  收益率: 0.3719
  距离: 25.5942
  内存使用: 0.6141
  能量使用: 0.8692
  推理时间: 2.1026秒


==================== 总结 ====================
平均收益率: 0.3461
平均能量使用: 0.8172
平均推理时间: 1.9904秒
