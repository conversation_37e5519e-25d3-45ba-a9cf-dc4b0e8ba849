推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_13_19_22_42

批次 1:
  奖励值: 130.5433
  收益率: 0.2684
  距离: 33.4272
  内存使用: 0.6351
  能量使用: 1.0511
  推理时间: 2.4549秒

批次 2:
  奖励值: 126.3033
  收益率: 0.2520
  距离: 32.4477
  内存使用: 0.6732
  能量使用: 1.0725
  推理时间: 2.3430秒

批次 3:
  奖励值: 113.2624
  收益率: 0.2252
  距离: 27.0945
  内存使用: 0.9050
  能量使用: 0.8757
  推理时间: 2.1333秒

批次 4:
  奖励值: 130.5117
  收益率: 0.2610
  距离: 32.6482
  内存使用: 0.6946
  能量使用: 1.0337
  推理时间: 2.5549秒

批次 5:
  奖励值: 144.8141
  收益率: 0.2824
  距离: 31.9188
  内存使用: 0.8290
  能量使用: 1.0240
  推理时间: 2.7243秒

批次 6:
  奖励值: 142.5715
  收益率: 0.2833
  距离: 36.9765
  内存使用: 0.7770
  能量使用: 1.1034
  推理时间: 2.6497秒

批次 7:
  奖励值: 123.0018
  收益率: 0.2452
  距离: 28.9698
  内存使用: 0.6660
  能量使用: 0.9606
  推理时间: 2.4758秒

批次 8:
  奖励值: 134.4230
  收益率: 0.2698
  距离: 34.4160
  内存使用: 0.7259
  能量使用: 1.0120
  推理时间: 2.5145秒

批次 9:
  奖励值: 127.0182
  收益率: 0.2542
  距离: 32.2979
  内存使用: 0.6838
  能量使用: 0.9819
  推理时间: 2.5023秒

批次 10:
  奖励值: 128.4438
  收益率: 0.2544
  距离: 29.3369
  内存使用: 0.7517
  能量使用: 1.0236
  推理时间: 2.3529秒

批次 11:
  奖励值: 121.2635
  收益率: 0.2439
  距离: 29.6549
  内存使用: 0.6396
  能量使用: 0.9785
  推理时间: 2.2160秒

批次 12:
  奖励值: 115.7641
  收益率: 0.2265
  距离: 28.8275
  内存使用: 0.9158
  能量使用: 0.9284
  推理时间: 2.1839秒

批次 13:
  奖励值: 115.8411
  收益率: 0.2366
  距离: 31.4643
  内存使用: 0.5707
  能量使用: 0.8828
  推理时间: 2.1371秒

批次 14:
  奖励值: 124.0342
  收益率: 0.2473
  距离: 32.9505
  内存使用: 0.6276
  能量使用: 0.9534
  推理时间: 2.3018秒

批次 15:
  奖励值: 118.6723
  收益率: 0.2369
  距离: 28.9876
  内存使用: 0.6264
  能量使用: 0.8414
  推理时间: 2.1041秒

批次 16:
  奖励值: 117.5860
  收益率: 0.2364
  距离: 28.3106
  内存使用: 0.6316
  能量使用: 0.8859
  推理时间: 2.0671秒

批次 17:
  奖励值: 134.2196
  收益率: 0.2739
  距离: 34.7913
  内存使用: 0.7038
  能量使用: 1.0657
  推理时间: 2.5161秒

批次 18:
  奖励值: 124.2459
  收益率: 0.2442
  距离: 29.2041
  内存使用: 0.6878
  能量使用: 1.0164
  推理时间: 2.2139秒

批次 19:
  奖励值: 145.2958
  收益率: 0.2802
  距离: 35.2419
  内存使用: 0.8063
  能量使用: 1.1601
  推理时间: 2.6845秒

批次 20:
  奖励值: 124.9397
  收益率: 0.2492
  距离: 33.0658
  内存使用: 0.5886
  能量使用: 1.0662
  推理时间: 2.3586秒

批次 21:
  奖励值: 116.5152
  收益率: 0.2361
  距离: 31.1030
  内存使用: 0.9054
  能量使用: 0.9611
  推理时间: 2.2155秒

批次 22:
  奖励值: 137.7527
  收益率: 0.2693
  距离: 33.1797
  内存使用: 0.6970
  能量使用: 1.0178
  推理时间: 2.4739秒

批次 23:
  奖励值: 132.4030
  收益率: 0.2662
  距离: 32.5539
  内存使用: 0.7768
  能量使用: 1.0632
  推理时间: 2.3865秒

批次 24:
  奖励值: 126.6175
  收益率: 0.2515
  距离: 31.1215
  内存使用: 0.6598
  能量使用: 0.9837
  推理时间: 2.2126秒

批次 25:
  奖励值: 128.5817
  收益率: 0.2600
  距离: 33.9592
  内存使用: 0.7126
  能量使用: 0.9648
  推理时间: 2.3096秒

批次 26:
  奖励值: 127.6966
  收益率: 0.2591
  距离: 34.6467
  内存使用: 0.7873
  能量使用: 0.9727
  推理时间: 2.3137秒

批次 27:
  奖励值: 140.3446
  收益率: 0.2796
  距离: 32.5074
  内存使用: 0.7635
  能量使用: 1.0399
  推理时间: 2.5294秒

批次 28:
  奖励值: 118.3074
  收益率: 0.2314
  距离: 30.3278
  内存使用: 0.6231
  能量使用: 0.8342
  推理时间: 2.1392秒

批次 29:
  奖励值: 136.0957
  收益率: 0.2712
  距离: 33.4372
  内存使用: 0.7410
  能量使用: 1.0303
  推理时间: 2.4357秒

批次 30:
  奖励值: 122.8453
  收益率: 0.2466
  距离: 33.0396
  内存使用: 0.6589
  能量使用: 1.0108
  推理时间: 2.2543秒

批次 31:
  奖励值: 145.0045
  收益率: 0.2959
  距离: 35.9590
  内存使用: 0.7187
  能量使用: 1.1133
  推理时间: 2.7208秒

批次 32:
  奖励值: 110.6642
  收益率: 0.2213
  距离: 28.2854
  内存使用: 0.8961
  能量使用: 0.8900
  推理时间: 2.0491秒

批次 33:
  奖励值: 138.2164
  收益率: 0.2753
  距离: 35.4071
  内存使用: 0.7061
  能量使用: 1.0539
  推理时间: 2.5484秒

批次 34:
  奖励值: 137.9116
  收益率: 0.2786
  距离: 34.0747
  内存使用: 0.7383
  能量使用: 1.1639
  推理时间: 2.6763秒

批次 35:
  奖励值: 126.5183
  收益率: 0.2534
  距离: 32.1021
  内存使用: 0.6680
  能量使用: 0.9934
  推理时间: 2.5289秒

批次 36:
  奖励值: 130.0805
  收益率: 0.2669
  距离: 34.4685
  内存使用: 0.7335
  能量使用: 1.0370
  推理时间: 2.3743秒

批次 37:
  奖励值: 134.9161
  收益率: 0.2658
  距离: 28.7937
  内存使用: 0.7403
  能量使用: 1.0336
  推理时间: 2.4251秒

批次 38:
  奖励值: 142.8261
  收益率: 0.2798
  距离: 33.8969
  内存使用: 0.7351
  能量使用: 1.1158
  推理时间: 2.5965秒

批次 39:
  奖励值: 117.5954
  收益率: 0.2356
  距离: 29.6180
  内存使用: 0.9107
  能量使用: 0.8805
  推理时间: 2.1578秒

批次 40:
  奖励值: 123.0383
  收益率: 0.2472
  距离: 30.7429
  内存使用: 0.6494
  能量使用: 0.9311
  推理时间: 2.2566秒

批次 41:
  奖励值: 127.0012
  收益率: 0.2526
  距离: 28.9759
  内存使用: 0.6752
  能量使用: 0.9464
  推理时间: 2.2503秒

批次 42:
  奖励值: 122.9128
  收益率: 0.2421
  距离: 30.1912
  内存使用: 0.6452
  能量使用: 0.9380
  推理时间: 2.1673秒

批次 43:
  奖励值: 109.0229
  收益率: 0.2199
  距离: 27.3875
  内存使用: 0.9173
  能量使用: 0.8495
  推理时间: 2.0080秒

批次 44:
  奖励值: 134.4985
  收益率: 0.2673
  距离: 31.2423
  内存使用: 0.7225
  能量使用: 1.0380
  推理时间: 2.4209秒

批次 45:
  奖励值: 127.6934
  收益率: 0.2602
  距离: 33.0176
  内存使用: 0.7267
  能量使用: 1.0689
  推理时间: 2.3158秒

批次 46:
  奖励值: 122.3827
  收益率: 0.2455
  距离: 29.0546
  内存使用: 0.6385
  能量使用: 0.9416
  推理时间: 2.1243秒

批次 47:
  奖励值: 130.9581
  收益率: 0.2591
  距离: 32.7535
  内存使用: 0.7660
  能量使用: 0.9644
  推理时间: 2.3687秒

批次 48:
  奖励值: 121.0087
  收益率: 0.2426
  距离: 26.8138
  内存使用: 0.6358
  能量使用: 0.9328
  推理时间: 2.1430秒

批次 49:
  奖励值: 134.4544
  收益率: 0.2701
  距离: 33.0476
  内存使用: 0.7550
  能量使用: 1.0886
  推理时间: 2.4203秒

批次 50:
  奖励值: 123.0042
  收益率: 0.2510
  距离: 31.6788
  内存使用: 0.6049
  能量使用: 0.9078
  推理时间: 2.2266秒

批次 51:
  奖励值: 137.6492
  收益率: 0.2731
  距离: 30.6816
  内存使用: 0.7119
  能量使用: 1.1079
  推理时间: 2.4369秒

批次 52:
  奖励值: 116.6425
  收益率: 0.2364
  距离: 29.7000
  内存使用: 0.9113
  能量使用: 0.9037
  推理时间: 2.1366秒

批次 53:
  奖励值: 120.8904
  收益率: 0.2442
  距离: 33.5257
  内存使用: 0.7407
  能量使用: 0.9528
  推理时间: 2.1698秒

批次 54:
  奖励值: 124.6276
  收益率: 0.2590
  距离: 33.1389
  内存使用: 0.6475
  能量使用: 0.9583
  推理时间: 2.2295秒

批次 55:
  奖励值: 124.1054
  收益率: 0.2502
  距离: 33.5448
  内存使用: 0.6986
  能量使用: 0.9457
  推理时间: 2.2394秒

批次 56:
  奖励值: 122.2262
  收益率: 0.2445
  距离: 29.6118
  内存使用: 0.6480
  能量使用: 0.8877
  推理时间: 2.1656秒

批次 57:
  奖励值: 110.8589
  收益率: 0.2222
  距离: 30.2679
  内存使用: 0.9073
  能量使用: 0.9068
  推理时间: 2.0275秒

批次 58:
  奖励值: 133.9217
  收益率: 0.2736
  距离: 34.4096
  内存使用: 0.6782
  能量使用: 1.0689
  推理时间: 2.4557秒

批次 59:
  奖励值: 131.5894
  收益率: 0.2565
  距离: 32.8805
  内存使用: 0.7049
  能量使用: 1.0358
  推理时间: 2.3329秒

批次 60:
  奖励值: 125.9934
  收益率: 0.2597
  距离: 31.7931
  内存使用: 0.6595
  能量使用: 0.9606
  推理时间: 2.2715秒

批次 61:
  奖励值: 116.6887
  收益率: 0.2286
  距离: 30.8643
  内存使用: 0.9068
  能量使用: 0.8514
  推理时间: 2.1241秒

批次 62:
  奖励值: 126.0865
  收益率: 0.2504
  距离: 29.5463
  内存使用: 0.7154
  能量使用: 0.9651
  推理时间: 2.3404秒

批次 63:
  奖励值: 127.4364
  收益率: 0.2550
  距离: 30.9067
  内存使用: 0.6875
  能量使用: 0.9329
  推理时间: 2.3200秒

批次 64:
  奖励值: 133.5908
  收益率: 0.2656
  距离: 35.9601
  内存使用: 0.6863
  能量使用: 0.9721
  推理时间: 2.5211秒

批次 65:
  奖励值: 122.8941
  收益率: 0.2423
  距离: 28.8993
  内存使用: 0.6305
  能量使用: 0.8820
  推理时间: 2.3051秒

批次 66:
  奖励值: 131.3460
  收益率: 0.2682
  距离: 32.1283
  内存使用: 0.7524
  能量使用: 1.0274
  推理时间: 2.6738秒

批次 67:
  奖励值: 123.5790
  收益率: 0.2466
  距离: 32.0485
  内存使用: 0.6587
  能量使用: 0.9347
  推理时间: 2.2934秒

批次 68:
  奖励值: 118.4703
  收益率: 0.2462
  距离: 31.7480
  内存使用: 0.7035
  能量使用: 1.0078
  推理时间: 2.2068秒

批次 69:
  奖励值: 133.6479
  收益率: 0.2672
  距离: 30.7832
  内存使用: 0.7293
  能量使用: 0.9915
  推理时间: 2.3903秒

批次 70:
  奖励值: 123.8863
  收益率: 0.2393
  距离: 28.7061
  内存使用: 0.6345
  能量使用: 0.8867
  推理时间: 2.2007秒

批次 71:
  奖励值: 124.4490
  收益率: 0.2543
  距离: 33.6138
  内存使用: 0.8918
  能量使用: 0.9963
  推理时间: 2.4608秒

批次 72:
  奖励值: 120.2894
  收益率: 0.2408
  距离: 29.5398
  内存使用: 0.8938
  能量使用: 0.9876
  推理时间: 2.3231秒

批次 73:
  奖励值: 118.1449
  收益率: 0.2348
  距离: 30.6001
  内存使用: 0.6085
  能量使用: 0.9016
  推理时间: 2.2387秒

批次 74:
  奖励值: 137.9032
  收益率: 0.2814
  距离: 37.5880
  内存使用: 0.7425
  能量使用: 1.0631
  推理时间: 2.6635秒

批次 75:
  奖励值: 125.4533
  收益率: 0.2482
  距离: 30.7812
  内存使用: 0.7059
  能量使用: 1.0063
  推理时间: 2.2809秒

批次 76:
  奖励值: 115.8821
  收益率: 0.2326
  距离: 27.7955
  内存使用: 0.9071
  能量使用: 0.9331
  推理时间: 2.4349秒

批次 77:
  奖励值: 133.6485
  收益率: 0.2748
  距离: 34.1165
  内存使用: 0.7584
  能量使用: 1.0281
  推理时间: 2.5263秒

批次 78:
  奖励值: 131.0934
  收益率: 0.2634
  距离: 32.8740
  内存使用: 0.6949
  能量使用: 1.0442
  推理时间: 2.5875秒

批次 79:
  奖励值: 118.5498
  收益率: 0.2340
  距离: 28.8081
  内存使用: 0.6314
  能量使用: 0.8608
  推理时间: 2.3694秒

批次 80:
  奖励值: 106.1955
  收益率: 0.2154
  距离: 28.0114
  内存使用: 0.9105
  能量使用: 0.8872
  推理时间: 2.0092秒

批次 81:
  奖励值: 129.4289
  收益率: 0.2614
  距离: 30.4158
  内存使用: 0.7018
  能量使用: 1.0245
  推理时间: 2.4408秒

批次 82:
  奖励值: 121.0587
  收益率: 0.2375
  距离: 30.4493
  内存使用: 0.9137
  能量使用: 0.9196
  推理时间: 2.3155秒

批次 83:
  奖励值: 128.2946
  收益率: 0.2509
  距离: 25.5613
  内存使用: 0.6737
  能量使用: 0.9757
  推理时间: 2.3436秒

批次 84:
  奖励值: 127.3422
  收益率: 0.2630
  距离: 35.4282
  内存使用: 0.6868
  能量使用: 1.0006
  推理时间: 2.5528秒

批次 85:
  奖励值: 117.8225
  收益率: 0.2391
  距离: 29.3384
  内存使用: 0.5245
  能量使用: 0.8920
  推理时间: 2.4070秒

批次 86:
  奖励值: 145.5132
  收益率: 0.2934
  距离: 39.2703
  内存使用: 0.8123
  能量使用: 1.0910
  推理时间: 2.7553秒

批次 87:
  奖励值: 127.0018
  收益率: 0.2592
  距离: 33.0538
  内存使用: 0.6666
  能量使用: 1.0760
  推理时间: 2.3882秒

批次 88:
  奖励值: 139.8029
  收益率: 0.2862
  距离: 32.5279
  内存使用: 0.7712
  能量使用: 1.0949
  推理时间: 2.7386秒

批次 89:
  奖励值: 121.9021
  收益率: 0.2393
  距离: 26.5500
  内存使用: 0.9150
  能量使用: 0.9309
  推理时间: 2.3082秒

批次 90:
  奖励值: 131.3104
  收益率: 0.2646
  距离: 35.0345
  内存使用: 0.7781
  能量使用: 1.1666
  推理时间: 2.5186秒

批次 91:
  奖励值: 135.3951
  收益率: 0.2661
  距离: 34.1659
  内存使用: 0.6522
  能量使用: 0.9915
  推理时间: 2.6037秒

批次 92:
  奖励值: 124.7743
  收益率: 0.2516
  距离: 30.0162
  内存使用: 0.6819
  能量使用: 0.9532
  推理时间: 2.4354秒

批次 93:
  奖励值: 107.2117
  收益率: 0.2164
  距离: 28.6267
  内存使用: 0.9131
  能量使用: 0.8368
  推理时间: 2.2060秒

批次 94:
  奖励值: 129.5993
  收益率: 0.2586
  距离: 30.7348
  内存使用: 0.7035
  能量使用: 0.9631
  推理时间: 2.4703秒

批次 95:
  奖励值: 118.4907
  收益率: 0.2277
  距离: 27.2694
  内存使用: 0.6204
  能量使用: 0.9289
  推理时间: 2.1361秒

批次 96:
  奖励值: 128.8318
  收益率: 0.2552
  距离: 33.7904
  内存使用: 0.7079
  能量使用: 1.0380
  推理时间: 2.4949秒

批次 97:
  奖励值: 130.6776
  收益率: 0.2681
  距离: 35.7341
  内存使用: 0.7140
  能量使用: 0.9965
  推理时间: 2.5053秒

批次 98:
  奖励值: 115.6674
  收益率: 0.2305
  距离: 32.0049
  内存使用: 0.6139
  能量使用: 0.8464
  推理时间: 2.3000秒

批次 99:
  奖励值: 116.1143
  收益率: 0.2316
  距离: 28.1554
  内存使用: 0.9109
  能量使用: 0.9050
  推理时间: 2.4237秒

批次 100:
  奖励值: 124.8254
  收益率: 0.2486
  距离: 30.7247
  内存使用: 0.6247
  能量使用: 0.9998
  推理时间: 2.4325秒


==================== 总结 ====================
平均收益率: 0.2533
平均能量使用: 0.9820
平均推理时间: 2.3605秒
